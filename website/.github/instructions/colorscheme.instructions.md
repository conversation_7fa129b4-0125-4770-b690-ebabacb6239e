---
applyTo: '**'
---

# InterChat Dashboard Color Scheme

This document outlines the comprehensive color scheme and design system used throughout the InterChat dashboard.

## Design System Overview

The InterChat dashboard uses a **rounded design system** with consistent border-radius values and a dark theme optimized for Discord bot management interfaces.

### Border Radius System

```css
:root {
  --radius: 8px;        /* Cards, containers */
  --radius-button: 12px; /* Buttons, inputs, selects */
  --radius-modal: 16px;  /* Modals, dialogs */
}
```

## Core Color Palette

### Background Colors

- **Primary Background**: `bg-gray-950` - Main page background
- **Secondary Background**: `bg-gray-900` - Card backgrounds
- **Tertiary Background**: `bg-gray-800` - Input backgrounds, hover states

### Text Colors

- **Primary Text**: `text-white` - Main headings, important text
- **Secondary Text**: `text-gray-300` - Subheadings, descriptions
- **Muted Text**: `text-gray-400` - Labels, helper text
- **Disabled Text**: `text-gray-500` - Disabled states

### Border Colors

- **Primary Border**: `border-gray-700/50` - Main borders with transparency
- **Secondary Border**: `border-gray-800/50` - Subtle borders
- **Focus Border**: Various accent colors with `/50` opacity

## Premium Card System

### Base Premium Card

```css
.premium-card {
  @apply bg-gradient-to-br from-gray-900/90 to-gray-950/90;
  @apply backdrop-blur-sm border border-gray-700/50;
  @apply rounded-[var(--radius)] shadow-xl;
  @apply hover:shadow-2xl transition-all duration-300;
}
```

### Enhanced Premium Card (for special components)

```css
.premium-card-enhanced {
  @apply bg-gradient-to-br from-gray-900/95 to-gray-950/95;
  @apply backdrop-blur-md border border-gray-600/50;
  @apply rounded-[var(--radius)] shadow-2xl;
  @apply hover:shadow-purple-500/10 hover:border-purple-500/30;
  @apply transition-all duration-300;
}
```

## Accent Colors

### Primary Accents (Purple/Indigo)

- **Purple 400**: `text-purple-400`, `bg-purple-400` - Primary accent
- **Purple 500**: `bg-purple-500/20` - Background tints
- **Indigo 400**: `text-indigo-400` - Secondary accent
- **Indigo 500**: `bg-indigo-500/20` - Background tints

### Status Colors

#### Success/Active (Green/Emerald)

- **Emerald 400**: `text-emerald-400` - Success text
- **Emerald 500**: `bg-emerald-500/20` - Success backgrounds
- **Green 500**: `bg-green-500/20` - Alternative success

#### Warning (Amber/Yellow)

- **Amber 400**: `text-amber-400` - Warning text
- **Amber 500**: `bg-amber-500/20` - Warning backgrounds
- **Yellow 500**: `bg-yellow-500/20` - Alternative warning

#### Error/Danger (Red)

- **Red 400**: `text-red-400` - Error text
- **Red 500**: `bg-red-500/20` - Error backgrounds
- **Red 950**: `from-red-950/30` - Error gradients

#### Info (Blue/Cyan)

- **Blue 400**: `text-blue-400` - Info text
- **Blue 500**: `bg-blue-500/20` - Info backgrounds
- **Cyan 500**: `to-cyan-500/20` - Gradient variations

#### Neutral (Gray)

- **Gray 400**: `text-gray-400` - Neutral text
- **Gray 500**: `bg-gray-500/20` - Neutral backgrounds

## Button Styles

### Primary Buttons

```css
.btn-primary {
  @apply bg-gradient-to-r from-purple-500 to-indigo-500;
  @apply hover:from-purple-600 hover:to-indigo-600;
  @apply text-white border-none rounded-[var(--radius-button)];
  @apply transition-all duration-200;
}
```

### Secondary Buttons

```css
.btn-secondary {
  @apply bg-gray-800/50 hover:bg-gray-700/50;
  @apply border border-gray-700/50 hover:border-gray-600/50;
  @apply text-gray-300 hover:text-white;
  @apply rounded-[var(--radius-button)] transition-all duration-200;
}
```

### Danger Buttons

```css
.btn-danger {
  @apply bg-gradient-to-r from-red-500 to-red-600;
  @apply hover:from-red-600 hover:to-red-700;
  @apply text-white border-none rounded-[var(--radius-button)];
  @apply transition-all duration-200;
}
```

## Input Styles

### Standard Inputs

```css
.input-standard {
  @apply bg-gray-900/50 border-gray-700/50;
  @apply hover:bg-gray-800/50 focus:border-purple-500/50;
  @apply text-white placeholder:text-gray-400;
  @apply rounded-[var(--radius-button)] transition-all duration-200;
}
```

### Select Components

```css
.select-standard {
  @apply bg-gray-900/50 border-gray-700/50;
  @apply hover:bg-gray-800/50 focus:border-purple-500/50;
  @apply rounded-[var(--radius-button)] transition-all duration-200;
}

.select-content {
  @apply bg-gray-900 border-gray-700/50;
  @apply rounded-[var(--radius-button)];
}
```

## Gradient Patterns

### Background Gradients

- **Primary**: `bg-gradient-to-br from-gray-900/90 to-gray-950/90`
- **Enhanced**: `bg-gradient-to-br from-gray-900/95 to-gray-950/95`
- **Subtle**: `bg-gradient-to-b from-gray-900/80 to-gray-950/80`

### Button Gradients

- **Purple-Indigo**: `from-purple-500 to-indigo-500`
- **Emerald-Green**: `from-emerald-500 to-green-500`
- **Red**: `from-red-500 to-red-600`
- **Blue-Cyan**: `from-blue-500 to-cyan-500`

### Status Gradients

- **Success**: `from-emerald-950/30 to-emerald-900/20`
- **Warning**: `from-amber-950/30 to-amber-900/20`
- **Error**: `from-red-950/30 to-red-900/20`
- **Info**: `from-blue-950/30 to-blue-900/20`

## Animation & Transitions

### Standard Transitions

```css
.transition-standard {
  @apply transition-all duration-200 ease-in-out;
}

.transition-slow {
  @apply transition-all duration-300 ease-in-out;
}
```

### Hover Effects

```css
.hover-scale {
  @apply hover:scale-[1.02] transition-transform duration-300;
}

.hover-glow {
  @apply hover:shadow-purple-500/10 hover:border-purple-500/30;
  @apply transition-all duration-300;
}
```

## Usage Guidelines

### When to Use Each Color

1. **Purple/Indigo**: Primary actions, main branding elements
2. **Emerald/Green**: Success states, active status, positive actions
3. **Amber/Yellow**: Warnings, pending states, caution indicators
4. **Red**: Errors, destructive actions, critical alerts
5. **Blue/Cyan**: Information, secondary actions, neutral highlights
6. **Gray**: Disabled states, subtle elements, neutral content

### Accessibility Considerations

- All text maintains WCAG AA contrast ratios
- Focus states are clearly visible with accent colors
- Color is never the only indicator of state or meaning
- Hover states provide clear visual feedback

### Consistency Rules

1. Always use CSS custom properties for border-radius
2. Maintain consistent opacity levels (20% for backgrounds, 50% for borders)
3. Use gradients for enhanced visual hierarchy
4. Apply transitions to all interactive elements
5. Maintain consistent spacing and padding patterns

## Component-Specific Applications

### Stats Cards

- Use premium card base with hover scale effects
- Color-code metrics with appropriate accent colors
- Include icon backgrounds with matching color themes

### Forms

- Apply consistent input styling across all form elements
- Use proper label associations and focus states
- Implement validation states with appropriate colors

### Navigation

- Maintain consistent active/inactive states
- Use subtle hover effects for better UX
- Apply proper contrast for accessibility

This color scheme ensures a cohesive, professional appearance across the entire InterChat dashboard while maintaining excellent usability and accessibility standards.
