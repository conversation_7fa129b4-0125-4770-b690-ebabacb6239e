import { Mail, Settings, User } from 'lucide-react';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import Image from 'next/image';
import { redirect } from 'next/navigation';
import { auth } from '@/auth';
import { UserSettingsForm } from '@/components/dashboard/settings/user-settings-form';
import { Button } from '@/components/ui/button';
import { db } from '@/lib/prisma';

export const metadata: Metadata = {
  title: 'Settings | InterChat Dashboard',
  description: 'Manage your settings and preferences on InterChat',
};

export default async function SettingsPage() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect(`/login?callbackUrl=/${window.location.pathname}`);
  }

  // Fetch user data from database to get current settings
  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      mentionOnReply: true,
      locale: true,
      showNsfwHubs: true,
      email: true,
    },
  });

  if (!user?.email) {
    return (
      <div className="mx-auto max-w-4xl p-6">
        <div className="rounded-xl border border-amber-500/20 bg-gradient-to-br from-amber-950/30 to-amber-900/20 p-6 text-center">
          <Mail className="mx-auto mb-4 h-12 w-12 text-amber-400" />
          <h1 className="mb-2 font-bold text-2xl text-white">Email Required</h1>
          <p className="mb-4 text-amber-200">
            You need to link your email to access all settings.
          </p>
          <a
            href="/login"
            className="inline-flex items-center gap-2 rounded-xl bg-gradient-to-r from-amber-500 to-amber-600 px-6 py-3 font-medium text-white transition-all duration-200 hover:from-amber-600 hover:to-amber-700"
          >
            <Mail className="h-4 w-4" />
            Sign In with Discord
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl space-y-8 p-6">
      {/* Header */}
      <div className="mb-8 text-center">
        <h1 className="mb-2 font-bold text-3xl text-white">Account Settings</h1>
        <p className="text-gray-400">Manage your profile and preferences</p>
      </div>

      {/* Quick Stats/Info Bar */}
      <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="rounded-xl border border-gray-700/50 bg-gradient-to-br from-gray-900/90 to-gray-950/90 p-4 text-center backdrop-blur-sm">
          <User className="mx-auto mb-2 h-6 w-6 text-purple-400" />
          <p className="text-gray-400 text-sm">Profile Status</p>
          <p className="font-medium text-white">Active</p>
        </div>
        <div className="rounded-xl border border-gray-700/50 bg-gradient-to-br from-gray-900/90 to-gray-950/90 p-4 text-center backdrop-blur-sm">
          <Settings className="mx-auto mb-2 h-6 w-6 text-blue-400" />
          <p className="text-gray-400 text-sm">Settings</p>
          <p className="font-medium text-white">Available</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto max-w-2xl space-y-6">
        {/* Profile Section */}
        <div className="rounded-xl border border-gray-700/50 bg-gradient-to-br from-gray-900/90 to-gray-950/90 p-6 backdrop-blur-sm">
          <div className="mb-4 flex items-center gap-2">
            <User className="h-5 w-5 text-purple-400" />
            <h2 className="font-semibold text-white text-xl">Profile</h2>
          </div>

          <div className="mb-4 flex items-center gap-4">
            <div className="relative">
              <Image
                src={
                  session.user.image ||
                  'https://api.dicebear.com/7.x/shapes/svg?seed=user'
                }
                alt={session.user.name || 'User'}
                width={64}
                height={64}
                className="rounded-full border-2 border-purple-500/30"
              />
              <div className="-bottom-1 -right-1 absolute h-4 w-4 rounded-full border-2 border-gray-900 bg-emerald-500"></div>
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-lg text-white">
                {session.user.name}
              </h3>
              <p className="mt-1 inline-block rounded bg-gray-800/50 px-2 py-1 font-mono text-gray-400 text-xs">
                ID: {session.user.id}
              </p>
            </div>
          </div>

          <div className="rounded-lg border border-blue-500/20 bg-blue-950/20 p-3">
            <p className="text-blue-200 text-sm">
              <strong>Note:</strong> Profile info is synced from Discord. Update
              your avatar and username there.
            </p>
          </div>
        </div>

        {/* Account Preferences */}
        <div className="rounded-xl border border-gray-700/50 bg-gradient-to-br from-gray-900/90 to-gray-950/90 p-6 backdrop-blur-sm">
          <div className="mb-4 flex items-center gap-2">
            <Settings className="h-5 w-5 text-indigo-400" />
            <h2 className="font-semibold text-white text-xl">Preferences</h2>
          </div>

          <UserSettingsForm />
        </div>
      </div>

      {/* Quick Actions Footer */}
      <div className="rounded-xl border border-gray-700/30 bg-gradient-to-r from-gray-900/50 to-gray-800/50 p-4">
        <div className="flex flex-wrap items-center justify-center gap-4">
          <Button className="inline-flex items-center gap-2 rounded-lg border border-gray-600/50 bg-gray-800/50 px-4 py-2 text-gray-300 text-sm transition-all duration-200 hover:border-gray-500/50 hover:bg-gray-700/50 hover:text-white">
            <Settings className="h-4 w-4" />
            Advanced Settings
          </Button>
        </div>
      </div>
    </div>
  );
}
