'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { signIn } from '@/lib/auth-client';

export default function LoginPage() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';

  useEffect(() => {
    // Automatically trigger Discord OAuth on page load
    signIn.social({
      provider: 'discord',
      callbackURL: callbackUrl,
    });
  }, [callbackUrl]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="mx-auto max-w-sm space-y-6 p-6 text-center">
        <div className="space-y-2">
          <h1 className="font-bold text-3xl">Redirecting to Discord...</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Please wait while we redirect you to Discord for authentication.
          </p>
        </div>
        <div className="mx-auto h-8 w-8 animate-spin rounded-full border-purple-500 border-b-2"></div>
      </div>
    </div>
  );
}
