import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { db } from '@/lib/prisma';

export const auth = betterAuth({
  database: prismaAdapter(db, {
    provider: 'postgresql',
  }),
  user: { modelName: 'User' },
  account: { modelName: 'Account' },
  session: {
    modelName: 'Session',
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day (extend session)
  },
  verification: { modelName: 'Verification' },
  socialProviders: {
    discord: {
      clientId: process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID!,
      clientSecret: process.env.DISCORD_CLIENT_SECRET!,
      scope: ['identify', 'email', 'guilds'],
    },
  },
});
