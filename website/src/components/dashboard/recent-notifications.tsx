'use client';

import { useQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import {
  AlertTriangle,
  Bell,
  CheckCircle,
  ExternalLink,
  Info,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { useInView } from 'react-intersection-observer';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useTRPC } from '@/utils/trpc';

export function RecentNotifications() {
  const trpc = useTRPC();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Fetch recent announcements
  const { data: announcements, isLoading } = useQuery(
    trpc.announcement.getAnnouncements.queryOptions(undefined, {
      staleTime: 60 * 1000, // 1 minute stale time
    })
  );

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <Info className="h-4 w-4 text-blue-400" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      default:
        return <Bell className="h-4 w-4 text-gray-400" />;
    }
  };

  const getNotificationBorderColor = (type: string) => {
    switch (type) {
      case 'info':
        return 'border-blue-500/20 hover:border-blue-500/30';
      case 'warning':
        return 'border-yellow-500/20 hover:border-yellow-500/30';
      case 'success':
        return 'border-green-500/20 hover:border-green-500/30';
      default:
        return 'border-gray-500/20 hover:border-gray-500/30';
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? 'show' : 'hidden'}
      variants={container}
      className="h-full"
    >
      <Card className="h-full border-gray-800 bg-gradient-to-b from-gray-900/50 to-gray-900/30 backdrop-blur-sm">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-indigo-400" />
            <div>
              <CardTitle className="font-bold text-xl">
                Recent Updates
              </CardTitle>
              <CardDescription>Latest announcements</CardDescription>
            </div>
          </div>
          <Link
            href="/dashboard/announcements"
            className="flex items-center gap-1 text-primary text-xs hover:underline"
          >
            View all
            <ExternalLink className="h-3 w-3" />
          </Link>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, i) => (
                <motion.div
                  key={`notification-skeleton-${i + 1}`}
                  variants={item}
                >
                  <div className="flex items-start space-x-3 rounded-md border border-gray-800/50 p-3">
                    <div className="h-4 w-4 animate-pulse rounded bg-gray-700" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 animate-pulse rounded bg-gray-700" />
                      <div className="h-3 w-2/3 animate-pulse rounded bg-gray-800" />
                    </div>
                  </div>
                </motion.div>
              ))
            ) : announcements && announcements.announcements.length > 0 ? (
              announcements.announcements.slice(0, 4).map((announcement) => {
                const timeAgo = formatDistanceToNow(
                  new Date(announcement.createdAt),
                  { addSuffix: true }
                );

                return (
                  <motion.div key={announcement.id} variants={item}>
                    <div
                      className={`flex items-start space-x-3 rounded-md border p-3 transition-colors duration-200 ${getNotificationBorderColor('info')}`}
                    >
                      <div className="mt-0.5">
                        {getNotificationIcon('info')}
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="font-medium text-sm text-white">
                          {announcement.title}
                        </p>
                        <p className="line-clamp-2 text-gray-400 text-xs">
                          {announcement.content}
                        </p>
                        <p className="text-gray-500 text-xs">{timeAgo}</p>
                      </div>
                    </div>
                  </motion.div>
                );
              })
            ) : (
              <motion.div variants={item} className="py-8 text-center">
                <Bell className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <p className="mb-2 text-gray-300 text-lg">No recent updates</p>
                <p className="text-gray-400 text-sm">
                  You&apos;re all caught up! Check back later for new
                  announcements.
                </p>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
