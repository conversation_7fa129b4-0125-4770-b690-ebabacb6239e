services:
  # redis yet not redis (its better)
  dragonfly:
    image: "docker.dragonflydb.io/dragonflydb/dragonfly"
    ulimits:
      memlock: -1
    # ports:
    #   - "6379:6379"
    # For better performance, consider `host` mode instead `port` to avoid docker NAT.
    # `host` mode is NOT currently supported in Swarm Mode.
    # https://docs.docker.com/compose/compose-file/compose-file-v3/#network_mode
    network_mode: "host"
    volumes:
      - dragonflydata:/data

  # who doesnt like big stonks
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ../../prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--web.enable-lifecycle"
    ports:
      - "9090:9090"
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # big goth girl blocker
  safe-content-ai:
    image: steelcityamir/safe-content-ai:latest
    ports:
      - "8000:8000"

volumes:
  dragonflydata:
