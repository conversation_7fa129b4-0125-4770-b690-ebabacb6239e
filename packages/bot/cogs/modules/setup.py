from datetime import datetime
import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from discord.ui import View, select, Select, button, Button, TextInput

import re

from db.models import Hub
from utils.modules.core.checks import interaction_check
from utils.modules.ui.Localization import create_embed
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.core.i18n import t
from utils.utils import load_user_locale, check_user

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class CreateView(View):
    def __init__(self, bot: 'Bot', locale, user):
        super().__init__(timeout=300)
        self.bot = bot
        self.locale = locale
        self.user = user
        self.constants = bot.constants
        self.hub_data: dict[str, str] = {}
        self.back_callback.label = t('ui.setup.buttons.back', locale=self.locale)

    async def update_button(self, interaction: discord.Interaction['Bot']):
        # Update button states based on hub_data
        self.create_callback.disabled = not self.hub_data
        self.create_callback.style = (
            discord.ButtonStyle.secondary if not self.hub_data else discord.ButtonStyle.green
        )

        if self.hub_data:
            self.hub_info_callback.label = t('ui.setup.buttons.hubInfoComplete', locale=self.locale)
            self.hub_info_callback.emoji = self.bot.emotes.tick_icon
            self.hub_info_callback.style = discord.ButtonStyle.secondary

            self.create_callback.label = t('ui.setup.buttons.createHub', locale=self.locale)
            self.create_callback.emoji = self.bot.emotes.wand_icon
        else:
            self.hub_info_callback.label = t('ui.setup.buttons.enterHubInfo', locale=self.locale)
            self.hub_info_callback.emoji = self.bot.emotes.edit_icon
            self.hub_info_callback.style = discord.ButtonStyle.primary
            self.create_callback.label = t('ui.setup.buttons.completeInfoFirst', locale=self.locale)
            self.create_callback.emoji = self.bot.emotes.x_icon

        if interaction.response.is_done():
            await interaction.edit_original_response(view=self)
        else:
            await interaction.response.edit_message(view=self)

    async def on_timeout(self):
        for item in self.children:
            if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                item.disabled = True

    @button(
        label='Back',
        style=discord.ButtonStyle.secondary,
        emoji='⬅️',
        row=1,
    )
    async def back_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        if not await interaction_check(interaction, self.user, interaction.user):
            await interaction.response.defer()
            return

        embed = create_embed(
            self.locale,
            'commands.setup.welcome.title',
            'commands.setup.welcome.description',
        )

        options = [
            discord.SelectOption(
                emoji=str(self.bot.emotes.plus_icon),
                label=t('commands.setup.options.create.label', locale=self.locale),
                description=t('commands.setup.options.create.description', locale=self.locale),
                value='create',
            ),
            discord.SelectOption(
                emoji=str(self.bot.emotes.chat_icon),
                label=t('commands.setup.options.join.label', locale=self.locale),
                description=t('commands.setup.options.join.description', locale=self.locale),
                value='join',
            ),
        ]

        view = SetupView(self.bot, self.locale, self.user, options)
        view.set_options()
        await interaction.response.edit_message(embed=embed, view=view)

    @button(label='ui.setup.buttons.enterHubInfo', style=discord.ButtonStyle.primary, row=0)
    async def hub_info_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        if not await interaction_check(interaction, self.user, interaction.user):
            await interaction.response.defer()
            return

        modal = CustomModal(
            t('ui.common.modal.hubCreation.title', locale=self.locale),
            [
                (
                    'name',
                    TextInput(
                        label=t('ui.common.modal.hubCreation.name.label', locale=self.locale),
                        placeholder=t(
                            'ui.common.modal.hubCreation.name.placeholder', locale=self.locale
                        ),
                        max_length=50,
                        required=True,
                    ),
                ),
                (
                    'short',
                    TextInput(
                        label=t('ui.common.modal.hubCreation.brief.label', locale=self.locale),
                        placeholder=t(
                            'ui.common.modal.hubCreation.brief.placeholder', locale=self.locale
                        ),
                        max_length=140,
                        required=True,
                    ),
                ),
                (
                    'description',
                    TextInput(
                        label=t(
                            'ui.common.modal.hubCreation.description.label', locale=self.locale
                        ),
                        placeholder=t(
                            'ui.common.modal.hubCreation.description.placeholder',
                            locale=self.locale,
                        ),
                        style=discord.TextStyle.paragraph,
                        max_length=1000,
                        required=True,
                    ),
                ),
                (
                    'url',
                    TextInput(
                        label=t('ui.common.modal.hubCreation.logoUrl.label', locale=self.locale),
                        placeholder=t(
                            'ui.common.modal.hubCreation.logoUrl.placeholder', locale=self.locale
                        ),
                        required=True,
                    ),
                ),
            ],
        )

        await interaction.response.send_modal(modal)
        await modal.wait()

        # Extract values from CustomModal's saved_items for type safety
        self.hub_data = {
            'name': modal.saved_items['name'].value,
            'short': modal.saved_items['short'].value,
            'description': modal.saved_items['description'].value,
            'url': modal.saved_items['url'].value,
        }

        preview_embed = discord.Embed(
            title=t(
                'responses.setup.preview.titleSaved',
                locale=await load_user_locale(interaction),
                emoji=self.bot.emotes.tick_icon,
            ),
            description=t(
                'responses.setup.preview.previewTitle',
                locale=await load_user_locale(interaction),
            ),
            color=self.constants.color,
        )
        preview_embed.add_field(
            name=t(
                'responses.setup.preview.name',
                locale=await load_user_locale(interaction),
                emoji=self.bot.emotes.hash_icon,
            ),
            value=self.hub_data['name'],
            inline=True,
        )
        preview_embed.add_field(
            name=t(
                'responses.setup.preview.short',
                locale=await load_user_locale(interaction),
                emoji=self.bot.emotes.chat_icon,
            ),
            value=self.hub_data['short'],
            inline=True,
        )
        preview_embed.add_field(
            name=t(
                'responses.setup.preview.description',
                locale=await load_user_locale(interaction),
                emoji=self.bot.emotes.edit_icon,
            ),
            value=(
                self.hub_data['description'][:150] + '...'
                if len(self.hub_data['description']) > 150
                else self.hub_data['description']
            ),
            inline=False,
        )

        if re.match(
            r'^https?://[^\s]+\.(?:png|jpe?g|gif|webp)$',
            self.hub_data.get('url', ''),
            re.IGNORECASE,
        ):
            preview_embed.set_thumbnail(url=self.hub_data.get('url', ''))
        else:
            self.hub_data['url'] = self.bot.user.display_avatar.url  # pyright: ignore[reportOptionalMemberAccess]
            preview_embed.set_thumbnail(url=self.bot.user.display_avatar.url)  # pyright: ignore[reportOptionalMemberAccess]

        preview_embed.set_footer(
            text=t(
                'responses.setup.setupComplete',
                locale=await load_user_locale(interaction),
            )
        )

        await interaction.followup.send(embed=preview_embed, ephemeral=True)
        await self.update_button(interaction)

    @button(
        label='ui.setup.buttons.completeInfoFirst',
        style=discord.ButtonStyle.secondary,
        row=0,
        disabled=True,
    )
    async def create_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        if not await interaction_check(interaction, self.user, interaction.user):
            return

        loading_embed = discord.Embed(
            title=t(
                'responses.setup.loading.creatingHub',
                locale=self.locale,
                loading=self.bot.emotes.loading,
            ),
            description=t('responses.setup.loading.pleaseWait', locale=self.locale),
            color=discord.Color.orange(),
        )
        await interaction.edit_original_response(embed=loading_embed, view=None)

        try:
            async with self.bot.db.get_session() as session:
                hub = Hub(
                    name=self.hub_data['name'],
                    shortDescription=self.hub_data['short'],
                    description=self.hub_data['description'],
                    ownerId=str(interaction.user.id),
                    iconUrl=self.hub_data['url'],
                    rules=[],
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                    lastActive=datetime.now(),
                )
                session.add(hub)
                await session.commit()

            success_embed = discord.Embed(
                title=t('commands.setup.nextSteps.created.title', locale=self.locale),
                description=t(
                    'commands.setup.nextSteps.created.description',
                    locale=self.locale,
                    hubName=self.hub_data['name'],
                ),
                color=self.constants.color,
            )

            success_embed.add_field(
                name=t('commands.setup.nextSteps.created.inviteLink.title', locale=self.locale),
                value=t(
                    'commands.setup.nextSteps.created.inviteLink.description',
                    locale=self.locale,
                    hubInviteCommand='/hub invite create',
                    hubName=self.hub_data['name'],
                ),
                inline=False,
            )

            success_embed.add_field(
                name=t('commands.setup.nextSteps.created.shareHub.title', locale=self.locale),
                value=t(
                    'commands.setup.nextSteps.created.shareHub.description',
                    locale=self.locale,
                    dot=str(self.bot.emotes.dot),
                    supportInvite=self.constants.support_invite,
                ),
                inline=False,
            )

            success_embed.add_field(
                name=t(
                    'commands.setup.nextSteps.created.proTips.title',
                    locale=self.locale,
                    dot=str(self.bot.emotes.dot),
                ),
                value=t(
                    'commands.setup.nextSteps.created.proTips.description',
                    locale=self.locale,
                    dot=str(self.bot.emotes.dot),
                    website='https://interchat.app',
                    hubVisibilityCommand='/hub configure',
                    supportInvite=self.constants.support_invite,
                ),
                inline=False,
            )

            try:
                success_embed.set_thumbnail(url=self.hub_data['url'])
            except Exception:
                pass

            success_embed.set_footer(
                text=f'Hub created by {interaction.user.display_name}',
                icon_url=interaction.user.display_avatar.url,
            )

            await interaction.edit_original_response(embed=success_embed, view=None)

        except Exception:
            error_embed = discord.Embed(
                title=t(
                    'responses.setup.errors.hubCreationFailed',
                    locale=self.locale,
                    no=self.bot.emotes.no,
                ),
                description=t('responses.setup.errors.hubCreationFailed', locale=self.locale),
                color=discord.Color.red(),
            )
            error_embed.add_field(
                name=t(
                    'commands.hubCreate.errors.troubleshooting',
                    locale=self.locale,
                    gear_icon=self.bot.emotes.gear_icon,
                ),
                value=t('commands.hubCreate.errors.troubleshootingSteps', locale=self.locale),
                inline=False,
            )
            await interaction.edit_original_response(embed=error_embed, view=None)


class SetupView(View):
    def __init__(self, bot, locale, user, options):
        super().__init__(timeout=300)
        self.bot: Bot = bot
        self.user = user
        self.constants = bot.constants
        self.locale = locale
        self.options = options
        self.on_submit.placeholder = t('ui.setup.select.chooseOption', locale=self.locale)

    def set_options(self):
        self.on_submit.options = self.options
        self.on_submit.placeholder = t('ui.setup.select.chooseOption', locale=self.locale)

    async def on_timeout(self):
        for item in self.children:
            if isinstance(item, (discord.ui.Button, discord.ui.Select)):
                item.disabled = True

    @select(
        options=[
            discord.SelectOption(
                label='ui.setup.select.loadingLabel',
                description='ui.setup.select.loadingDescription',
                value='loading',
            )
        ],
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], select: Select):
        if not await interaction_check(interaction, self.user, interaction.user):
            await interaction.response.defer()
            return

        if select.values[0] == 'create':
            embed = discord.Embed(
                title=t(
                    'commands.setup.create.whatYoullCreate.title',
                    locale=self.locale,
                    lightbulb_icon=self.bot.emotes.lightbulb_icon,
                ),
                description=t(
                    'commands.setup.create.whatYoullCreate.description',
                    locale=self.locale,
                    dot=self.bot.emotes.dot,
                ),
                color=self.constants.color,
            )
            embed.add_field(
                name=t(
                    'commands.setup.create.youllNeed.title',
                    locale=self.locale,
                    info_icon=self.bot.emotes.info_icon,
                ),
                value=t(
                    'commands.setup.create.youllNeed.description',
                    locale=self.locale,
                    arrow_right=self.bot.emotes.arrow_right,
                ),
                inline=False,
            )

            embed.set_footer(text=t('ui.common.create.footer.getStarted', locale=self.locale))

            view = CreateView(self.bot, self.locale, self.user)
            await view.update_button(interaction)
            await interaction.response.edit_message(embed=embed, view=view)

        elif select.values[0] == 'join':
            # New "Join a Hub" UI with clear instructions for public and private hubs
            embed = discord.Embed(
                title=t(
                    'commands.setup.join.title',
                    locale=self.locale,
                    chat_icon=self.bot.emotes.chat_icon,
                ),
                description=t('commands.setup.join.description', locale=self.locale),
                color=self.constants.color,
            )

            embed.add_field(
                name=t(
                    'commands.setup.join.publicHubs.title',
                    locale=self.locale,
                    globe_icon=self.bot.emotes.globe_icon,
                ),
                value=t('commands.setup.join.publicHubs.description', locale=self.locale),
                inline=False,
            )

            embed.add_field(
                name=t(
                    'commands.setup.join.privateHubs.title',
                    locale=self.locale,
                    lock_icon=self.bot.emotes.lock_icon,
                ),
                value=t('commands.setup.join.privateHubs.description', locale=self.locale),
                inline=False,
            )

            embed.set_footer(
                text=t('commands.setup.join.footer', locale=self.locale),
            )

            view = View(timeout=300)

            # Link button to hubs directory
            directory_button = discord.ui.Button(
                label=t('ui.setup.buttons.discoverHubs', locale=self.locale),
                style=discord.ButtonStyle.link,
                url='https://interchat.tech/hubs',
                emoji=self.bot.emotes.search_icon,
                row=0,
            )
            support_button = discord.ui.Button(
                label=t('ui.setup.buttons.supportServer', locale=self.locale),
                style=discord.ButtonStyle.link,
                url=self.constants.support_invite,
                emoji=self.bot.emotes.question_icon,
                row=0,
            )
            view.add_item(directory_button)
            view.add_item(support_button)

            # Back button to return to main setup menu
            back_button = discord.ui.Button(
                label=t('ui.setup.buttons.back', locale=self.locale),
                style=discord.ButtonStyle.secondary,
                emoji='⬅️',
                row=1,
            )

            async def back_button_callback(interaction: discord.Interaction['Bot']):
                if not await interaction_check(interaction, self.user, interaction.user):
                    await interaction.response.defer()
                    return

                main_embed = create_embed(
                    self.locale,
                    'commands.setup.welcome.title',
                    'commands.setup.welcome.description',
                )
                main_view = SetupView(self.bot, self.locale, self.user, self.options)
                main_view.set_options()
                await interaction.response.edit_message(embed=main_embed, view=main_view)

            back_button.callback = back_button_callback
            view.add_item(back_button)
            await interaction.response.edit_message(embed=embed, view=view)


class Setup(CogBase):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = bot.constants

    @commands.hybrid_command(
        name='setup',
        description='Setup InterChat for your server',
        extras={'category': 'Server'},
    )
    @check_user()
    async def setup(self, ctx: commands.Context):
        locale = await self.get_locale(ctx)

        embed = create_embed(
            locale, 'commands.setup.welcome.title', 'commands.setup.welcome.description'
        )

        guild_name = (
            getattr(ctx.guild, 'name', 'this server')
            if getattr(ctx, 'guild', None)
            else 'this server'
        )
        guild_icon = None
        if getattr(ctx, 'guild', None):
            guild_icon_obj = getattr(ctx.guild, 'icon', None)
            guild_icon = guild_icon_obj.url if guild_icon_obj else None

        embed.set_footer(
            text=f'InterChat Setup • {guild_name}',
            icon_url=guild_icon,
        )

        options = [
            discord.SelectOption(
                emoji=str(self.bot.emotes.plus_icon),
                label='Create a Hub',
                description='Start your own InterChat community',
                value='create',
            ),
            discord.SelectOption(
                emoji=str(self.bot.emotes.chat_icon),
                label='Join a Hub',
                description='Connect to an existing community',
                value='join',
            ),
        ]

        view = SetupView(self.bot, locale, ctx.author, options)
        view.set_options()
        await ctx.send(embed=embed, view=view, ephemeral=True)


async def setup(bot):
    await bot.add_cog(Setup(bot))
