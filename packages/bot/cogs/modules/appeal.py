import locale
from typing import TYPE_CHECKING, List, Optional, TypedDict

import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from sqlmodel import and_, or_, select, col
from sqlalchemy.orm import selectinload
from sqlalchemy import desc

from utils.constants import logger
from utils.modules.core.i18n import t
from datetime import datetime, timedelta
from db.models import (
    Infraction,
    InfractionStatus,
    InfractionType,
    Appeal,
    AppealStatus,
    User,
)
from utils.modules.events.eventDispatcher import create_hub_event, HubEventType, event_dispatcher

if TYPE_CHECKING:
    from main import Bot

# Character limits (Discord modal text inputs).
LIMIT_Q1 = 300
LIMIT_Q2 = 300
LIMIT_Q3 = 300


class AppealData(TypedDict):
    infraction: Infraction
    has_pending_appeal: bool
    has_any_appeal: bool
    cooldown_end: Optional[datetime]
    can_appeal_again: bool


class AppealCompositeView(discord.ui.View):
    """Composite view that handles both appeal selection and pagination."""

    def __init__(
        self,
        bot: 'Bot',
        appealable_infractions: List[Infraction],
        infraction_data: List[AppealData],
        user_id: str,
        locale: str,
        build_embed_func,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.appealable_infractions = appealable_infractions
        self.infraction_data = infraction_data
        self.user_id = user_id  # stored as string for consistency with DB
        self.locale = locale
        self.build_embed_func = build_embed_func

        # Pagination state
        self.page = 0
        self.per_page = 5
        self.total = len(infraction_data)

        # Track the select menu component for updates
        self.select_menu = None

        # Build initial view
        self._build_view()

    def _build_view(self):
        """Build the entire view with current page's select menu and pagination."""
        # Clear existing items
        self.clear_items()

        # Add select menu for current page
        self._add_select_menu()

        # Add pagination buttons if needed
        if len(self.infraction_data) > self.per_page:
            self._add_pagination_buttons()

    def _get_current_page_appealable_infractions(self) -> List[Infraction]:
        """Get appealable infractions for the current page only."""
        start = self.page * self.per_page
        end = start + self.per_page
        current_page_data = self.infraction_data[start:end]

        # Filter to only appealable infractions on this page
        page_appealable = []
        for data in current_page_data:
            if not data['has_pending_appeal'] and data['can_appeal_again']:
                page_appealable.append(data['infraction'])

        return page_appealable

    def _add_select_menu(self):
        """Add the appeal selection dropdown for current page infractions only."""
        current_page_appealable = self._get_current_page_appealable_infractions()

        if not current_page_appealable:
            return

        options: list[discord.SelectOption] = []
        for inf in current_page_appealable[:25]:
            hub_name = (
                inf.hub.name
                if inf.hub
                else t('responses.appeal.constants.unknownHub', locale=self.locale)
            )
            infraction_type = inf.type.name.title()
            created_date = inf.createdAt.strftime('%Y-%m-%d')

            description = f'{hub_name} • {created_date}'
            if inf.reason and len(inf.reason) > 50:
                description += f' • {inf.reason[:47]}...'
            elif inf.reason:
                description += f' • {inf.reason}'

            options.append(
                discord.SelectOption(
                    label=f'{infraction_type} in {hub_name}',
                    description=description,
                    value=inf.id,
                    emoji=self.bot.emotes.hammer_icon
                    if inf.type == InfractionType.BAN
                    else self.bot.emotes.alert_icon,
                )
            )

        if options:
            self.select_menu = discord.ui.Select(
                placeholder=t('ui.appeal.select.placeholder', locale=self.locale),
                options=options,
                custom_id='appeal_select',
                min_values=1,
                max_values=1,
            )

            async def _callback(interaction: discord.Interaction['Bot']):
                logger.debug(
                    'AppealCompositeView select interaction received: user=%s component_id=%s values=%s',
                    interaction.user.id,
                    interaction.data.get('custom_id') if interaction.data else 'N/A',
                    interaction.data.get('values', []) if interaction.data else [],
                )

                if interaction.user.id != int(self.user_id):
                    await interaction.response.send_message(
                        t(
                            'ui.appeal.errors.notYourMenu',
                            locale=self.locale,
                            x_icon=self.bot.emotes.x_icon,
                        ),
                        ephemeral=True,
                    )
                    return

                # Get values from interaction data
                values = interaction.data.get('values', []) if interaction.data else []
                if not values:
                    await interaction.response.send_message(
                        t(
                            'ui.appeal.errors.nothingSelected',
                            locale=self.locale,
                            x_icon=self.bot.emotes.x_icon,
                        ),
                        ephemeral=True,
                    )
                    return

                selected_id = values[0]
                selected_infraction = next(
                    (inf for inf in current_page_appealable if inf.id == selected_id), None
                )
                if not selected_infraction:
                    await interaction.response.send_message(
                        t(
                            'ui.appeal.errors.invalidSelection',
                            locale=self.locale,
                            x_icon=self.bot.emotes.x_icon,
                        ),
                        ephemeral=True,
                    )
                    return

                try:
                    modal = AppealModal(self.bot, selected_infraction, self.locale)
                    await interaction.response.send_modal(modal)
                except Exception as e:
                    logger.error('Failed to send appeal modal: %s', e)
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            t(
                                'ui.appeal.errors.modalError',
                                locale=self.locale,
                                x_icon=self.bot.emotes.x_icon,
                            ),
                            ephemeral=True,
                        )

            self.select_menu.callback = _callback
            self.add_item(self.select_menu)

    def _add_pagination_buttons(self):
        """Add pagination buttons to the view."""
        self.prev_button = discord.ui.Button(
            label=t('ui.appeal.buttons.previous', locale=self.locale),
            style=discord.ButtonStyle.secondary,
            emoji=self.bot.emotes.back,
        )
        self.next_button = discord.ui.Button(
            label=t('ui.appeal.buttons.next', locale=self.locale),
            style=discord.ButtonStyle.secondary,
            emoji=self.bot.emotes.next,
        )
        self.page_button = discord.ui.Button(
            label=self._page_label(), style=discord.ButtonStyle.gray, disabled=True
        )

        self.prev_button.callback = self.on_prev
        self.next_button.callback = self.on_next

        self.add_item(self.prev_button)
        self.add_item(self.page_button)
        self.add_item(self.next_button)
        self._update_pagination_buttons()

    def _page_label(self) -> str:
        last_page = max(0, (self.total - 1) // self.per_page)
        return f'Page {self.page + 1}/{last_page + 1 if self.total else 1}'

    def _update_pagination_buttons(self):
        """Update the state of pagination buttons."""
        if not hasattr(self, 'prev_button'):
            return

        last_page = max(0, (self.total - 1) // self.per_page)
        self.prev_button.disabled = self.page <= 0
        self.next_button.disabled = self.page >= last_page
        self.page_button.label = self._page_label()

    async def _edit_message(self, interaction: discord.Interaction):
        """Edit the message with updated embed and rebuild the view for current page."""
        if interaction.user.id != int(self.user_id):
            await interaction.response.send_message(
                t(
                    'ui.appeal.errors.cannotControl',
                    locale=self.locale,
                    x_icon=self.bot.emotes.x_icon,
                ),
                ephemeral=True,
            )
            return

        # Rebuild the entire view for the current page
        self._build_view()

        # Update embed for current page
        embed = await self.build_embed_func(self.page, self.infraction_data, self.locale)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_prev(self, interaction: discord.Interaction):
        """Handle previous page button click."""
        if self.page > 0:
            self.page -= 1
        await self._edit_message(interaction)

    async def on_next(self, interaction: discord.Interaction):
        """Handle next page button click."""
        last_page = max(0, (self.total - 1) // self.per_page)
        if self.page < last_page:
            self.page += 1
        await self._edit_message(interaction)


class AppealModal(discord.ui.Modal):
    def __init__(self, bot: 'Bot', infraction: Infraction, locale: str):
        self.locale = locale
        super().__init__(title=t('ui.appeal.modal.title', locale=self.locale))
        self.bot = bot
        self.infraction = infraction

        self.q1 = discord.ui.TextInput(
            label=t('ui.appeal.modal.q1', locale=self.locale),
            style=discord.TextStyle.paragraph,
            max_length=LIMIT_Q1,
            required=True,
        )
        self.q2 = discord.ui.TextInput(
            label=t('ui.appeal.modal.q2', locale=self.locale),
            style=discord.TextStyle.paragraph,
            max_length=LIMIT_Q2,
            required=True,
        )
        self.q3 = discord.ui.TextInput(
            label=t('ui.appeal.modal.q3', locale=self.locale),
            style=discord.TextStyle.paragraph,
            max_length=LIMIT_Q3,
            required=False,
        )
        self.add_item(self.q1)
        self.add_item(self.q2)
        self.add_item(self.q3)

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        reason_combined = t('ui.appeal.modal.q1', locale=self.locale) + f'\n{self.q1.value}\n\n'
        reason_combined += t('ui.appeal.modal.q2', locale=self.locale) + f'\n{self.q2.value}\n\n'
        reason_combined += t('ui.appeal.modal.q3', locale=self.locale) + f'\n{self.q3.value}\n\n'

        async with self.bot.db.get_session() as session:
            # Refresh infraction to ensure still active and appealable
            stmt = (
                select(Infraction)
                .where(Infraction.id == self.infraction.id)
                .options(selectinload(getattr(Infraction, 'hub')))
            ).limit(1)
            inf = (await session.exec(stmt)).first()
            if not inf or (inf.expiresAt is not None and inf.expiresAt < datetime.now()):
                await interaction.response.send_message(
                    t(
                        'commands.appeal.notAppealable',
                        locale=self.locale,
                        x_icon=self.bot.emotes.x_icon,
                    ),
                    ephemeral=True,
                )
                return

            # Fetch or create user model
            user_stmt = select(User).where(User.id == str(interaction.user.id))
            user_model = (await session.exec(user_stmt)).first()
            if not user_model:
                await interaction.response.send_message(
                    f'{self.bot.emotes.x_icon} Internal error: user record missing.', ephemeral=True
                )
                return

            # Create appeal record
            appeal = Appeal(
                infractionId=inf.id,
                userId=str(interaction.user.id),
                reason=reason_combined,
                infraction=inf,
                user=user_model,
            )
            session.add(appeal)

            # Update infraction status
            inf.status = InfractionStatus.APPEALED
            await session.commit()

            # Dispatch appeal-submitted event for logging
            hub_name = inf.hub.name if inf.hub else 'Unknown Hub'
            event = create_hub_event(
                event_type=HubEventType.APPEAL_SUBMITTED,
                hub_id=str(inf.hubId),
                hub_name=str(hub_name),
                moderator_id=str(interaction.user.id),  # appellant
                moderator_name=str(interaction.user),
                reason=reason_combined,
                appeal_id=appeal.id,
            )
            await event_dispatcher.dispatch_hub_event(event)

        embed = discord.Embed(
            title=t('ui.common.titles.success', self.locale),
            description=t(
                'ui.appeal.success.submitted', locale=self.locale, tick=self.bot.emotes.tick
            ),
            color=discord.Color.green(),
        )
        await interaction.edit_original_response(embed=embed, view=None)


class AppealCog(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = bot.constants
        self.locale = locale

    async def get_user_infractions_with_appeals(self, user_id: str) -> List[AppealData]:
        """Get user's appealable infractions with appeal status and cooldown info."""
        async with self.bot.db.get_session() as session:
            # Get all active ban/mute infractions for user
            stmt = (
                select(Infraction)
                .where(
                    and_(
                        Infraction.userId == user_id,
                        or_(
                            Infraction.status == InfractionStatus.ACTIVE,
                        ),
                        or_(
                            Infraction.type == InfractionType.BAN,
                            Infraction.type == InfractionType.BLACKLIST,
                            Infraction.type == InfractionType.MUTE,
                        ),
                        or_(
                            col(Infraction.expiresAt).is_(None),
                            col(Infraction.expiresAt) > datetime.now(),
                        ),
                    )
                )
                .options(
                    selectinload(getattr(Infraction, 'hub')),
                    selectinload(getattr(Infraction, 'appeals')),
                )
                .order_by(desc(col(Infraction.createdAt)))
            )
            result = await session.exec(stmt)
            infractions = list(result.all())

            infraction_data = []
            for inf in infractions:
                # Check if already appealed
                has_pending_appeal = any(
                    appeal.status == AppealStatus.PENDING for appeal in inf.appeals
                )
                has_any_appeal = len(inf.appeals) > 0

                # Calculate cooldown
                cooldown_end = None
                can_appeal_again = True
                if has_any_appeal:
                    # Find most recent appeal
                    latest_appeal = max(inf.appeals, key=lambda a: a.createdAt)
                    hub_cooldown_hours = inf.hub.appealCooldownHours
                    cooldown_end = latest_appeal.createdAt + timedelta(hours=hub_cooldown_hours)
                    can_appeal_again = datetime.now() >= cooldown_end

                infraction_data.append(
                    {
                        'infraction': inf,
                        'has_pending_appeal': has_pending_appeal,
                        'has_any_appeal': has_any_appeal,
                        'cooldown_end': cooldown_end,
                        'can_appeal_again': can_appeal_again,
                    }
                )

            return infraction_data

    async def build_appeals_embed(
        self,
        page: int,
        infraction_data: List[AppealData],
        user_locale: str = 'en',
    ) -> discord.Embed:
        """Build embed showing user's appealable infractions."""
        per_page = 5
        start = page * per_page
        end = start + per_page
        page_data = infraction_data[start:end]

        embed = discord.Embed(
            color=self.bot.constants.color,
            title=f'{t("commands.appeal.title", locale=user_locale)}\n',
            description=(f'{t("commands.appeal.description", locale=user_locale)}'),
        )

        if not page_data:
            embed.add_field(
                name=f'{self.bot.emotes.tick} {t("commands.appeal.noInfractions.title", locale=user_locale)}',
                value=t('commands.appeal.noInfractions.description', locale=user_locale),
                inline=False,
            )
            return embed

        for data in page_data:
            inf = data['infraction']
            hub_name = (
                inf.hub.name
                if inf.hub
                else t('responses.appeal.constants.unknownHub', locale=user_locale)
            )
            infraction_type = inf.type.name.title()

            # Status indicators
            status_text = ''
            if data['has_pending_appeal']:
                status_text = f'> -# {self.bot.emotes.clock_icon} **{t("responses.appeal.status.pending", locale=user_locale)}**'
            elif data['has_any_appeal'] and not data['can_appeal_again']:
                cooldown_timestamp = (
                    int(data['cooldown_end'].timestamp()) if data['cooldown_end'] else 0
                )
                status_text = f'> -# {self.bot.emotes.cross} **{t("responses.appeal.status.cooldown", locale=user_locale)}** until <t:{cooldown_timestamp}:R>'
            elif data['has_any_appeal'] and data['can_appeal_again']:
                status_text = f'> -# {self.bot.emotes.tick} **{t("responses.appeal.status.canAppealAgain", locale=user_locale)}**'
            else:
                status_text = f'> -# {self.bot.emotes.tick} **{t("responses.appeal.status.canAppeal", locale=user_locale)}**'

            # Reason and date
            if inf.reason and len(inf.reason) <= 100:
                reason = inf.reason
            elif inf.reason:
                reason = f'{inf.reason[:97]}...'
            else:
                reason = t('responses.appeal.constants.noReason', locale=user_locale)
            created_date = f'<t:{int(inf.createdAt.timestamp())}:d>'

            field_value = f'\n> {self.bot.emotes.clock_icon} **{t("responses.appeal.fields.date", locale=user_locale)}** {created_date}\n> {self.bot.emotes.wiki_icon} **{t("responses.appeal.fields.reason", locale=user_locale)}** {reason}\n{status_text}'

            embed.add_field(
                name=f'{self.bot.emotes.hammer_icon if inf.type == InfractionType.BAN else self.bot.emotes.alert_icon} {hub_name} — {infraction_type}',
                value=field_value,
                inline=False,
            )

        # Footer with instructions
        appealable_count = sum(
            1
            for data in infraction_data
            if not data['has_pending_appeal'] and data['can_appeal_again']
        )
        if appealable_count > 0:
            embed.set_footer(
                text=t(
                    'responses.appeal.embed.footer.canAppeal',
                    locale=user_locale,
                    count=appealable_count,
                )
            )
        else:
            embed.set_footer(text=t('responses.appeal.embed.footer.checkLater', locale=user_locale))

        return embed

    @commands.hybrid_command()
    async def appeal(self, ctx: commands.Context['Bot']):
        await ctx.defer(ephemeral=True)
        logger.debug('Appeal command invoked by user_id=%s', ctx.author.id)

        # Get user's locale
        user_locale = await self.get_locale(ctx)
        self.locale = user_locale
        # Get user's infraction data
        infraction_data = await self.get_user_infractions_with_appeals(str(ctx.author.id))

        if not infraction_data:
            embed = discord.Embed(
                title=t('responses.appeal.embed.noInfractions.title', locale=user_locale),
                description=f'{self.bot.emotes.tick} {t("responses.appeal.embed.noInfractions.description", locale=user_locale)}',
                color=discord.Color.green(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        # Get appealable infractions (for select menu)
        appealable_infractions = [
            data['infraction']
            for data in infraction_data
            if not data['has_pending_appeal'] and data['can_appeal_again']
        ]

        # Build initial embed
        embed = await self.build_appeals_embed(0, infraction_data, user_locale)

        # Build composite view that handles both selection and pagination
        view = AppealCompositeView(
            bot=self.bot,
            appealable_infractions=appealable_infractions,
            infraction_data=infraction_data,
            user_id=str(ctx.author.id),
            locale=self.locale,
            build_embed_func=self.build_appeals_embed,
        )

        logger.debug(
            'Sending appeals embed: user_id=%s appealable=%d total=%d paginator=%s',
            ctx.author.id,
            len(appealable_infractions),
            len(infraction_data),
            'yes' if len(infraction_data) > 5 else 'no',
        )
        await ctx.send(embed=embed, view=view, ephemeral=True)


async def setup(bot: 'Bot'):
    await bot.add_cog(AppealCog(bot))
