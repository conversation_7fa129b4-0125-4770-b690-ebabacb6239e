from typing import TYPE_CHECKING

import discord
from discord import app_commands
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from sqlmodel import or_, select, col
from sqlalchemy.orm import selectinload
from sqlalchemy import desc
from datetime import datetime

from utils.modules.core.checks import is_interchat_staff
from db.models import DevAlerts, User, Hub, HubModerator
from utils.modules.services.userService import UserService
from utils.modules.ui.HtmlGeneration import generate_leaderboard, generate_profile
from utils.modules.core.i18n import t
from utils.modules.ui.views.UserCommandViews import PreferencesView
from utils.utils import (
    check_user,
    fetch_achievements,
    parse_discord_emoji,
)

if TYPE_CHECKING:
    from main import Bot
    from utils.interfaces import InterChatBadge


class UserCog(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot: Bot = bot
        self.constants = bot.constants
        self.usersvc: 'UserService'
        self.bot.loop.create_task(self.setup())

    async def setup(self):
        async with self.bot.db.get_session() as session:
            self.usersvc = UserService(session)

    @commands.hybrid_command(
        name='profile',
        description='View a users InterChat profile',
        extras={'category': 'General'},
    )
    @check_user()
    async def profile(self, ctx: commands.Context['Bot'], user: discord.User = None):  # pyright: ignore[reportArgumentType]
        await ctx.defer()

        achievements = []

        if user is None:
            user = ctx.author

        locale = await self.get_locale(ctx)
        def_achievements = [
            {
                'icon': parse_discord_emoji(self.bot.emotes.x_icon),
                'title': t('commands.profile.achievements.noneFound', locale=locale),
                'description': t(
                    'commands.profile.achievements.noneFoundDescription', locale=locale
                ),
            }
        ]

        def_badges: list['InterChatBadge'] = [
            {
                'icon': parse_discord_emoji(self.bot.emotes.x_icon),
                'title': t('commands.profile.badges.noneFound', locale=locale),
                'description': t('commands.profile.badges.noneFoundDescription', locale=locale),
            }
        ]

        achievements = await fetch_achievements(self.bot, user, 6)
        _, badges = await self.usersvc.fetch_badges(
            self.bot, str(user.id), use_cache=False, html_format=True
        )

        if not badges:
            badges = def_badges

        if not achievements:
            achievements = def_achievements

        buffer = await generate_profile(ctx, achievements, badges, user)
        await ctx.send(file=discord.File(buffer, filename=f'{user.id}_profile.png'))

    # NOTE: !! Not implemented yet !!
    # @commands.hybrid_command(
    #     name='achievements',
    #     description='View a users InterChat achievements',
    #     extras={'category': 'General'},
    # )
    # async def achievements(self, ctx: commands.Context['Bot'], user: discord.User = None): ...  # type: ignore[reportArgumentType]

    @commands.hybrid_command(
        name='leaderboard',
        description='View the InterChat leaderboard',
        extras={'category': 'General'},
    )
    @app_commands.choices(
        filter=[
            app_commands.Choice(name='Messages', value='messages'),
            app_commands.Choice(name='Votes', value='votes'),
        ]
    )
    @check_user()
    async def leaderboard(self, ctx: commands.Context['Bot'], filter: str = 'messages'):
        await ctx.defer()

        filter_map = {
            'messages': User.messageCount,
            'votes': User.voteCount,
        }

        order_column = filter_map.get(filter.lower(), User.messageCount)

        async with self.bot.db.get_session() as session:
            stmt = select(User).order_by(desc(col(order_column))).limit(10)
            result = (await session.exec(stmt)).all()

        leaderboard_data = []

        cur_rank = 0

        for user in result:
            cur_rank += 1
            discord_user = await self.bot.fetch_user(int(user.id))
            is_staff = is_interchat_staff(ctx, discord_user)
            leaderboard_data.append(
                {
                    'rank': cur_rank,
                    'username': f'@{user.name}',
                    'guild_tag': t('commands.leaderboard.staffTag', locale='en')
                    if is_staff
                    else t('commands.leaderboard.userTag', locale='en'),
                    'avatar_url': user.image
                    if user.image
                    else (self.bot.user.display_avatar.url if self.bot.user else ''),
                    'stat1_value': user.messageCount,
                    'stat2_value': user.voteCount,
                }
            )

        locale = await self.get_locale(ctx)
        buffer = await generate_leaderboard(
            leaderboard_data,
            t('commands.leaderboard.messagesColumn', locale=locale),
            t('commands.leaderboard.voteCountColumn', locale=locale),
        )
        await ctx.send(file=discord.File(buffer, filename='leaderboard.png'))

    @commands.hybrid_group()
    async def my(self, ctx: commands.Context['Bot']):
        # Group command - subcommands handle functionality
        pass

    @my.command(
        name='hubs',
        description='View all InterChat hubs you moderate, or own',
        extras={'category': 'Hubs'},
    )
    async def hubs(self, ctx: commands.Context['Bot']):
        embed = discord.Embed(title='Your Hubs', description=' ', color=self.constants.color)
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)

        async with self.bot.db.get_session() as session:
            stmt = (
                select(Hub)
                .options(selectinload(getattr(Hub, 'moderators')))
                .where(
                    or_(
                        Hub.ownerId == str(ctx.author.id),
                        getattr(Hub, 'moderators').any(HubModerator.userId == str(ctx.author.id)),
                    )
                )
            )
            result = (await session.exec(stmt)).all()

            for hub in result:
                # Derive role: prefer Owner if matches; otherwise check moderators
                if hub.ownerId == str(ctx.author.id):
                    user_role = 'Owner'
                else:
                    user_role = 'Member'
                    # Access preloaded moderators while session is still active
                    for moderator in hub.moderators:
                        if moderator.userId == str(ctx.author.id):
                            user_role = moderator.role.value
                            break

                embed.add_field(
                    name=hub.name,
                    value=f'> **Description:** {hub.description}\n> **Position:** {user_role}',
                    inline=False,
                )

        await ctx.send(embed=embed)

    @my.command(
        name='preferences',
        description='Change how InterChat works to best suit you',
        extras={'category': 'User'},
    )
    @check_user()
    async def preferences(self, ctx: commands.Context['Bot']):
        await ctx.defer(ephemeral=True)
        locale = await self.get_locale(ctx)
        embed = discord.Embed(
            title=t('ui.preferences.title', locale=locale),
            description=t('ui.preferences.description', locale=locale),
            color=self.constants.color,
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)
        view = PreferencesView(self.bot, ctx.author, locale)
        await ctx.send(embed=embed, view=view, ephemeral=True)

    @commands.hybrid_command(
        name='inbox',
        description='View official InterChat announcements',
        extras={'category': 'User'},
    )
    @check_user()
    async def inbox(self, ctx: commands.Context['Bot']):
        await ctx.defer(ephemeral=True)
        async with self.bot.db.get_session() as session:
            stmt = select(User).where(User.id == str(ctx.author.id))
            user = (await session.exec(stmt)).first()

            if user and user.inboxLastReadDate is not None:
                devstmt = select(DevAlerts).where(DevAlerts.createdAt > user.inboxLastReadDate)
            else:
                devstmt = select(DevAlerts)
            alerts = (await session.exec(devstmt)).all()

            if user:
                user.inboxLastReadDate = datetime.now()
            await session.commit()

        embed = discord.Embed(title='NONE', description='NONE', color=self.constants.color)
        embed.set_author(name=f'@{ctx.author}', icon_url=ctx.author.avatar)

        if not alerts:
            embed.title = 'All caught up!'
            embed.description = f"{self.bot.emotes.tick} We couldn't find any new announcements for you. Check back later!"
            return await ctx.send(embed=embed)

        embed.title = 'Inbox'
        embed.description = f'{self.bot.emotes.megaphone_icon} We found some messages from our team for you! Take a look below.'

        for alert in alerts:
            embed.add_field(name=alert.title, value=alert.content, inline=False)

        await ctx.send(embed=embed)


async def setup(bot):
    await bot.add_cog(UserCog(bot))
