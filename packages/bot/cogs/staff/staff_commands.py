from __future__ import annotations

import asyncio
from datetime import datetime
from typing import TYPE_CHECKING, Literal, Optional, cast

import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from sqlmodel import func, select, col
from sqlalchemy.orm import selectinload
from sqlalchemy import desc

from utils.constants import constants
from utils.modules.core.checks import is_interchat_staff_check, is_interchat_staff_direct
from db.models import Blacklist, Hub, ServerBlacklist, ServerData, User
from utils.modules.services.moderationService import ModerationService
from utils.utils import parse_duration
from utils.modules.core.i18n import t
from utils.modules.common.database import DatabaseUtils

REASON_DEFAULT = 'No reason provided.'

if TYPE_CHECKING:
    from main import Bot


class PaginatorView(discord.ui.View):
    def __init__(
        self,
        *,
        bot: Bot,
        build_embed,
        per_page: int,
        total: int,
        owner_id: int,
        initial_page: int = 0,
    ):
        super().__init__(timeout=180)
        self.bot = bot
        self.build_embed = build_embed
        self.per_page = per_page
        self.total = total
        self.page = max(0, initial_page)
        self.owner_id = owner_id

        self.prev_button = discord.ui.Button(
            label=t('ui.common.pagination.previous', locale='en'),
            style=discord.ButtonStyle.secondary,
        )
        self.next_button = discord.ui.Button(
            label=t('ui.common.pagination.next', locale='en'), style=discord.ButtonStyle.secondary
        )
        self.page_button = discord.ui.Button(
            label=t(
                'ui.common.pagination.page',
                locale='en',
                current=self.page + 1,
                total=max(1, (self.total + self.per_page - 1) // self.per_page),
            ),
            style=discord.ButtonStyle.gray,
            disabled=True,
        )

        self.prev_button.callback = self.on_prev
        self.next_button.callback = self.on_next

        self.add_item(self.prev_button)
        self.add_item(self.page_button)
        self.add_item(self.next_button)
        self._update_buttons()

    def _update_buttons(self):
        last_page = max(0, (self.total - 1) // self.per_page)
        self.prev_button.disabled = self.page <= 0
        self.next_button.disabled = self.page >= last_page
        self.page_button.label = t(
            'ui.common.pagination.page',
            locale='en',
            current=self.page + 1,
            total=(last_page + 1 if self.total else 1),
        )

    async def on_prev(self, interaction: discord.Interaction):
        if interaction.user.id != self.owner_id:
            await interaction.response.send_message(
                f'{self.bot.emotes.x_icon} '
                + t('ui.common.errors.cannotControlPagination', locale='en'),
                ephemeral=True,
            )
            return
        if self.page > 0:
            self.page -= 1
        await self._edit(interaction)

    async def on_next(self, interaction: discord.Interaction):
        if interaction.user.id != self.owner_id:
            await interaction.response.send_message(
                f'{self.bot.emotes.x_icon} '
                + t('ui.common.errors.cannotControlPagination', locale='en'),
                ephemeral=True,
            )
            return
        last_page = max(0, (self.total - 1) // self.per_page)
        if self.page < last_page:
            self.page += 1
        await self._edit(interaction)

    async def _edit(self, interaction: discord.Interaction):
        self._update_buttons()
        embed = await self.build_embed(self.page)
        await interaction.response.edit_message(embed=embed, view=self)


class Staff(CogBase):
    def __init__(self, bot: 'Bot') -> None:
        self.bot: 'Bot' = bot
        self._moderationsvc: Optional[ModerationService] = None

    async def get_moderation_service(self) -> ModerationService:
        if self._moderationsvc:
            return self._moderationsvc
        async with self.bot.db.get_session() as session:
            self._moderationsvc = ModerationService(session)
        return self._moderationsvc

    @commands.group(name='get', description=t('commands.staff.get.description', locale='en'))
    async def get_group(self, ctx: commands.Context) -> None:
        """Group command for retrieving information about hubs, servers, and users."""
        if ctx.invoked_subcommand is None:
            await ctx.send_help(ctx.command)

    def _format_timestamp(self, timestamp: datetime) -> str:
        """Format a datetime object for Discord timestamp display."""
        unix_timestamp = int(timestamp.timestamp())
        return f'<t:{unix_timestamp}:D>\n<t:{unix_timestamp}:R>'

    def _create_base_embed(self, title: Optional[str] = None) -> discord.Embed:
        """Create a base embed with consistent styling."""
        embed = discord.Embed(color=constants.color)
        if title:
            embed.title = title
        return embed

    def _add_status_badges(self, embed: discord.Embed, hub: Hub) -> None:
        """Add status badges to a hub embed."""
        status_badges = []
        if hub.verified:
            status_badges.append(
                f'{self.bot.emotes.tick} {t("ui.staff.hub.badges.verified", locale="en")}'
            )
        if hub.partnered:
            status_badges.append(
                f'{self.bot.emotes.connect} {t("ui.staff.hub.badges.partnered", locale="en")}'
            )
        if hub.featured:
            status_badges.append(
                f'{self.bot.emotes.star} {t("ui.staff.hub.badges.featured", locale="en")}'
            )
        if hub.private:
            status_badges.append(
                f'{self.bot.emotes.lock_icon} {t("ui.staff.hub.badges.private", locale="en")}'
            )
        if hub.locked:
            status_badges.append(
                f'{self.bot.emotes.no} {t("ui.staff.hub.badges.locked", locale="en")}'
            )
        if hub.nsfw:
            status_badges.append(
                f'{self.bot.emotes.alert_icon} {t("ui.staff.hub.badges.nsfw", locale="en")}'
            )

        if status_badges:
            embed.add_field(
                name=t('ui.staff.hub.section.status', locale='en'),
                value=' • '.join(status_badges),
                inline=False,
            )

    def _add_moderation_summary(self, embed: discord.Embed, hub: Hub) -> None:
        """Add moderation summary to a hub embed."""
        if not any([hub.moderators, hub.blockWords, hub.antiSwearRules, hub.rules]):
            return

        mod_summary = []
        if hub.moderators:
            mod_summary.append(f'{len(hub.moderators)} mods')
        if hub.rules:
            mod_summary.append(f'{len(hub.rules)} rules')
        if hub.blockWords:
            mod_summary.append(f'{len(hub.blockWords)} blocked words')
        if hub.antiSwearRules:
            mod_summary.append(f'{len(hub.antiSwearRules)} anti-swear rules')

        embed.add_field(
            name=f'{self.bot.emotes.hammer_icon} {t("ui.staff.hub.section.moderation", locale="en")}',
            value=' • '.join(mod_summary),
            inline=False,
        )

    @get_group.command(name='hub', description=t('commands.staff.get.hub.description', locale='en'))
    @is_interchat_staff_check()
    async def get_hub(self, ctx: commands.Context['Bot'], hub_name: str) -> None:
        """Retrieve and display detailed information about a specific hub."""
        async with ctx.bot.db.get_session() as session:
            stmt = (
                select(Hub)
                .where(Hub.name == hub_name)
                .options(
                    selectinload(Hub.moderators),  # pyright: ignore[reportArgumentType]
                    selectinload(Hub.blockWords),  # pyright: ignore[reportArgumentType]
                    selectinload(Hub.antiSwearRules),  # pyright: ignore[reportArgumentType]
                    selectinload(Hub.upvotes),  # pyright: ignore[reportArgumentType]
                    selectinload(Hub.connections),  # pyright: ignore[reportArgumentType]
                )
            )
            result = await session.exec(stmt)
            hub_data = result.first()

            if not hub_data:
                await ctx.send(t('ui.common.messages.hubNotFound', locale='en'))
                return

            embed = await self._create_hub_embed(hub_data)

        await ctx.send(embed=embed)

    async def _create_hub_embed(self, hub: Hub) -> discord.Embed:
        """Create a detailed embed for a hub."""
        embed = self._create_base_embed()

        # Set thumbnail if available
        if hub.iconUrl and hub.iconUrl != 'none':
            embed.set_thumbnail(url=hub.iconUrl)
        elif hub.bannerUrl and hub.bannerUrl != 'none':
            embed.set_image(url=hub.bannerUrl)

        # Basic information
        hub_title = f'**{hub.name}**'
        if hub.shortDescription:
            hub_title += f'\n{hub.shortDescription}'
        embed.add_field(
            name=f'{self.bot.emotes.hash_icon} {t("ui.staff.hub.fields.name", locale="en")}',
            value=hub_title,
            inline=True,
        )

        # Statistics
        embed.add_field(
            name=f'{self.bot.emotes.chat_icon} {t("ui.staff.hub.fields.messages", locale="en")}',
            value=t(
                'ui.staff.hub.values.messagesThisWeek', locale='en', count=hub.weeklyMessageCount
            ),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.link_icon} {t("ui.staff.hub.fields.connections", locale="en")}',
            value=t(
                'ui.staff.hub.values.connectionsActive', locale='en', count=len(hub.connections)
            ),
            inline=True,
        )

        # Timestamps
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} {t("ui.staff.hub.fields.created", locale="en")}',
            value=self._format_timestamp(hub.createdAt),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.refresh_icon} {t("ui.staff.hub.fields.lastActive", locale="en")}',
            value=self._format_timestamp(hub.lastActive),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.trophy_icon} {t("ui.staff.hub.fields.upvotes", locale="en")}',
            value=t('ui.staff.hub.values.upvotesTotal', locale='en', count=len(hub.upvotes)),
            inline=True,
        )

        # Owner and location
        embed.add_field(
            name=f'{self.bot.emotes.person_icon} {t("ui.staff.hub.fields.owner", locale="en")}',
            value=f'<@{hub.ownerId}>',
            inline=True,
        )

        location_info = []
        if hub.language:
            location_info.append(f'{self.bot.emotes.globe_icon} {hub.language}')
        if hub.region:
            location_info.append(f'{self.bot.emotes.house_icon} {hub.region}')

        location_value = (
            ' • '.join(location_info)
            if location_info
            else t('ui.common.messages.notSpecified', locale='en')
        )
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} {t("ui.staff.hub.fields.location", locale="en")}',
            value=location_value,
            inline=True,
        )

        embed.add_field(
            name=f'{self.bot.emotes.fire_icon} {t("ui.staff.hub.fields.activity", locale="en")}',
            value=hub.activityLevel.value.title(),
            inline=True,
        )

        # Status badges and moderation
        self._add_status_badges(embed, hub)
        self._add_moderation_summary(embed, hub)

        # Additional information
        if hub.appealCooldownHours != 168:
            embed.add_field(
                name=f'{self.bot.emotes.clock_icon} {t("ui.staff.hub.fields.appealCooldown", locale="en")}',
                value=f'{hub.appealCooldownHours} hours',
                inline=True,
            )

        if hub.welcomeMessage:
            welcome_preview = (
                hub.welcomeMessage[:150] + '...'
                if len(hub.welcomeMessage) > 150
                else hub.welcomeMessage
            )
            embed.add_field(
                name=f'{self.bot.emotes.wave_anim} {t("ui.staff.hub.fields.welcomeMessage", locale="en")}',
                value=f'```{welcome_preview}```',
                inline=False,
            )

        if (
            hub.description
            and hub.description != hub.shortDescription
            and len(hub.description) > 100
        ):
            desc_preview = (
                hub.description[:200] + '...' if len(hub.description) > 200 else hub.description
            )
            embed.add_field(
                name=f'{self.bot.emotes.description} {t("ui.staff.hub.fields.description", locale="en")}',
                value=desc_preview,
                inline=False,
            )

        if hub.bannerUrl:
            embed.set_image(url=hub.bannerUrl)

        embed.set_footer(text=f'Hub ID: {hub.id}')
        return embed

    @get_group.command(
        name='server', description=t('commands.staff.get.server.description', locale='en')
    )
    @is_interchat_staff_check()
    async def get_server(self, ctx: commands.Context['Bot'], server_id: str) -> None:
        """Retrieve and display detailed information about a specific server."""
        server_data = await DatabaseUtils.get_server(server_id)
        if not server_data:
            await ctx.send(t('ui.common.messages.serverNotFound', locale='en'))
            return

        embed = await self._create_server_embed(server_data)
        await ctx.send(embed=embed)

    async def _create_server_embed(self, server: ServerData) -> discord.Embed:
        """Create a detailed embed for a server."""
        embed = self._create_base_embed()

        # Set thumbnail if available
        if server.iconUrl:
            embed.set_thumbnail(url=server.iconUrl)

        # Basic information
        embed.add_field(
            name=f'{self.bot.emotes.hash_icon} {t("ui.staff.server.fields.name", locale="en")}',
            value=f'**{server.name}**',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.ID_icon} {t("ui.staff.server.fields.serverId", locale="en")}',
            value=f'`{server.id}`',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.invite_icon} {t("ui.staff.server.fields.inviteCode", locale="en")}',
            value=f'`{server.inviteCode}`',
            inline=True,
        )

        # Statistics
        embed.add_field(
            name=f'{self.bot.emotes.chat_icon} {t("ui.staff.server.fields.messages", locale="en")}',
            value=t('ui.staff.server.values.messagesTotal', locale='en', count=server.messageCount),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.link_icon} {t("ui.staff.server.fields.connections", locale="en")}',
            value=t(
                'ui.staff.server.values.connectionsActive',
                locale='en',
                count=len(server.connections),
            ),
            inline=True,
        )

        # Timestamps
        embed.add_field(
            name=f'{self.bot.emotes.calendar_icon} {t("ui.staff.server.fields.created", locale="en")}',
            value=self._format_timestamp(server.createdAt),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.clock_icon} {t("ui.staff.server.fields.lastMessage", locale="en")}',
            value=self._format_timestamp(server.lastMessageAt),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.refresh_icon} {t("ui.staff.server.fields.updated", locale="en")}',
            value=self._format_timestamp(server.updatedAt),
            inline=True,
        )

        # Moderation summary
        mod_summary = []
        if server.infractions:
            mod_summary.append(f'{len(server.infractions)} infractions')
        if server.serverBlacklists:
            mod_summary.append(f'{len(server.serverBlacklists)} blacklists')

        if mod_summary:
            embed.add_field(
                name=t('ui.staff.hub.section.moderation', locale='en'),
                value=' • '.join(mod_summary),
                inline=False,
            )

        # Leaderboard entries
        if server.leaderboardEntries:
            embed.add_field(
                name=f'{self.bot.emotes.leaderboard_icon} Leaderboard',
                value=f'**{len(server.leaderboardEntries)}** entries',
                inline=True,
            )

        embed.set_footer(text=f'Server ID: {server.id}')
        return embed

    @get_group.command(
        name='user', description=t('commands.staff.get.user.description', locale='en')
    )
    @is_interchat_staff_check()
    async def get_user(self, ctx: commands.Context['Bot'], user: discord.User) -> None:
        """Retrieve and display detailed information about a specific user."""
        user_data = await DatabaseUtils.get_user(str(user.id))
        if not user_data:
            await ctx.send(t('ui.common.messages.userNotFound', locale='en'))
            return

        embed = await self._create_user_embed(user_data)
        await ctx.send(embed=embed)

    async def _create_user_embed(self, user: User) -> discord.Embed:
        """Create a detailed embed for a user."""
        embed = self._create_base_embed()

        # Set thumbnail if available
        if user.image:
            embed.set_thumbnail(url=user.image)

        # Basic information
        embed.add_field(
            name=f'{self.bot.emotes.person_icon} {t("ui.staff.user.fields.name", locale="en")}',
            value=f'**{user.name}**',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.ID_icon} {t("ui.staff.user.fields.userId", locale="en")}',
            value=f'`{user.id}`',
            inline=True,
        )

        # Status
        staff_status = (
            f'{self.bot.emotes.staff_badge} {t("ui.staff.user.values.staff", locale="en")}'
            if is_interchat_staff_direct(self.bot, int(user.id))
            else f'{self.bot.emotes.dot} {t("ui.staff.user.values.member", locale="en")}'
        )
        embed.add_field(
            name=f'{self.bot.emotes.info_icon} {t("ui.staff.user.fields.status", locale="en")}',
            value=staff_status,
            inline=True,
        )

        # Statistics
        self._add_user_statistics(embed, user)

        # Timestamps
        self._add_user_timestamps(embed, user)

        # Additional sections
        self._add_user_preferences(embed, user)
        self._add_user_owned_content(embed, user)
        self._add_user_moderation_summary(embed, user)
        self._add_user_activity_summary(embed, user)
        self._add_user_badges(embed, user)

        embed.set_footer(text=f'User ID: {user.id}')
        return embed

    def _add_user_statistics(self, embed: discord.Embed, user: User) -> None:
        """Add user statistics to the embed."""
        embed.add_field(
            name=f'{self.bot.emotes.chat_icon} {t("ui.staff.user.fields.messages", locale="en")}',
            value=t('ui.staff.user.values.messagesSent', locale='en', count=user.messageCount),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.trophy_icon} {t("ui.staff.user.fields.reputation", locale="en")}',
            value=t('ui.staff.user.values.reputationPoints', locale='en', count=user.reputation),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} {t("ui.staff.user.fields.votes", locale="en")}',
            value=t('ui.staff.user.values.votesCast', locale='en', count=user.voteCount),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.link_icon} {t("ui.staff.user.fields.hubJoins", locale="en")}',
            value=t('ui.staff.user.values.hubJoinsTotal', locale='en', count=user.hubJoinCount),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.fire_icon} {t("ui.staff.user.fields.engagement", locale="en")}',
            value=f'**{user.hubEngagementScore:.1f}**',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} {t("ui.staff.user.fields.locale", locale="en")}',
            value=f'**{user.locale.upper() if user.locale else t("ui.staff.user.values.notSet", locale="en")}**',
            inline=True,
        )

    def _add_user_timestamps(self, embed: discord.Embed, user: User) -> None:
        """Add user timestamps to the embed."""
        if user.createdAt:
            embed.add_field(
                name=f'{self.bot.emotes.calendar_icon} {t("ui.staff.user.fields.created", locale="en")}',
                value=self._format_timestamp(user.createdAt),
                inline=True,
            )

        if user.lastMessageAt:
            embed.add_field(
                name=f'{self.bot.emotes.clock_icon} {t("ui.staff.user.fields.lastMessage", locale="en")}',
                value=self._format_timestamp(user.lastMessageAt),
                inline=True,
            )

        if user.lastVoted:
            embed.add_field(
                name=f'{self.bot.emotes.globe_icon} {t("ui.staff.user.fields.lastVote", locale="en")}',
                value=self._format_timestamp(user.lastVoted),
                inline=True,
            )
        else:
            embed.add_field(
                name=f'{self.bot.emotes.globe_icon} {t("ui.staff.user.fields.lastVote", locale="en")}',
                value=t('ui.staff.user.values.never', locale='en'),
                inline=True,
            )

    def _add_user_preferences(self, embed: discord.Embed, user: User) -> None:
        """Add user preferences to the embed."""
        preferences = []
        if user.showBadges:
            preferences.append(t('ui.staff.user.preferences.showBadges', locale='en'))
        if user.mentionOnReply:
            preferences.append(t('ui.staff.user.preferences.mentionOnReply', locale='en'))
        if user.showNsfwHubs:
            preferences.append(t('ui.staff.user.preferences.showNsfwHubs', locale='en'))

        if preferences:
            embed.add_field(
                name=t('ui.staff.user.sections.preferences', locale='en'),
                value=' • '.join(preferences),
                inline=False,
            )

    def _add_user_owned_content(self, embed: discord.Embed, user: User) -> None:
        """Add user owned content summary to the embed."""
        owned_summary = []
        if user.ownedHubs:
            owned_summary.append(
                t('ui.staff.user.content.hubs', locale='en', count=len(user.ownedHubs))
            )
        if user.modPositions:
            owned_summary.append(
                t('ui.staff.user.content.modPositions', locale='en', count=len(user.modPositions))
            )
        if user.blockWordsCreated:
            owned_summary.append(
                t(
                    'ui.staff.user.content.blockedWords',
                    locale='en',
                    count=len(user.blockWordsCreated),
                )
            )
        if user.antiSwearRulesCreated:
            owned_summary.append(
                t(
                    'ui.staff.user.content.antiSwearRules',
                    locale='en',
                    count=len(user.antiSwearRulesCreated),
                )
            )

        if owned_summary:
            embed.add_field(
                name=t('ui.staff.user.sections.createdContent', locale='en'),
                value=' • '.join(owned_summary),
                inline=False,
            )

    def _add_user_moderation_summary(self, embed: discord.Embed, user: User) -> None:
        """Add user moderation summary to the embed."""
        mod_summary = []
        if user.infractions:
            mod_summary.append(
                t(
                    'ui.staff.user.moderation.infractionsReceived',
                    locale='en',
                    count=len(user.infractions),
                )
            )
        if user.issuedInfractions:
            mod_summary.append(
                t(
                    'ui.staff.user.moderation.infractionsIssued',
                    locale='en',
                    count=len(user.issuedInfractions),
                )
            )
        if user.blacklists:
            mod_summary.append(
                t(
                    'ui.staff.user.moderation.blacklistsReceived',
                    locale='en',
                    count=len(user.blacklists),
                )
            )
        if user.issuedBlacklists:
            mod_summary.append(
                t(
                    'ui.staff.user.moderation.blacklistsIssued',
                    locale='en',
                    count=len(user.issuedBlacklists),
                )
            )

        if mod_summary:
            embed.add_field(
                name=f'{self.bot.emotes.hammer_icon} {t("ui.staff.hub.section.moderation", locale="en")}',
                value=' • '.join(mod_summary),
                inline=False,
            )

    def _add_user_activity_summary(self, embed: discord.Embed, user: User) -> None:
        """Add user activity summary to the embed."""
        activity_summary = []
        if user.appeals:
            activity_summary.append(
                t('ui.staff.user.activity.appeals', locale='en', count=len(user.appeals))
            )
        if user.reviews:
            activity_summary.append(
                t('ui.staff.user.activity.reviews', locale='en', count=len(user.reviews))
            )
        if user.hubReportsSubmitted:
            activity_summary.append(
                t(
                    'ui.staff.user.activity.reportsMade',
                    locale='en',
                    count=len(user.hubReportsSubmitted),
                )
            )
        if user.hubReportsReceived:
            activity_summary.append(
                t(
                    'ui.staff.user.activity.reportsReceived',
                    locale='en',
                    count=len(user.hubReportsReceived),
                )
            )
        if user.achievements:
            activity_summary.append(
                t('ui.staff.user.activity.achievements', locale='en', count=len(user.achievements))
            )

        if activity_summary:
            embed.add_field(
                name=f'{self.bot.emotes.activities} {t("ui.staff.user.sections.activity", locale="en")}',
                value=' • '.join(activity_summary),
                inline=False,
            )

    def _add_user_badges(self, embed: discord.Embed, user: User) -> None:
        """Add user badges to the embed."""
        if user.badges:
            badge_text = ' • '.join([badge.name for badge in user.badges])
            embed.add_field(
                name=f'{self.bot.emotes.staff_badge} {t("ui.staff.user.sections.badges", locale="en")}',
                value=badge_text,
                inline=False,
            )

    @commands.hybrid_group(name='blacklist')
    @is_interchat_staff_check()
    async def blacklist(self, ctx: commands.Context):
        # This is a command group; subcommands implement functionality.
        pass

    @blacklist.command(name='add')
    async def add(
        self,
        ctx: commands.Context['Bot'],
        reason: str,
        duration: Optional[str] = None,
        user: Optional[discord.User] = None,
        server: Optional[str] = None,
    ) -> None:
        """Blacklist a user or server from InterChat."""
        if user and server:
            await ctx.send(t('responses.moderation.target.both', locale='en'))
            return
        if not user and not server:
            await ctx.send(t('responses.moderation.target.missing', locale='en'))
            return

        moderationsvc = await self.get_moderation_service()
        dur_ms = parse_duration(duration or '')
        if user:
            # Duplicate prevention
            if await moderationsvc.is_user_blacklisted(str(user.id)):
                await ctx.send(
                    f'{self.bot.emotes.x_icon} '
                    + t('responses.moderation.blacklist.alreadyActive', locale='en')
                )
                return

            await moderationsvc.create_blacklist_entry(
                str(user.id),
                str(ctx.author.id),
                reason,
                dur_ms,
            )

            await ctx.send(
                f'{self.bot.emotes.blobFastBan} '
                + t(
                    'responses.moderation.blacklist.success', locale='en', target=f'**{user.name}**'
                )
                + f' {t("ui.infractions.fields.reason", locale="en")}: {reason}'
            )
        elif server:
            server_id = server

            # Duplicate prevention
            if await moderationsvc.is_server_blacklisted(server_id):
                await ctx.send(
                    f'{self.bot.emotes.x_icon} '
                    + t('responses.moderation.blacklist.alreadyActive', locale='en')
                )
                return

            await moderationsvc.create_server_blacklist_entry(
                server_id,
                str(ctx.author.id),
                reason,
                dur_ms,
            )

            server_row = await DatabaseUtils.get_server(server_id)
            server_name = server_row.name if server_row else server_id
            await ctx.send(
                f'{self.bot.emotes.blobFastBan} '
                + t(
                    'responses.moderation.blacklist.success',
                    locale='en',
                    target=f'**{server_name}**',
                )
                + f' {t("ui.infractions.fields.reason", locale="en")}: {reason}'
            )

    @blacklist.command(name='remove')
    @is_interchat_staff_check()
    async def remove(
        self,
        ctx: commands.Context['Bot'],
        user: Optional[discord.User] = None,
        server: Optional[str] = None,
    ) -> None:
        """Unblacklist a user or server from InterChat."""
        if user and server:
            await ctx.send(t('responses.moderation.target.both', locale='en'))
            return
        if not user and not server:
            await ctx.send(t('responses.moderation.target.missing', locale='en'))
            return

        moderationsvc = await self.get_moderation_service()
        if user:
            ok = await moderationsvc.delete_blacklist_entry(str(user.id))
            if ok:
                await ctx.send(
                    f'{self.bot.emotes.tick} '
                    + t('responses.staff.blacklist.removed', locale='en', target=f'**{user.name}**')
                )
            else:
                await ctx.send(
                    f'{self.bot.emotes.x_icon} '
                    + t('responses.staff.blacklist.notFound', locale='en', type='user')
                )
        elif server:
            ok = await moderationsvc.delete_server_blacklist_entry(server)
            server_row = await DatabaseUtils.get_server(server)
            server_name = server_row.name if server_row else server
            if ok:
                await ctx.send(
                    f'{self.bot.emotes.tick} '
                    + t(
                        'responses.staff.blacklist.removed',
                        locale='en',
                        target=f'**{server_name}**',
                    )
                )
            else:
                await ctx.send(
                    f'{self.bot.emotes.x_icon} '
                    + t('responses.staff.blacklist.notFound', locale='en', type='server')
                )

    @blacklist.command(
        name='list', description=t('commands.staff.blacklist.list.description', locale='en')
    )
    @is_interchat_staff_check()
    async def bl_list(self, ctx: commands.Context[Bot], type: Literal['users', 'servers']):
        """List globally blacklisted users or servers."""
        await ctx.defer()

        per_page = 10
        # Compute total in one session
        async with ctx.bot.db.get_session() as session:
            if type == 'users':
                total_stmt = select(func.count()).select_from(Blacklist)
                total = (await session.exec(total_stmt)).one()
            else:
                total_stmt = select(func.count()).select_from(ServerBlacklist)
                total = (await session.exec(total_stmt)).one()

        async def build_user_embed(page: int):
            async with ctx.bot.db.get_session() as session:
                stmt = (
                    select(Blacklist)
                    .options(
                        selectinload(Blacklist.user),  # pyright: ignore[reportArgumentType]
                        selectinload(Blacklist.moderator),  # pyright: ignore[reportArgumentType]
                    )
                    .order_by(desc(col(Blacklist.createdAt)))
                    .offset(page * per_page)
                    .limit(per_page)
                )
                result = await session.exec(stmt)
                rows: list[Blacklist] = list(result.all())

            embed = discord.Embed(
                description=f'### {self.bot.emotes.hammer_icon} {t("ui.staff.blacklist.titles.userList", locale="en")}',
                color=constants.color,
            )
            if not rows:
                embed.description = t('ui.staff.blacklist.descriptions.userListEmpty', locale='en')
                return embed

            for bl in rows:
                user_name = bl.user.name if bl.user else t('responses.common.unknown', locale='en')
                moderator = bl.moderator
                mod_str = (
                    f'<@{moderator.id}>'
                    if moderator
                    else t('responses.common.unknown', locale='en')
                )
                ts = f'<t:{int(bl.createdAt.timestamp())}:R>'
                reason = bl.reason or t('responses.appeal.constants.noReason', locale='en')
                expires = (
                    f' • {t("ui.staff.blacklist.fields.expires", locale="en")}: <t:{int(bl.expiresAt.timestamp())}:R>'
                    if bl.expiresAt
                    else ''
                )
                embed.add_field(
                    name=f'{user_name} ({bl.userId})',
                    value=f'> {self.bot.emotes.info_icon} {t("ui.infractions.fields.reason", locale="en")}: {reason}\n> {self.bot.emotes.person_icon} {t("ui.staff.blacklist.fields.staff", locale="en")}: {mod_str}\n> {self.bot.emotes.calendar_icon} {t("ui.staff.blacklist.fields.added", locale="en")}: {ts}{expires}',
                    inline=False,
                )
            return embed

        async def build_server_embed(page: int):
            async with ctx.bot.db.get_session() as session:
                stmt = (
                    select(ServerBlacklist)
                    .options(
                        selectinload(ServerBlacklist.server),  # pyright: ignore[reportArgumentType]
                        selectinload(ServerBlacklist.moderator),  # pyright: ignore[reportArgumentType]
                    )
                    .order_by(desc(col(ServerBlacklist.createdAt)))
                    .offset(page * per_page)
                    .limit(per_page)
                )
                result = await session.exec(stmt)
                rows: list[ServerBlacklist] = list(result.all())

            embed = discord.Embed(
                description=f'### {self.bot.emotes.hammer_icon} {t("ui.staff.blacklist.titles.serverList", locale="en")}',
                color=constants.color,
            )
            if not rows:
                embed.description = t(
                    'ui.staff.blacklist.descriptions.serverListEmpty', locale='en'
                )
                return embed

            for bl in rows:
                server_name = (
                    bl.server.name if bl.server else t('responses.common.unknown', locale='en')
                )
                moderator = bl.moderator
                mod_str = (
                    f'<@{moderator.id}>'
                    if moderator
                    else t('responses.common.unknown', locale='en')
                )
                ts = f'<t:{int(bl.createdAt.timestamp())}:R>'
                reason = bl.reason or t('responses.appeal.constants.noReason', locale='en')
                expires = (
                    f' • {t("ui.staff.blacklist.fields.expires", locale="en")}: <t:{int(bl.expiresAt.timestamp())}:R>'
                    if bl.expiresAt
                    else ''
                )
                embed.add_field(
                    name=f'{server_name} ({bl.serverId})',
                    value=f'> {self.bot.emotes.info_icon} {t("ui.infractions.fields.reason", locale="en")}: {reason}\n> {self.bot.emotes.person_icon} {t("ui.staff.blacklist.fields.staff", locale="en")}: {mod_str}\n> {self.bot.emotes.calendar_icon} {t("ui.staff.blacklist.fields.added", locale="en")}: {ts}{expires}',
                    inline=False,
                )
            return embed

        build_embed = build_user_embed if type == 'users' else build_server_embed
        paginator = PaginatorView(
            bot=self.bot,
            build_embed=build_embed,
            per_page=per_page,
            total=total,
            owner_id=ctx.author.id,
        )
        embed = await paginator.build_embed(paginator.page)
        await ctx.reply(embed=embed, view=paginator, mention_author=False)

    async def _fetch_blacklist_entries_for_id(
        self, ctx: commands.Context[Bot], id: str
    ) -> tuple[list[Blacklist], list[ServerBlacklist]]:
        """Fetch blacklist entries for a given user or server ID."""
        async with ctx.bot.db.get_session() as session:
            user_stmt = (
                select(Blacklist)
                .where(Blacklist.userId == id)
                .options(
                    selectinload(Blacklist.user),  # pyright: ignore[reportArgumentType]
                    selectinload(Blacklist.moderator),  # pyright: ignore[reportArgumentType]
                )
            )
            server_stmt = (
                select(ServerBlacklist)
                .where(ServerBlacklist.serverId == id)
                .options(
                    selectinload(ServerBlacklist.server),  # pyright: ignore[reportArgumentType]
                    selectinload(ServerBlacklist.moderator),  # pyright: ignore[reportArgumentType]
                )
            )
            user_res = list((await session.exec(user_stmt)).all())
            server_res = list((await session.exec(server_stmt)).all())
        return user_res, server_res

    async def _build_blacklist_search_embed_page(
        self,
        combined: list[
            tuple[Literal['user'], Blacklist] | tuple[Literal['server'], ServerBlacklist]
        ],
        page: int,
        per_page: int,
    ) -> discord.Embed:
        """Build a single page of the blacklist search embed."""
        # Ensure this remains async for paginator expectations
        await asyncio.sleep(0)

        start = page * per_page
        end = start + per_page
        chunk = combined[start:end]

        embed = discord.Embed(
            color=constants.color,
            description=f'### {self.bot.emotes.search_icon} {t("ui.staff.blacklist.titles.searchResults", locale="en")}',
        )

        if not chunk:
            embed.description = f'{self.bot.emotes.x_icon} {t("ui.staff.blacklist.descriptions.noEntriesPage", locale="en")}'
            return embed

        for kind, entry in chunk:
            if kind == 'user':
                bl = cast(Blacklist, entry)
                ts = f'<t:{int(bl.createdAt.timestamp())}:R>'
                expires_at = bl.expiresAt
                expires = (
                    f' • {t("ui.staff.blacklist.fields.expires", locale="en")}: <t:{int(expires_at.timestamp())}:R>'
                    if expires_at
                    else ''
                )
                reason = bl.reason or t('responses.appeal.constants.noReason', locale='en')
                user_name = bl.user.name if bl.user else t('responses.common.unknown', locale='en')
                moderator = bl.moderator
                mod_str = (
                    f'<@{moderator.id}>'
                    if moderator
                    else t('responses.common.unknown', locale='en')
                )
                embed.add_field(
                    name=f'{t("ui.staff.blacklist.labels.user", locale="en")} {user_name} ({bl.userId})',
                    value=f'{t("ui.infractions.fields.reason", locale="en")}: {reason}\n{t("ui.staff.blacklist.fields.staff", locale="en")}: {mod_str}\n{t("ui.staff.blacklist.fields.added", locale="en")}: {ts}{expires}',
                    inline=False,
                )
            else:
                sbl = cast(ServerBlacklist, entry)
                ts = f'<t:{int(sbl.createdAt.timestamp())}:R>'
                expires_at = sbl.expiresAt
                expires = (
                    f' • {t("ui.staff.blacklist.fields.expires", locale="en")}: <t:{int(expires_at.timestamp())}:R>'
                    if expires_at
                    else ''
                )
                reason = sbl.reason or t('responses.appeal.constants.noReason', locale='en')
                server_name = (
                    sbl.server.name if sbl.server else t('responses.common.unknown', locale='en')
                )
                moderator = sbl.moderator
                mod_str = (
                    f'<@{moderator.id}>'
                    if moderator
                    else t('responses.common.unknown', locale='en')
                )
                embed.add_field(
                    name=f'{t("ui.staff.blacklist.labels.server", locale="en")} {server_name} ({sbl.serverId})',
                    value=f'{t("ui.infractions.fields.reason", locale="en")}: {reason}\n{t("ui.staff.blacklist.fields.staff", locale="en")}: {mod_str}\n{t("ui.staff.blacklist.fields.added", locale="en")}: {ts}{expires}',
                    inline=False,
                )
        return embed

    @blacklist.command(name='search', description='Search blacklist for a user or server ID')
    @is_interchat_staff_check()
    async def bl_search(self, ctx: commands.Context[Bot], id: str):
        """Search blacklist for a user or server ID."""
        await ctx.defer()

        if not id.isdigit():
            await ctx.reply(
                f'{self.bot.emotes.x_icon} Invalid ID format. Provide a valid Discord ID.'
            )
            return

        user_res, server_res = await self._fetch_blacklist_entries_for_id(ctx, id)
        if not user_res and not server_res:
            await ctx.reply(f'{self.bot.emotes.x_icon} No blacklist entries found for `{id}`.')
            return

        # Combine and sort by createdAt desc
        combined: list[
            tuple[Literal['user'], Blacklist] | tuple[Literal['server'], ServerBlacklist]
        ] = []
        for bl in user_res:
            combined.append(('user', bl))
        for bl in server_res:
            combined.append(('server', bl))
        combined.sort(key=lambda t: t[1].createdAt, reverse=True)

        per_page = 10
        total = len(combined)

        async def build_embed(page: int):
            return await self._build_blacklist_search_embed_page(combined, page, per_page)

        paginator = PaginatorView(
            bot=self.bot,
            build_embed=build_embed,
            per_page=per_page,
            total=total,
            owner_id=ctx.author.id,
        )
        embed = await paginator.build_embed(paginator.page)
        await ctx.reply(embed=embed, view=paginator, mention_author=False)


async def setup(bot: 'Bot') -> None:
    """Set up the Staff cog."""
    await bot.add_cog(Staff(bot))
