from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING

from discord.ext import tasks
from sqlmodel import and_, delete, func, select, update, col

from utils.constants import logger
from utils.modules.common.cogs import CogBase
from db.models import Badges, Broadcast, Message, User

if TYPE_CHECKING:
    from main import Bot


class DataCleanup(CogBase):
    def __init__(self, bot):
        self.bot: 'Bot' = bot

    async def cog_load(self) -> None:
        self.cleanup_data.start()
        logger.info('Data cleanup task started')

    async def cog_unload(self) -> None:
        if self.cleanup_data.is_running():
            logger.debug('Data cleanup task cancelled')
            self.cleanup_data.cancel()

    @tasks.loop(hours=24)
    async def cleanup_data(self):
        """
        Performs daily cleanup of old data:
        - Delete broadcasts older than 2 days
        - Remove voter badges from users who haven't voted in 12+ hours
        - Delete messages older than 30 days
        """
        logger.info('Starting daily data cleanup tasks')

        await self._run_cleanup_task('broadcasts', self._cleanup_old_broadcasts)
        await self._run_cleanup_task('voter badges', self._cleanup_voter_badges)
        await self._run_cleanup_task('messages', self._cleanup_old_messages)

        logger.info('Daily data cleanup tasks completed')

    async def _run_cleanup_task(self, task_name: str, cleanup_func):
        """Run a single cleanup task with independent error handling"""
        try:
            async with self.bot.db.get_session() as session:
                await cleanup_func(session)
                await session.commit()
                logger.debug(f'{task_name} cleanup task completed successfully')
        except Exception as e:
            logger.error(f'Error during {task_name} cleanup: {e}', exc_info=True)

    async def _cleanup_old_broadcasts(self, session):
        """Delete broadcasts older than 2 days"""
        cutoff_date = datetime.now() - timedelta(days=2)

        # Count broadcasts to be deleted for logging
        count_stmt = select(func.count(col(Broadcast.id))).where(
            col(Broadcast.createdAt) < cutoff_date
        )
        count_result = await session.scalar(count_stmt)

        if count_result > 0:
            # Delete old broadcasts
            delete_stmt = delete(Broadcast).where(col(Broadcast.createdAt) < cutoff_date)
            await session.exec(delete_stmt)
            logger.info(f'Deleted {count_result} broadcasts older than 2 days')
        else:
            logger.debug('No broadcasts older than 2 days found for cleanup')

    async def _cleanup_voter_badges(self, session):
        """Remove voter badges from users who haven't voted in 12+ hours"""
        cutoff_date = datetime.now() - timedelta(hours=12)

        # Find users with voter badges who haven't voted recently
        stmt = select(User.id, User.badges, User.lastVoted).where(
            and_(col(User.badges).contains([Badges.VOTER]), User.lastVoted < cutoff_date)
        )

        users_to_update = await session.exec(stmt)
        updated_count = 0

        for user_id, current_badges, _ in users_to_update:
            # Remove VOTER badge from the badges array
            updated_badges = [badge for badge in current_badges if badge != Badges.VOTER]

            # Update user's badges
            update_stmt = select(User).where(User.id == user_id)
            user = await session.scalar(update_stmt)
            if user:
                user.badges = updated_badges
                updated_count += 1

        if updated_count > 0:
            logger.info(
                f"Removed voter badges from {updated_count} users who haven't voted in 12+ hours"
            )
        else:
            logger.debug('No expired voter badges found for cleanup')

    async def _cleanup_old_messages(self, session):
        """Delete messages older than 30 days"""
        cutoff_date = datetime.now() - timedelta(days=30)

        # Count messages to be deleted for logging
        count_stmt = select(func.count(col(Message.id))).where(col(Message.createdAt) < cutoff_date)
        count_result = await session.scalar(count_stmt)

        if count_result > 0:
            # First, get IDs of messages to be deleted
            message_ids_stmt = select(Message.id).where(col(Message.createdAt) < cutoff_date)
            message_ids_result = await session.exec(message_ids_stmt)
            message_ids = [row[0] for row in message_ids_result]

            if message_ids:
                # Set referredMessageId to null for any messages that reference the messages being deleted
                await session.exec(
                    update(Message)
                    .where(col(Message.referredMessageId).in_(message_ids))
                    .values(referredMessageId=None)
                )

                # Delete associated broadcasts first
                delete_broadcasts_stmt = delete(Broadcast).where(
                    col(Broadcast.messageId).in_(message_ids)
                )
                await session.exec(delete_broadcasts_stmt)

                # Delete old messages
                delete_stmt = delete(Message).where(col(Message.createdAt) < cutoff_date)
                await session.exec(delete_stmt)

            logger.info(f'Deleted {count_result} messages older than 30 days')
        else:
            logger.debug('No messages older than 30 days found for cleanup')

    @cleanup_data.before_loop
    async def before_cleanup(self):
        await self.bot.wait_until_ready()
        logger.debug('Data cleanup task is ready to start')

    @cleanup_data.error
    async def cleanup_error(self, error):
        logger.error(f'Critical error in data cleanup task scheduler: {error}', exc_info=True)


async def setup(bot: 'Bot'):
    await bot.add_cog(DataCleanup(bot))
