# !! TODO: NEEDS TO BE LOCALISED

import discord
from discord.ext import tasks
from utils.modules.common.cogs import CogBase

from sqlmodel import col, select
from sqlalchemy.orm import joinedload
from datetime import datetime

from utils.utils import ms_to_datetime
from utils.constants import logger, constants
from db.models import HubAnnouncement
from utils.modules.broadcast.announce import broadcast_announcement

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class HubAnnouncements(CogBase):
    def __init__(self, bot):
        self.bot: 'Bot' = bot

    async def cog_load(self) -> None:
        self.send_announcements.start()

    async def cog_unload(self) -> None:
        if self.send_announcements.is_running():
            logger.debug('Task cancelled')
            self.send_announcements.cancel()

    @tasks.loop(seconds=5)
    async def send_announcements(self):
        async with self.bot.db.get_session() as session:
            stmt = (
                select(HubAnnouncement)
                .where(col(HubAnnouncement.nextAnnouncement) <= datetime.now())
                .options(joinedload(HubAnnouncement.hub))  # pyright: ignore[reportArgumentType]
            )
            result = (await session.exec(stmt)).all()

            for announcement in result:
                embed = discord.Embed(
                    title=f'{announcement.hub.name} | Official Hub Announcement',
                    description=announcement.content,
                    color=constants.color,
                )
                embed.set_footer(
                    text='This announcement was sent by hub officials - not InterChat staff'
                )
                await broadcast_announcement(self.bot, announcement.hub, embed)

                announcement.nextAnnouncement = ms_to_datetime(announcement.frequencyMs)
                announcement.previousAnnouncement = datetime.now()
                await session.commit()

    @send_announcements.before_loop
    async def before(self):
        await self.bot.wait_until_ready()


async def setup(bot: 'Bot'):
    await bot.add_cog(HubAnnouncements(bot))
