from discord.ext import commands

from utils.modules.common.cogs import CogBase
from utils.modules.errors.errorHandler import error_handler


class OnCommandError(CogBase):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_command_error(self, ctx: commands.Context, error):
        await error_handler(ctx, error)


async def setup(bot):
    await bot.add_cog(OnCommandError(bot))
