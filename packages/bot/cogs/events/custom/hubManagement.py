from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDecorator import hub_event_listener
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.modules.hub.hubLogging import log_event


class HubManagementEvents(BaseEventCog):
    @hub_event_listener(HubEventType.HUB_CREATE)
    async def on_hub_create(self, event: HubEvent):
        """Handle hub creation events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.HUB_DELETE)
    async def on_hub_delete(self, event: HubEvent):
        """Handle hub deletion events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.HUB_UPDATE)
    async def on_hub_update(self, event: HubEvent):
        """Handle hub update events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(HubManagementEvents(bot))
