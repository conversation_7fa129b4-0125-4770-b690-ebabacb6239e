from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDecorator import hub_event_listener
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.modules.hub.hubLogging import log_event


class ConnectionEvents(BaseEventCog):
    @hub_event_listener(HubEventType.CONNECTION_ADD)
    async def on_connection_add(self, event: HubEvent):
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.CONNECTION_REMOVE)
    async def on_connection_remove(self, event: HubEvent):
        """Handle server disconnection events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(ConnectionEvents(bot))
