from typing import TYPE_CHECKING
import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase

from utils.constants import constants
from db.models import ServerData
from utils.modules.core.i18n import t

if TYPE_CHECKING:
    from main import Bot


class OnGuildJoin(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    @commands.Cog.listener()
    async def on_guild_join(self, guild: discord.Guild):
        jchannel = await self.bot.fetch_channel(1246117516099457146)
        owner = await self.bot.fetch_user(guild.owner_id) if guild.owner_id else None
        owner_name = owner.name if owner else 'unknown'

        dembed = discord.Embed(
            title=f'Joined {guild.name}',
            description=(
                f'> **Name:** {guild.name} (`{guild.id}`)\n'
                f'> **Owner:** @{owner_name} (`{guild.owner_id}`)\n'
                f'> **Guild members:** {guild.member_count}'
            ),
            color=self.bot.constants.color,
        )
        dembed.set_footer(text=f'Total Guilds: {len(self.bot.guilds)}')

        if guild.icon:
            dembed.set_thumbnail(url=guild.icon.url)

        if isinstance(jchannel, (discord.Thread, discord.TextChannel)):
            await jchannel.send(embed=dembed)

        # Store guild in db
        async with self.bot.db.get_session() as session:
            stmt = ServerData(
                id=str(guild.id),
                name=guild.name,
                iconUrl=guild.icon.url if guild.icon else None,
            )
            session.add(stmt)
            await session.commit()

        # Use default 'en' on join; follow-up interactions will use user locale
        embed = discord.Embed(
            title=t('responses.welcome.onGuildJoinTitle', locale='en'),
            description=t(
                'responses.welcome.onGuildJoinDescription',
                locale='en',
                dot=self.bot.emotes.dot,
            ),
            color=constants.color,
        )

        for channel in sorted(guild.text_channels, key=lambda x: x.position):
            everyone = channel.permissions_for(guild.default_role)
            bot = channel.permissions_for(guild.me)

            if everyone.send_messages and bot.send_messages:
                try:
                    await channel.send(embed=embed)
                except Exception:
                    pass
                return


async def setup(bot):
    await bot.add_cog(OnGuildJoin(bot))
