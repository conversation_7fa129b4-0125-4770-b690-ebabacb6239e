import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from typing import TYPE_CHECKING

from sqlmodel import select

from db.models import Connection

if TYPE_CHECKING:
    from main import Bot


class OnGuildRemove(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    @commands.Cog.listener()
    async def on_guild_remove(self, guild: discord.Guild):
        if guild.unavailable:
            return

        # Delete connections on server leave
        async with self.bot.db.get_session() as session:
            stmt = select(Connection).where(Connection.serverId == str(guild.id))
            res = (await session.exec(stmt)).all()

            for conn in res:
                await session.delete(conn)

            await session.commit()

        jchannel = await self.bot.fetch_channel(1246117516099457146)
        owner = await self.bot.fetch_user(guild.owner_id) if guild.owner_id else None
        owner_name = owner.name if owner else 'unknown'

        dembed = discord.Embed(
            title=f'Left {guild.name}',
            description=(
                f'> **Name:** {guild.name} (`{guild.id}`)\n'
                f'> **Owner:** @{owner_name} (`{guild.owner_id}`)\n'
                f'> **Guild members:** {guild.member_count}'
            ),
            color=self.bot.constants.color,
        )
        dembed.set_footer(text=f'Total Guilds: {len(self.bot.guilds)}')

        if guild.icon:
            dembed.set_thumbnail(url=guild.icon.url)

        if jchannel and isinstance(jchannel, discord.Thread):
            try:
                await jchannel.send(embed=dembed)
            except Exception:
                pass


async def setup(bot):
    await bot.add_cog(OnGuildRemove(bot))
