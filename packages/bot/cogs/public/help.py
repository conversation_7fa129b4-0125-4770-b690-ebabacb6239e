import discord
from discord.ext import commands
from discord.ui import View, Select, <PERSON><PERSON>
from typing import TYPE_CHECKING, List, Dict, Optional, Union
from utils.modules.common.cogs import CogBase
from utils.constants import constants
from utils.modules.core.i18n import t
from utils.utils import check_user

if TYPE_CHECKING:
    from main import Bot

COMMANDS_PER_PAGE = 7


class HelpView(View):
    def __init__(
        self,
        bot: 'Bot',
        author: Union[discord.User, discord.Member],
        commands_data: Dict[str, List[commands.Command]],
        locale: str,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.author = author
        self.commands_data = commands_data
        self.locale = locale

        # State management
        self.current_page: int = 0
        self.current_category: Optional[str] = None
        self.current_command: Optional[commands.Command] = None

        self.CATEGORY_EMOJIS: Dict[str, str | discord.Emoji] = {
            'General': self.bot.emotes.house_icon,
            'Moderation': self.bot.emotes.shield_alert_icon,
            'Music': self.bot.emotes.activities,
            'Fun': self.bot.emotes.tada,
            'Utility': self.bot.emotes.hammer_icon,
            'Uncategorized': self.bot.emotes.gear_icon,
            'Default': '⚙️',
        }

        # Create the initial state
        self.initial_embed: discord.Embed = self._create_main_embed()
        self._update_components()

    # --- Embed Creators ---
    def _create_main_embed(self) -> discord.Embed:
        """Creates the initial 'home' embed with a list of categories."""
        embed = discord.Embed(
            title=t('commands.help.title', locale=self.locale),
            description=t('commands.help.description', locale=self.locale),
            color=constants.color,
        )
        # Use display_avatar for a safe way to get the URL
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)
        for category, cmds in self.commands_data.items():
            emoji = self.CATEGORY_EMOJIS.get(category, self.CATEGORY_EMOJIS['Default'])
            embed.add_field(
                name=f'{emoji} {category}',
                value=f'`{len(cmds)}` commands',
                inline=True,
            )
        return embed

    def _create_category_embed(self) -> discord.Embed:
        """Creates an embed for the currently selected category with pagination."""
        if not self.current_category:
            return self._create_main_embed()  # Fallback

        emoji = self.CATEGORY_EMOJIS.get(self.current_category, self.CATEGORY_EMOJIS['Default'])
        embed = discord.Embed(
            title=f'{emoji} {self.current_category} Commands',
            color=constants.color,
        )

        commands_in_category = self.commands_data[self.current_category]
        start_index = self.current_page * COMMANDS_PER_PAGE
        end_index = start_index + COMMANDS_PER_PAGE

        paginated_commands = commands_in_category[start_index:end_index]

        description_parts = []
        for command in paginated_commands:
            # Use short_doc for a concise description, fallback to description
            cmd_desc = command.short_doc or command.description or 'No description available.'
            description_parts.append(f'**/{command.qualified_name}**\n> {cmd_desc}')

        embed.description = '\n'.join(description_parts)
        total_pages = (len(commands_in_category) + COMMANDS_PER_PAGE - 1) // COMMANDS_PER_PAGE
        embed.set_footer(text=f'Page {self.current_page + 1}/{total_pages}')
        return embed

    def _create_command_embed(self) -> discord.Embed:
        """Creates a detailed embed for a single command."""
        command = self.current_command
        if not command:
            return discord.Embed(
                title='Error',
                description='Could not find details for this command.',
                color=discord.Color.red(),
            )

        category = command.extras.get('category', 'Uncategorized')
        emoji = self.CATEGORY_EMOJIS.get(category, self.CATEGORY_EMOJIS['Default'])

        embed = discord.Embed(
            title=f'{emoji} /{command.qualified_name}',
            description=command.description or 'No description available.',
            color=constants.color,
        )

        # Displaying Parameters (Arguments) using the modern `clean_params`
        if command.clean_params:
            usage_parts = [f'`/{command.qualified_name}']
            params_description_parts = []

            for param in command.clean_params.values():
                # A parameter is required if it doesn't have a default value
                is_required = param.required

                usage_parts.append(f'<{param.name}>' if is_required else f'[{param.name}]')

                param_details = param.description or 'No details.'
                req_text = '(Required)' if is_required else '(Optional)'
                params_description_parts.append(f'**{param.name}** {req_text}\n> {param_details}')

            usage_parts.append('`')
            embed.add_field(name='Usage', value=' '.join(usage_parts), inline=False)
            embed.add_field(
                name='Parameters', value='\n'.join(params_description_parts), inline=False
            )

        return embed

    # --- Component Management ---
    def _update_components(self):
        """Dynamically adds/removes buttons and select menus based on the current view."""
        self.clear_items()

        if self.current_command:  # Detailed command view
            self.add_item(self.create_back_button())
            self.add_item(self.create_home_button())
        elif self.current_category:  # Category view
            command_select = self.create_command_select()
            if command_select:
                self.add_item(command_select)
            self.add_pagination_buttons()
            self.add_item(self.create_home_button())
        else:  # Home/Main view
            self.add_item(self.create_category_select())
            home_button = self.create_home_button()
            home_button.disabled = True
            self.add_item(home_button)

    # --- Button and Select Creators ---
    def create_category_select(self) -> Select:
        """Creates a dropdown to select a command category."""
        options = [
            discord.SelectOption(
                label=category,
                value=category,
                emoji=self.CATEGORY_EMOJIS.get(category, self.CATEGORY_EMOJIS['Default']),
            )
            for category in self.commands_data.keys()
        ]
        select_menu = Select(
            placeholder='Select a category to explore...',
            options=options,
            custom_id='category_select',
        )
        select_menu.callback = self.on_category_select
        return select_menu

    def create_command_select(self) -> Optional[Select]:
        """Creates a dropdown for commands on the current page."""
        if not self.current_category:
            return None

        start_index = self.current_page * COMMANDS_PER_PAGE
        end_index = start_index + COMMANDS_PER_PAGE
        commands_on_page = self.commands_data[self.current_category][start_index:end_index]

        if not commands_on_page:
            return None

        options = [
            discord.SelectOption(label=f'/{cmd.qualified_name}', value=cmd.qualified_name)
            for cmd in commands_on_page
        ]
        select_menu = Select(
            placeholder='Select a command for details...',
            options=options,
            custom_id='command_select',
        )
        select_menu.callback = self.on_command_select
        return select_menu

    def create_home_button(self) -> Button:
        button = Button(label='Home', emoji='🏠', style=discord.ButtonStyle.green, custom_id='home')
        button.callback = self.on_home
        return button

    def create_back_button(self) -> Button:
        button = Button(
            label=f'Back to: {self.current_category}',
            emoji='⬅️',
            style=discord.ButtonStyle.blurple,
            custom_id='back',
        )
        button.callback = self.on_back
        return button

    def add_pagination_buttons(self):
        """Adds previous/next page buttons if needed."""
        if not self.current_category:
            return

        commands_in_category = self.commands_data[self.current_category]
        total_pages = (len(commands_in_category) + COMMANDS_PER_PAGE - 1) // COMMANDS_PER_PAGE

        if total_pages <= 1:
            return

        prev_button = Button(
            emoji='◀️',
            style=discord.ButtonStyle.secondary,
            custom_id='prev_page',
            disabled=(self.current_page == 0),
        )
        next_button = Button(
            emoji='▶️',
            style=discord.ButtonStyle.secondary,
            custom_id='next_page',
            disabled=(self.current_page >= total_pages - 1),
        )

        prev_button.callback = self.on_page_change
        next_button.callback = self.on_page_change

        self.add_item(prev_button)
        self.add_item(next_button)

    # --- Callbacks ---
    async def on_category_select(self, interaction: discord.Interaction):
        """Callback for when a user selects a category from the dropdown."""
        values = interaction.data.get('values')
        if not values:
            return

        self.current_category = values[0]
        self.current_command = None
        self.current_page = 0

        embed = self._create_category_embed()
        self._update_components()
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_command_select(self, interaction: discord.Interaction):
        """Callback for when a user selects a command from the dropdown."""
        values = interaction.data.get('values')
        if not values:
            return

        command_name = values[0]
        self.current_command = self.bot.get_command(command_name)

        embed = self._create_command_embed()
        self._update_components()
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_home(self, interaction: discord.Interaction):
        """Callback for the 'Home' button."""
        self.current_category = None
        self.current_command = None
        self.current_page = 0

        self._update_components()
        await interaction.response.edit_message(embed=self.initial_embed, view=self)

    async def on_back(self, interaction: discord.Interaction):
        """Callback for the 'Back' button."""
        self.current_command = None

        embed = self._create_category_embed()
        self._update_components()
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_page_change(self, interaction: discord.Interaction):
        """Callback for the pagination buttons."""
        custom_id = interaction.data.get('custom_id')
        if custom_id == 'prev_page':
            self.current_page = max(0, self.current_page - 1)
        elif custom_id == 'next_page' and self.current_category:
            commands_in_category = self.commands_data[self.current_category]
            total_pages = (len(commands_in_category) + COMMANDS_PER_PAGE - 1) // COMMANDS_PER_PAGE
            self.current_page = min(total_pages - 1, self.current_page + 1)

        embed = self._create_category_embed()
        self._update_components()
        await interaction.response.edit_message(embed=embed, view=self)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Ensures that only the original author can interact with the view."""
        if interaction.user.id != self.author.id:
            await interaction.response.send_message('This is not your help menu!', ephemeral=True)
            return False
        return True


class Help(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    @commands.hybrid_command(name='help', description='📚 Explore all available commands.')
    @check_user()
    async def help(self, ctx: commands.Context):
        """Displays an interactive and paginated help menu."""
        locale: str = await self.get_locale(ctx)

        categorized_commands: Dict[str, List[commands.Command]] = {}
        EXCLUDED_CATEGORIES = {'Group', 'Developer', 'Staff'}

        for command in self.bot.walk_commands():
            if command.hidden:
                continue

            category = command.extras.get('category', 'Uncategorized')
            if category in EXCLUDED_CATEGORIES:
                continue

            # This ensures we don't list subcommands in the main category view
            if command.parent:
                continue

            categorized_commands.setdefault(category, []).append(command)

        # Sort categories by name, then commands within each category by name
        sorted_categories = {
            category: sorted(cmds, key=lambda c: c.name)
            for category, cmds in sorted(categorized_commands.items())
        }

        view = HelpView(self.bot, ctx.author, sorted_categories, locale)
        await ctx.send(embed=view.initial_embed, view=view, ephemeral=True)


async def setup(bot: 'Bot'):
    await bot.add_cog(Help(bot))
