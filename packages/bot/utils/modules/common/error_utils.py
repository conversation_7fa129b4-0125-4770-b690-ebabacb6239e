import logging
from typing import TYPE_CHECKING, Optional, TypeGuard, Union, Any, Dict
from enum import Enum

import discord
from discord.ext import commands

if TYPE_CHECKING:
    from main import Bot


class ErrorSeverity(Enum):
    INFO = 'info'
    WARNING = 'warning'
    ERROR = 'error'
    CRITICAL = 'critical'


class ErrorResponseType(Enum):
    EMBED = 'embed'
    TEXT = 'text'
    EPHEMERAL = 'ephemeral'
    DM = 'dm'


def is_interaction(
    source: discord.Interaction | commands.Context,
) -> TypeGuard[discord.Interaction]:
    return isinstance(source, discord.Interaction)


class ErrorContext:
    def __init__(
        self,
        source: Optional[Union[discord.Interaction['Bot'], commands.Context['Bot']]],
        error: Exception,
        user_message: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        should_log: bool = True,
        should_notify_user: bool = True,
        additional_context: Optional[Dict[str, Any]] = None,
    ):
        self.source = source
        self.error = error
        self.user_message = user_message
        self.severity = severity
        self.should_log = should_log
        self.should_notify_user = should_notify_user
        self.additional_context = additional_context or {}

        # Extract common properties (handle None source for service operations)
        if source:
            self.user = source.user if isinstance(source, discord.Interaction) else source.author
            self.bot = source.client if isinstance(source, discord.Interaction) else source.bot
            self.guild = source.guild
        else:
            self.user = None
            self.bot = None
            self.guild = None


class ResponseBuilder:
    @staticmethod
    def create_error_embed(
        bot: 'Bot',
        message: str,
        title: str = 'Error',
        color: discord.Color = discord.Color.red(),
        include_support_info: bool = True,
    ) -> discord.Embed:
        embed = discord.Embed(
            title=f'{bot.emotes.x_icon} {title}', description=message, color=color
        )

        if include_support_info:
            embed.set_footer(text='Need help? Join our support server!')

        return embed

    @staticmethod
    def create_success_embed(
        bot: 'Bot',
        message: str,
        title: str = 'Success',
        color: discord.Color = discord.Color.green(),
    ) -> discord.Embed:
        embed = discord.Embed(title=f'{bot.emotes.tick} {title}', description=message, color=color)
        return embed

    @staticmethod
    def create_warning_embed(
        bot: 'Bot',
        message: str,
        title: str = 'Warning',
        color: discord.Color = discord.Color.orange(),
    ) -> discord.Embed:
        embed = discord.Embed(
            title=f'{bot.emotes.alert_icon} {title}', description=message, color=color
        )
        return embed


class ErrorLogger:
    """Centralized error logging utilities to eliminate duplicate logging patterns."""

    @staticmethod
    def log_error(
        error: Exception, context: ErrorContext, logger_instance: Optional[logging.Logger] = None
    ) -> None:
        """Log error with standardized format and context."""
        if not logger_instance:
            logger_instance = logging.getLogger('InterChat')

        # Build context information
        context_info = {
            'user_id': str(context.user.id) if context.user else None,
            'guild_id': str(context.guild.id) if context.guild else None,
            'error_type': type(error).__name__,
            'is_interaction': isinstance(context.source, discord.Interaction),
        }

        # Add command context if available
        if not isinstance(context.source, discord.Interaction) and context.source:
            if context.source.command is not None:
                context_info['command'] = context.source.command.qualified_name
        elif isinstance(context.source, discord.Interaction) and hasattr(context.source, 'command'):
            context_info['command'] = getattr(context.source.command, 'qualified_name', 'unknown')

        # Add additional context
        context_info.update(context.additional_context)

        # Log based on severity
        if context.severity == ErrorSeverity.CRITICAL:
            logger_instance.critical(f'Critical error: {error}', extra=context_info, exc_info=True)
        elif context.severity == ErrorSeverity.ERROR:
            logger_instance.error(f'Error: {error}', extra=context_info, exc_info=True)
        elif context.severity == ErrorSeverity.WARNING:
            logger_instance.warning(f'Warning: {error}', extra=context_info)
        else:
            logger_instance.info(f'Info: {error}', extra=context_info)


class ErrorResponseSender:
    """Handles sending error responses to users with consistent patterns."""

    @staticmethod
    async def send_error_response(
        context: ErrorContext,
        response_type: ErrorResponseType = ErrorResponseType.EMBED,
        ephemeral: bool = True,
    ) -> bool:
        """Send error response to user."""
        try:
            if not context.bot:
                return False  # Cannot send response without bot instance

            if response_type == ErrorResponseType.EMBED:
                embed = ResponseBuilder.create_error_embed(
                    context.bot,
                    context.user_message or 'An error occurred. Please try again.',
                    include_support_info=True,
                )
                return await ErrorResponseSender._send_embed_response(context, embed, ephemeral)

            elif response_type == ErrorResponseType.TEXT:
                message = (
                    f'{context.bot.emotes.x_icon} {context.user_message or "An error occurred."}'
                )
                return await ErrorResponseSender._send_text_response(context, message, ephemeral)

            return False

        except Exception as send_error:
            try:
                if not context.bot or not context.user:
                    return False
                embed = ResponseBuilder.create_error_embed(
                    context.bot,
                    "I couldn't respond in the channel. Here's the error message.",
                    include_support_info=True,
                )
                await context.user.send(embed=embed)
                return True
            except Exception:
                # Ultimate fallback: just log the error
                ErrorLogger.log_error(send_error, context)
                return False

    @staticmethod
    async def _send_embed_response(
        context: ErrorContext, embed: discord.Embed, ephemeral: bool = True
    ) -> bool:
        try:
            if context.source and isinstance(context.source, discord.Interaction):
                if not context.source.response.is_done():
                    await context.source.response.send_message(embed=embed, ephemeral=ephemeral)
                else:
                    await context.source.followup.send(embed=embed, ephemeral=ephemeral)
            elif context.source:
                await context.source.send(embed=embed)
            return True
        except discord.Forbidden:
            try:
                if context.user:
                    await context.user.send(embed=embed)
                    return True
            except Exception:
                pass
            return False

    @staticmethod
    async def _send_text_response(
        context: ErrorContext, message: str, ephemeral: bool = True
    ) -> bool:
        try:
            if context.source and isinstance(context.source, discord.Interaction):
                if not context.source.response.is_done():
                    await context.source.response.send_message(message, ephemeral=ephemeral)
                else:
                    await context.source.followup.send(message, ephemeral=ephemeral)
            elif context.source:
                await context.source.send(message)
            return True
        except discord.Forbidden:
            try:
                if context.user:
                    await context.user.send(message)
                    return True
            except Exception:
                pass
            return False


class ErrorHandler:
    @staticmethod
    async def handle_error(
        source: Union[discord.Interaction['Bot'], commands.Context['Bot']],
        error: Exception,
        user_message: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        response_type: ErrorResponseType = ErrorResponseType.EMBED,
        should_log: bool = True,
        should_notify_user: bool = True,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> bool:
        context = ErrorContext(
            source=source,
            error=error,
            user_message=user_message,
            severity=severity,
            should_log=should_log,
            should_notify_user=should_notify_user,
            additional_context=additional_context,
        )

        # Log the error if requested
        if should_log:
            ErrorLogger.log_error(error, context)

        # Notify user if requested
        if should_notify_user:
            return await ErrorResponseSender.send_error_response(context, response_type)

        return True


# Convenience functions
async def handle_command_error(
    ctx: commands.Context['Bot'], error: Exception, user_message: Optional[str] = None
) -> bool:
    return await ErrorHandler.handle_error(
        source=ctx, error=error, user_message=user_message, response_type=ErrorResponseType.EMBED
    )


async def handle_interaction_error(
    interaction: discord.Interaction['Bot'],
    error: Exception,
    user_message: Optional[str] = None,
    ephemeral: bool = True,
) -> bool:
    """Handle interaction error with standard patterns."""
    return await ErrorHandler.handle_error(
        source=interaction,
        error=error,
        user_message=user_message,
        response_type=ErrorResponseType.EMBED if ephemeral else ErrorResponseType.TEXT,
    )


async def send_error_message(
    source: Union[discord.Interaction['Bot'], commands.Context['Bot']],
    message: str,
    title: str = 'Error',
    ephemeral: bool = True,
) -> bool:
    bot = source.client if isinstance(source, discord.Interaction) else source.bot
    embed = ResponseBuilder.create_error_embed(bot, message, title)

    # Send directly without creating dummy exception
    try:
        if isinstance(source, discord.Interaction):
            if not source.response.is_done():
                await source.response.send_message(embed=embed, ephemeral=ephemeral)
            else:
                await source.followup.send(embed=embed, ephemeral=ephemeral)
        else:
            await source.send(embed=embed)
        return True
    except discord.Forbidden:
        # Try DM fallback
        try:
            user = source.user if isinstance(source, discord.Interaction) else source.author
            await user.send(embed=embed)
            return True
        except Exception:
            return False


async def send_success_message(
    source: Union[discord.Interaction['Bot'], commands.Context['Bot']],
    message: str,
    title: str = 'Success',
    ephemeral: bool = True,
) -> bool:
    bot = source.client if isinstance(source, discord.Interaction) else source.bot
    embed = ResponseBuilder.create_success_embed(bot, message, title)

    try:
        if isinstance(source, discord.Interaction):
            if not source.response.is_done():
                await source.response.send_message(embed=embed, ephemeral=ephemeral)
            else:
                await source.followup.send(embed=embed, ephemeral=ephemeral)
        else:
            await source.send(embed=embed)
        return True
    except discord.Forbidden:
        # DM fallback
        try:
            user = source.user if isinstance(source, discord.Interaction) else source.author
            await user.send(embed=embed)
            return True
        except Exception:
            return False
