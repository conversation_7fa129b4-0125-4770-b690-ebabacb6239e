from typing import TYPE_CHECKING, Any, Awaitable, Callable, Optional, TypeVar

from sqlmodel import select, col
from sqlalchemy.orm import selectinload

from db.database import get_db
from db.models import Broadcast, Connection, Hub, Message, ServerData, User

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession


class DatabaseQueries:
    # ===== USER QUERIES =====

    @staticmethod
    async def fetch_user_by_id(session: 'AsyncSession', user_id: str):
        stmt = select(User).where(User.id == user_id)
        return await session.scalar(stmt)

    @staticmethod
    async def fetch_user_locale(session: 'AsyncSession', user_id: str) -> Optional[str]:
        stmt = select(User.locale).where(User.id == user_id)
        return await session.scalar(stmt)

    @staticmethod
    async def check_user_exists(session: 'AsyncSession', user_id: str) -> bool:
        stmt = select(User.id).where(User.id == user_id)
        return (await session.scalar(stmt)) is not None

    @staticmethod
    async def fetch_user_badges(session: 'AsyncSession', user_id: str):
        stmt = select(User.badges, User.showBadges).where(User.id == user_id).limit(1)
        return await session.scalar(stmt)

    # ===== HUB QUERIES =====

    @staticmethod
    async def fetch_hub_by_id(session: 'AsyncSession', hub_id: str):
        stmt = select(Hub).where(Hub.id == hub_id)
        return await session.scalar(stmt)

    @staticmethod
    async def fetch_hub_with_moderators(session: 'AsyncSession', hub_id: str):
        """Fetch hub with moderators preloaded."""
        stmt = select(Hub).where(Hub.id == hub_id).options(selectinload(Hub.moderators))
        return await session.scalar(stmt)

    @staticmethod
    async def fetch_hub_by_name(
        session: 'AsyncSession', hub_name: str, include_private: bool = False
    ):
        """Fetch hub by name."""
        conditions = [Hub.name == hub_name]
        if not include_private:
            conditions.append(col(Hub.private).is_(False))
        stmt = select(Hub).where(*conditions)
        return await session.scalar(stmt)

    @staticmethod
    async def fetch_public_hubs(session: 'AsyncSession'):
        stmt = select(Hub).where(col(Hub.private).is_(False))
        return (await session.exec(stmt)).all()

    # ===== SERVER QUERIES =====

    @staticmethod
    async def fetch_server_by_id(session: 'AsyncSession', server_id: str):
        """Fetch server by ID."""
        stmt = select(ServerData).where(ServerData.id == server_id)
        return await session.scalar(stmt)

    # ===== CONNECTION QUERIES =====

    @staticmethod
    async def fetch_connection_by_channel(session: 'AsyncSession', channel_id: str):
        """Fetch connection by channel ID."""
        stmt = select(Connection).where(Connection.channelId == channel_id)
        return await session.scalar(stmt)

    @staticmethod
    async def fetch_connections_by_server(session: 'AsyncSession', server_id: str):
        """Fetch all connections for a server with hub names."""
        stmt = (
            select(Connection, Hub.name)
            .join(Hub, Hub.id == Connection.hubId)
            .where(Connection.serverId == server_id)
        )
        return (await session.exec(stmt)).all()

    @staticmethod
    async def fetch_hub_connections(
        session: 'AsyncSession',
        hub_id: str,
        exclude_channel_id: Optional[str] = None,
        connected_only: bool = True,
    ):
        """Fetch all connections in a hub, optionally excluding a specific channel."""
        conditions = [Connection.hubId == hub_id]
        if connected_only:
            conditions.append(col(Connection.connected).is_(True))
        if exclude_channel_id:
            conditions.append(Connection.channelId != exclude_channel_id)

        stmt = select(Connection).where(*conditions)
        return (await session.exec(stmt)).all()

    # ===== MESSAGE QUERIES =====

    @staticmethod
    async def fetch_message_by_id(session: 'AsyncSession', message_id: str):
        """Fetch message by ID."""
        stmt = select(Message).where(Message.id == message_id)
        return await session.scalar(stmt)

    @staticmethod
    async def fetch_message_with_broadcast_fallback(session: 'AsyncSession', message_id: str):
        """Fetch message by ID, falling back to broadcast lookup if not found directly."""
        # First try direct lookup
        message = await DatabaseQueries.fetch_message_by_id(session, message_id)
        if message:
            return message

        # Fallback to broadcast lookup
        stmt = (
            select(Message)
            .join(Broadcast, Message.id == Broadcast.messageId)
            .where(Broadcast.id == message_id)
        )
        return await session.scalar(stmt)

    @staticmethod
    async def fetch_message_with_user_and_hub(session: 'AsyncSession', message_id: str):
        """Fetch message with user and hub data"""
        # First try direct lookup
        stmt = (
            select(Message, User, Hub.id)
            .join(User, Message.authorId == User.id)
            .join(Hub, Message.hubId == Hub.id)
            .where(Message.id == message_id)
        )
        result = (await session.exec(stmt)).first()
        if result:
            return result

        # Fallback to broadcast lookup
        stmt = (
            select(Message, User, Hub.id)
            .join(Broadcast, Message.id == Broadcast.messageId)
            .join(User, Message.authorId == User.id)
            .join(Hub, Message.hubId == Hub.id)
            .where(Broadcast.id == message_id)
        )
        return (await session.exec(stmt)).first()


R = TypeVar('R')


class DatabaseSessionManager:
    @staticmethod
    async def with_session(
        operation: Callable[['AsyncSession', Any], Awaitable[R]],
        *args: Any,
        **kwargs: Any,
    ) -> R:
        async with get_db().get_session() as session:
            return await operation(session, *args, **kwargs)

    @staticmethod
    async def fetch_with_session(
        query_func: Callable[['AsyncSession', Any], Awaitable[R]],
        *args: Any,
        **kwargs: Any,
    ) -> R:
        async with get_db().get_session() as session:
            return await query_func(session, *args, **kwargs)

    @staticmethod
    async def execute_with_session(
        operation_func: Callable[['AsyncSession', Any], Awaitable[R]],
        *args: Any,
        **kwargs: Any,
    ) -> R:
        async with get_db().get_session() as session:
            result = await operation_func(session, *args, **kwargs)
            await session.commit()
            return result


# Convenience functions that combine session management with common queries
class DatabaseUtils:
    @staticmethod
    async def get_user(user_id: str):
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_user_by_id, user_id
        )

    @staticmethod
    async def get_user_locale(user_id: str) -> str:
        locale = await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_user_locale, user_id
        )
        return locale or 'en'

    @staticmethod
    async def user_exists(user_id: str) -> bool:
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.check_user_exists, user_id
        )

    @staticmethod
    async def get_hub(hub_id: str):
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_hub_by_id, hub_id
        )

    @staticmethod
    async def get_server(server_id: str):
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_server_by_id, server_id
        )

    @staticmethod
    async def get_connection_by_channel(channel_id: str):
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_connection_by_channel, channel_id
        )

    @staticmethod
    async def get_server_connections(server_id: str):
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_connections_by_server, server_id
        )

    @staticmethod
    async def get_message_with_fallback(message_id: str):
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_message_with_broadcast_fallback, message_id
        )

    @staticmethod
    async def get_message_with_context(message_id: str):
        return await DatabaseSessionManager.fetch_with_session(
            DatabaseQueries.fetch_message_with_user_and_hub, message_id
        )
