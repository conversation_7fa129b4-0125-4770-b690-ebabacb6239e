import re
from datetime import datetime, timedelta
from typing import Op<PERSON>, <PERSON><PERSON>, Union
from urllib.parse import urlparse


class ValidationResult:
    def __init__(
        self,
        is_valid: bool,
        error_message: Optional[str] = None,
        field_name: Optional[str] = None,
        error_code: Optional[str] = None,
    ):
        self.is_valid = is_valid
        self.error_message = error_message
        self.field_name = field_name
        self.error_code = error_code

    def __bool__(self) -> bool:
        return self.is_valid

    @classmethod
    def success(cls) -> 'ValidationResult':
        """Create a successful validation result."""
        return cls(is_valid=True)

    @classmethod
    def error(
        cls, message: str, field_name: Optional[str] = None, error_code: Optional[str] = None
    ) -> 'ValidationResult':
        """Create a failed validation result."""
        return cls(
            is_valid=False, error_message=message, field_name=field_name, error_code=error_code
        )


class TextValidators:
    """Common text validation utilities."""

    # Common regex patterns
    HUB_NAME_PATTERN = re.compile(r'^[\w \-]{2,50}$', re.IGNORECASE)
    DISCORD_ID_PATTERN = re.compile(r'^\d{17,20}$')

    @staticmethod
    def validate_length(
        text: str, min_length: int = 0, max_length: int = 2000, field_name: str = 'text'
    ) -> ValidationResult:
        """Validate text length"""
        if not text:
            text = ''

        length = len(text)

        if length < min_length:
            return ValidationResult.error(
                f'{field_name} must be at least {min_length} characters long',
                field_name=field_name,
                error_code='TOO_SHORT',
            )

        if length > max_length:
            return ValidationResult.error(
                f'{field_name} must be no more than {max_length} characters long',
                field_name=field_name,
                error_code='TOO_LONG',
            )

        return ValidationResult.success()

    @staticmethod
    def validate_not_empty(text: str, field_name: str = 'text') -> ValidationResult:
        """Validate that text is not empty or whitespace only."""
        if not text or not text.strip():
            return ValidationResult.error(
                f'{field_name} cannot be empty', field_name=field_name, error_code='EMPTY'
            )
        return ValidationResult.success()

    @staticmethod
    def validate_hub_name(name: str) -> ValidationResult:
        """Validate hub name format"""
        if not name or not name.strip():
            return ValidationResult.error(
                'Hub name cannot be empty', field_name='hub_name', error_code='EMPTY'
            )

        if not TextValidators.HUB_NAME_PATTERN.match(name):
            return ValidationResult.error(
                'Hub name must contain only letters, numbers, spaces, and hyphens (2-50 characters)',
                field_name='hub_name',
                error_code='INVALID_FORMAT',
            )

        return ValidationResult.success()

    @staticmethod
    def validate_discord_id(discord_id: str) -> ValidationResult:
        """Validate Discord ID format"""
        if not discord_id:
            return ValidationResult.error(
                'Discord ID cannot be empty', field_name='discord_id', error_code='EMPTY'
            )

        if not TextValidators.DISCORD_ID_PATTERN.match(discord_id):
            return ValidationResult.error(
                'Invalid Discord ID format', field_name='discord_id', error_code='INVALID_FORMAT'
            )

        return ValidationResult.success()

    @staticmethod
    def sanitize_webhook_username(name: str) -> str:
        """
        Sanitize username for Discord webhooks.
        """
        if not name or not name.strip():
            return 'Unknown User'

        sanitized = name
        forbidden_terms = [
            (r'discord', '****'),
            (r'```', '***'),
            (r'clyde', '****'),
            (r'everyone', '****'),
            (r'here', '****'),
        ]

        for pattern, replacement in forbidden_terms:
            sanitized = re.sub(pattern, replacement, sanitized, flags=re.IGNORECASE)

        sanitized = re.sub(r'\s+', ' ', sanitized).strip()

        # Fallback if the name is now empty or only contains censored characters
        if not sanitized or re.match(r'^[\*\s]*$', sanitized):
            sanitized = 'User'

        if len(sanitized) > 80:
            sanitized = sanitized[:80].rstrip()

        return sanitized

    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = '...') -> str:
        """Safely truncate text with proper suffix handling."""
        if not text:
            return ''

        if len(text) <= max_length:
            return text

        return text[: max_length - len(suffix)] + suffix


class MessageValidators:
    """Message-specific validation utilities."""

    @staticmethod
    def validate_message_length(content: str, max_length: int = 2000) -> ValidationResult:
        """Validate message content length"""
        if not content:
            content = ''

        if len(content) > max_length:
            return ValidationResult.error(
                f'Message exceeds {max_length} character limit ({len(content)} characters)',
                field_name='message_content',
                error_code='TOO_LONG',
            )

        return ValidationResult.success()

    @staticmethod
    def validate_account_age(created_at: datetime, min_age_days: int = 7) -> ValidationResult:
        """Validate account age"""
        account_age = datetime.now(created_at.tzinfo) - created_at

        if account_age < timedelta(days=min_age_days):
            return ValidationResult.error(
                f'Account must be at least {min_age_days} days old',
                field_name='account_age',
                error_code='TOO_NEW',
            )

        return ValidationResult.success()


class URLValidators:
    """URL validation utilities."""

    # Precompiled patterns for performance
    IMAGE_EXTENSIONS = frozenset(['.png', '.webp', '.jpg', '.jpeg'])
    GIF_EXTENSION = '.gif'
    TENOR_DOMAIN = 'tenor.com'

    @staticmethod
    def is_valid_url(url: str) -> bool:
        """Check if URL is valid."""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except (ValueError, AttributeError):
            return False

    @staticmethod
    def is_image_url(url: str) -> bool:
        """Check if URL is an image (png, webp, jpg, jpeg)."""
        try:
            parsed_url = urlparse(url.lower())
            return any(parsed_url.path.endswith(ext) for ext in URLValidators.IMAGE_EXTENSIONS)
        except (ValueError, AttributeError):
            return False

    @staticmethod
    def is_gif_url(url: str) -> bool:
        """Check if URL is a GIF."""
        try:
            parsed_url = urlparse(url.lower())
            return parsed_url.path.endswith(URLValidators.GIF_EXTENSION)
        except (ValueError, AttributeError):
            return False

    @staticmethod
    def is_tenor_gif(url: str) -> bool:
        """Check if URL is a Tenor GIF."""
        try:
            parsed_url = urlparse(url.lower())
            return URLValidators.TENOR_DOMAIN in parsed_url.netloc
        except (ValueError, AttributeError):
            return False


class DurationValidators:
    """Duration parsing and validation utilities."""

    DURATION_UNITS = {
        's': 1000,
        'sec': 1000,
        'second': 1000,
        'm': 60000,
        'min': 60000,
        'minute': 60000,
        'h': 3600000,
        'hour': 3600000,
        'd': ********,
        'day': ********,
        'w': 604800000,
        'week': 604800000,
        'mo': 2592000000,
        'month': 2592000000,
        'y': 31536000000,
        'year': 31536000000,
    }

    @staticmethod
    def parse_duration(duration_str: str) -> Tuple[bool, Union[int, str]]:
        """
        Parse duration string to milliseconds.
        Returns (success, result) where result is int on success, error message on failure.
        """
        if not duration_str or not duration_str.strip():
            return False, 'Duration cannot be empty'

        total = 0
        for match in re.finditer(r'(\d+)\s*([a-z]+)', duration_str.lower()):
            num, unit = match.groups()
            # Handle plurals by removing 's'
            unit = unit.rstrip('s')
            if unit in DurationValidators.DURATION_UNITS:
                total += int(num) * DurationValidators.DURATION_UNITS[unit]
            else:
                return False, f'Unknown time unit: {unit}'

        if total <= 0:
            return False, 'Invalid duration format'

        return True, total

    @staticmethod
    def validate_duration_range(
        duration_ms: int,
        min_ms: int = 60000,  # 1 minute
        max_ms: int = 31536000000,  # 1 year
    ) -> ValidationResult:
        """Validate duration is within acceptable range."""
        if duration_ms < min_ms:
            return ValidationResult.error(
                f'Duration must be at least {min_ms // 1000} seconds',
                field_name='duration',
                error_code='TOO_SHORT',
            )

        if duration_ms > max_ms:
            return ValidationResult.error(
                f'Duration cannot exceed {max_ms // ********} days',
                field_name='duration',
                error_code='TOO_LONG',
            )

        return ValidationResult.success()


# Convenience functions for backward compatibility
def is_message_too_long(content: str, max_length: int = 1800) -> bool:
    """Backward compatibility function - delegates to MessageValidators."""
    return not MessageValidators.validate_message_length(content, max_length).is_valid


def is_account_too_new(created_at: datetime, min_age_days: int = 7) -> bool:
    """Backward compatibility function - delegates to MessageValidators."""
    return not MessageValidators.validate_account_age(created_at, min_age_days).is_valid


def validate_discord_id(discord_id: str) -> bool:
    """Backward compatibility function - delegates to TextValidators."""
    return TextValidators.validate_discord_id(discord_id).is_valid
