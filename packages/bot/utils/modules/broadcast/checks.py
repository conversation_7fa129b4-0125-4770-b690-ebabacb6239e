import asyncio
from datetime import datetime
from typing import TYPE_CHECKING, List, NamedTuple, Optional, Tuple

import aiohttp
import discord
from sqlmodel import and_, literal, or_, select, union_all, col

from utils.constants import constants, logger
from utils.modules.broadcast.messageUtils import is_gif_url, is_image_url
from utils.modules.common.validation_utils import MessageValidators
from utils.modules.core.antiSpam import AntiSpamManager
from utils.modules.core.cache import cache_manager, validation_cache
from db.models import (
    Blacklist,
    Hub,
    Infraction,
    InfractionStatus,
    InfractionType,
    ServerBlacklist,
)
from utils.modules.core.hub.SettingsBitField import HubSettingsBitField
from utils.modules.core.profanity.filter_service import ProfanityFilterService
from utils.modules.events.eventDispatcher import (
    HubEventType,
    create_hub_event,
    event_dispatcher,
)

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession


class ValidationResult(NamedTuple):
    """Result of message validation checks"""

    is_valid: bool
    reason: Optional[str] = None
    should_notify: bool = False
    notification_message: Optional[str] = None
    infraction_id: Optional[str] = None
    spam_action: Optional[str] = None  # 'warn' or 'mute' for spam-related actions


class MessageValidator:
    """Handles all message validation checks before broadcasting"""

    def __init__(self, session: 'AsyncSession'):
        self.session = session
        self._profanity_service = ProfanityFilterService(
            self.session, client_id=str(constants.client_id)
        )

    async def validate_message(
        self,
        message: discord.Message,
        hub: Hub,
        user_id: str,
        guild_id: str,
    ) -> ValidationResult:
        """
        Perform all validation checks for a message before broadcasting.
        Returns ValidationResult with validation status and notification details.
        """
        start_time = datetime.now()

        logger.debug(
            f'Starting validation for message {message.id} from user {user_id} '
            f'in guild {guild_id}, hub {hub.id} ({hub.name})'
        )

        # Run validation checks in order of performance (fastest first).
        validation_steps = [
            ('message_length', lambda: self._validate_message_length(message)),
            ('hub_status', lambda: self._validate_hub_status(hub)),
            ('account_age', lambda: self._validate_account_age(message.author)),
            ('spam_check', lambda: self._validate_spam(user_id, hub.id)),
            ('batch_validation', lambda: self._batch_validation_check(user_id, guild_id, hub.id)),
            ('profanity_filter', lambda: self._validate_profanity(message, hub)),
            ('nsfw_detection', lambda: self._validate_nsfw_images(message, hub)),
        ]

        # Execute checks and return first failure
        for check_name, run_check in validation_steps:
            logger.debug(f'Running {check_name} validation for message {message.id}')
            result = await run_check()
            if not result.is_valid:
                logger.info(
                    f'Message {message.id} failed {check_name} validation: {result.reason} '
                    f'(should_notify={result.should_notify})'
                )
                return result
            logger.debug(f'{check_name} validation passed for message {message.id}')

        # Log performance
        elapsed = (datetime.now() - start_time).total_seconds() * 1000
        logger.debug(
            f'Message validation completed successfully in {elapsed:.2f}ms for message {message.id} '
            f'from user {user_id} in hub {hub.id}'
        )

        return ValidationResult(is_valid=True)

    async def _validate_profanity(self, message: discord.Message, hub: Hub) -> ValidationResult:
        """Run profanity filter against the message content.

        - Uses ProfanityFilterService with per-hub cached rules
        - Executes configured actions (warn/mute/ban/alert) via service
        - Blocks message if BLOCK_MESSAGE is triggered by any rule
        """
        try:
            result = await self._profanity_service.check_message(
                message=message.content or '',
                hub_id=hub.id,
                user_id=str(message.author.id),
                channel_id=str(message.channel.id),
                message_id=str(message.id),
            )
        except Exception as e:
            # Fail-open on profanity service errors
            logger.error(f'Profanity filter error: {e}', exc_info=True)
            return ValidationResult(is_valid=True)

        if not result.blocked:
            return ValidationResult(is_valid=True)

        # Notify user minimally (localization can refine later)
        return ValidationResult(
            is_valid=False,
            reason='Profanity filter blocked message',
            should_notify=True,
            notification_message='🚫 Your message was blocked by InterChat filters (Reason: Profanity)'.strip(),
        )

    async def _validate_nsfw_images(self, message: discord.Message, hub: Hub) -> ValidationResult:
        """Detect NSFW content in static images and stickers via local detection service.

        - Extracts URLs from attachments, embeds, and static stickers (PNG)
        - Calls POST to NSFW detection API
        - If any is_nsfw true, blocks message and emits NSFW_DETECTED event
        - On detector errors or rate limiting, default allow (graceful degrade)
        """
        hub_settings = HubSettingsBitField(hub.settings)
        if hub.nsfw and not hub_settings.has('BlockNSFW'):
            return ValidationResult(is_valid=True)

        try:
            urls = self._extract_static_image_urls(message)
        except Exception as e:
            logger.error(f'NSFW detection: failed to extract URLs: {e}', exc_info=True)
            return ValidationResult(is_valid=True)

        if not urls:
            return ValidationResult(is_valid=True)

        # Basic rate-limit to avoid overwhelming the detector: 5 checks/user/minute
        try:
            rl_count = await cache_manager.increment('rate_limit', 'nsfw', str(message.author.id))
            if rl_count == 1:
                await cache_manager.expire('rate_limit', 'nsfw', str(message.author.id), ttl=60)
            if rl_count and rl_count > 5:
                logger.debug(
                    f'NSFW detection skipped due to rate limit for user {message.author.id}'
                )
                return ValidationResult(is_valid=True)
        except Exception as e:
            # If cache/redis unavailable, don't block message
            logger.warning(f'NSFW detection rate-limit failed: {e}')

        # Allow detection to be disabled via env
        if not constants.enable_nsfw_detection:
            return ValidationResult(is_valid=True)

        timeout = aiohttp.ClientTimeout(total=4)
        payload = {'urls': urls}
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(constants.nsfw_detector_url, json=payload) as resp:
                    if resp.status != 200:
                        logger.warning(
                            f'NSFW detection service returned HTTP {resp.status}; allowing message'
                        )
                        return ValidationResult(is_valid=True)
                    data = await resp.json()
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logger.warning(f'NSFW detection service unavailable: {e}; allowing message')
            return ValidationResult(is_valid=True)
        except Exception as e:
            logger.error(f'NSFW detection unexpected error: {e}', exc_info=True)
            return ValidationResult(is_valid=True)

        # Parse response and check flags
        try:
            nsfw_results = [
                {
                    'url': item.get('url'),
                    'is_nsfw': bool(item.get('is_nsfw')),
                    'confidence_percentage': float(item.get('confidence_percentage', 0)),
                }
                for item in (data or [])
                if isinstance(item, dict)
            ]
        except Exception as e:
            logger.error(f'NSFW detection: failed to parse response: {e}', exc_info=True)
            return ValidationResult(is_valid=True)

        detected = [
            r
            for r in nsfw_results
            if r.get('is_nsfw') and r.get('confidence_percentage', 0) >= 70.0
        ]
        if not detected:
            return ValidationResult(is_valid=True)

        # Emit hub event for NSFW detection
        try:
            event = create_hub_event(
                event_type=HubEventType.NSFW_DETECTED,
                hub_id=hub.id,
                hub_name=hub.name,
                target_user_id=str(message.author.id),
                target_user_name=str(message.author.name),
                message_id=str(message.id),
                channel_id=str(message.channel.id),
                extra_data={
                    'detected_urls': [
                        {'url': r['url'], 'confidence_percentage': r['confidence_percentage']}
                        for r in detected
                    ],
                    'guild_id': str(message.guild.id) if message.guild else None,
                },
            )
            await event_dispatcher.dispatch_hub_event(event)
        except Exception as e:
            logger.error(f'Failed to dispatch NSFW_DETECTED event: {e}', exc_info=True)

        logger.info(
            f'NSFW content detected for user {message.author.id} in hub {hub.id}; blocking broadcast'
        )
        return ValidationResult(
            is_valid=False,
            should_notify=True,
            notification_message='🚫 Your message was blocked by InterChat filters (Reason: NSFW detection)',
        )

    def _extract_static_image_urls(self, message: discord.Message) -> List[str]:
        """Extract static image URLs (png, jpg, jpeg, webp) and static stickers from message."""
        urls: List[str] = []
        urls.extend(self._image_urls_from_attachments(message))
        urls.extend(self._image_urls_from_embeds(message))
        urls.extend(self._image_urls_from_stickers(message))
        # Deduplicate while preserving order
        seen = set()
        deduped: List[str] = []
        for u in urls:
            if u not in seen:
                deduped.append(u)
                seen.add(u)
        return deduped

    def _image_urls_from_attachments(self, message: discord.Message) -> List[str]:
        urls: List[str] = []
        for att in getattr(message, 'attachments', []) or []:
            url = getattr(att, 'url', None)
            if url and not is_gif_url(url) and is_image_url(url):
                urls.append(url)
        return urls

    def _image_urls_from_embeds(self, message: discord.Message) -> List[str]:
        urls: List[str] = []
        for emb in getattr(message, 'embeds', []) or []:
            for attr in ('image', 'thumbnail'):
                part = getattr(emb, attr, None)
                u = getattr(part, 'url', None) if part else None
                if u and not is_gif_url(u) and is_image_url(u):
                    urls.append(u)
        return urls

    def _image_urls_from_stickers(self, message: discord.Message) -> List[str]:
        urls: List[str] = []
        for sticker in getattr(message, 'stickers', []) or []:
            try:
                if (
                    hasattr(discord, 'StickerFormatType')
                    and getattr(sticker, 'format', None) == discord.StickerFormatType.png
                ):
                    u = getattr(sticker, 'url', None)
                    if u:
                        urls.append(u)
            except Exception:
                continue
        return urls

    async def _validate_message_length(self, message: discord.Message) -> ValidationResult:
        """Check if message length is within limits."""
        await asyncio.sleep(0)

        validation_result = MessageValidators.validate_message_length(message.content or '')
        if not validation_result.is_valid:
            logger.info(
                f'Message blocked: exceeds 1800 characters ({len(message.content or "")} chars)'
            )
            return ValidationResult(is_valid=False, reason=validation_result.error_message)

        return ValidationResult(is_valid=True)

    async def _validate_hub_status(self, hub: Hub) -> ValidationResult:
        """Check if hub is locked."""
        await asyncio.sleep(0)
        if hub.locked:
            logger.info(f'Message blocked: hub {hub.id} ({hub.name}) is locked')
            return ValidationResult(is_valid=False, reason='Hub is currently locked')

        logger.debug(f'Hub {hub.id} ({hub.name}) status check passed: unlocked')
        return ValidationResult(is_valid=True)

    async def _validate_account_age(
        self, author: discord.User | discord.Member
    ) -> ValidationResult:
        """Check if account meets minimum age requirement."""
        await asyncio.sleep(0)

        validation_result = MessageValidators.validate_account_age(author.created_at)
        if not validation_result.is_valid:
            return ValidationResult(
                is_valid=False,
                reason='Account too new',
                should_notify=True,
                notification_message=(
                    'Your account is too new to send messages through InterChat. '
                    'Please wait a few more days and try again.'
                ),
            )

        return ValidationResult(is_valid=True)

    async def _validate_spam(self, user_id: str, hub_id: str) -> ValidationResult:
        """Check for spam using anti-spam manager."""
        logger.debug(f'Running spam check for user {user_id} in hub {hub_id}')

        spam_manager = AntiSpamManager(self.session)
        spam_result = await spam_manager.check_spam(user_id, hub_id)

        if spam_result.is_spam:
            logger.info(
                f'Spam detected for user {user_id} in hub {hub_id}: action={spam_result.action_needed}, '
                f'reason={spam_result.warning_message}'
            )
            return ValidationResult(
                is_valid=False,
                reason='Spam detected',
                should_notify=True,
                notification_message=spam_result.warning_message,
                spam_action=spam_result.action_needed,
            )

        logger.debug(f'Spam check passed for user {user_id} in hub {hub_id}')
        return ValidationResult(is_valid=True)

    async def _batch_validation_check(
        self, user_id: str, guild_id: str, hub_id: str
    ) -> ValidationResult:
        logger.debug(
            f'Running batch validation check for user {user_id}, guild {guild_id}, hub {hub_id}'
        )

        # Check cache first
        cache_key = self._get_cache_key(user_id, guild_id, hub_id)
        cached_result = await self._get_validation_cache(cache_key)
        if cached_result:
            logger.debug(
                f'Found cached validation result for user {user_id} in hub {hub_id}: '
                f'valid={cached_result.is_valid}'
            )
            return cached_result

        now = datetime.now()
        logger.debug(f'No cache found, running database queries for user {user_id} in hub {hub_id}')

        # Single unified query using UNION ALL for all validation checks
        # to reduce database round trips from 4 to 1
        user_infraction_query = select(
            Infraction.id,
            Infraction.type,
            Infraction.reason,
            Infraction.expiresAt,
            Infraction.notified,
            literal('user_infraction').label('check_type'),
        ).where(
            and_(
                Infraction.userId == user_id,
                Infraction.hubId == hub_id,
                Infraction.status == InfractionStatus.ACTIVE,
                or_(
                    Infraction.type == InfractionType.BAN,
                    Infraction.type == InfractionType.MUTE,
                    Infraction.type == InfractionType.BLACKLIST,
                ),
                or_(col(Infraction.expiresAt).is_(None), Infraction.expiresAt > now),
            )
        )

        server_infraction_query = select(
            Infraction.id,
            Infraction.type,
            Infraction.reason,
            Infraction.expiresAt,
            Infraction.notified,
            literal('server_infraction').label('check_type'),
        ).where(
            and_(
                Infraction.serverId == guild_id,
                Infraction.hubId == hub_id,
                Infraction.status == InfractionStatus.ACTIVE,
                or_(
                    Infraction.type == InfractionType.BAN,
                    Infraction.type == InfractionType.MUTE,
                    Infraction.type == InfractionType.BLACKLIST,
                ),
                or_(col(Infraction.expiresAt).is_(None), Infraction.expiresAt > now),
            )
        )

        user_blacklist_query = select(
            Blacklist.id,
            literal(None).label('type'),  # Blacklist doesn't have type
            Blacklist.reason,
            Blacklist.expiresAt,
            literal(None).label('notified'),  # Add missing notified column
            literal('user_blacklist').label('check_type'),
        ).where(
            and_(
                Blacklist.userId == user_id,
                or_(col(Blacklist.expiresAt).is_(None), Blacklist.expiresAt > now),
            )
        )

        server_blacklist_query = select(
            ServerBlacklist.id,
            literal(None).label('type'),
            ServerBlacklist.reason,
            ServerBlacklist.expiresAt,
            literal(None).label('notified'),  # Add missing notified column
            literal('server_blacklist').label('check_type'),
        ).where(
            and_(
                ServerBlacklist.serverId == guild_id,
                or_(col(ServerBlacklist.expiresAt).is_(None), ServerBlacklist.expiresAt > now),
            )
        )

        # Combine all queries with UNION ALL and limit to first result
        combined_query = union_all(
            user_infraction_query,
            server_infraction_query,
            user_blacklist_query,
            server_blacklist_query,
        ).limit(1)

        # Execute single query
        result = (await self.session.exec(combined_query)).first()

        # Process result
        if result:
            logger.debug(
                f'Found violation in batch validation for user {user_id} in hub {hub_id}: '
                f'type={result.check_type}, infraction_id={result.id}'
            )
            violation_result = self._process_violation(result.tuple(), user_id, guild_id, hub_id)
            # Cache negative results for shorter time (30 seconds)
            await self._cache_validation_result(cache_key, violation_result, ttl=30)
            return violation_result

        # All checks passed - cache positive result for longer (2 minutes)
        logger.debug(f'Batch validation passed for user {user_id} in hub {hub_id}')
        success_result = ValidationResult(is_valid=True)
        await self._cache_validation_result(cache_key, success_result, ttl=120)
        return success_result

    def _process_violation(
        self,
        result: Tuple[str, InfractionType, str, datetime | None, bool, str],
        user_id: str,
        guild_id: str,
        hub_id: str,
    ) -> ValidationResult:
        """Process a validation violation result."""
        infraction_id, _, reason, expires_at, notified, check_type = result

        if check_type == 'user_infraction':
            if expires_at is None:
                logger.info(f'Message blocked: user {user_id} permanently banned from hub {hub_id}')
                return ValidationResult(
                    is_valid=False,
                    reason='User is banned from this hub',
                    should_notify=not notified,
                    notification_message=f'You are banned from this hub. Reason: {reason} You can appeal this decision using /appeal.',
                    infraction_id=infraction_id,
                )
            else:
                logger.info(
                    f'Message blocked: user {user_id} temporarily banned from hub {hub_id} until {expires_at}'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='User is temporarily banned from this hub',
                    should_notify=not notified,
                    notification_message=(
                        f'You are temporarily banned from this hub until <t:{int(expires_at.timestamp())}:F>. You can appeal this decision using /appeal. '
                        f'Reason: {reason}'
                    ),
                    infraction_id=infraction_id,
                )

        elif check_type == 'server_infraction':
            if expires_at is None:
                logger.info(
                    f'Message blocked: server {guild_id} permanently banned from hub {hub_id}'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='Server is banned from this hub',
                    should_notify=not notified,
                    notification_message=f'Your server is banned from this hub. Reason: {reason} You can appeal this decision using /appeal.',
                    infraction_id=infraction_id,
                )
            else:
                logger.info(
                    f'Message blocked: server {guild_id} temporarily banned from hub {hub_id} until {expires_at}'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='Server is temporarily banned from this hub',
                    should_notify=not notified,
                    notification_message=(
                        f'Your server is temporarily banned from this hub until <t:{int(expires_at.timestamp())}:F>. You can appeal this decision using /appeal. '
                        f'Reason: {reason}'
                    ),
                    infraction_id=infraction_id,
                )

        elif check_type == 'user_blacklist':
            logger.info(f'Message blocked: user {user_id} globally blacklisted')
            return ValidationResult(
                is_valid=False,
                reason='User is globally blacklisted',
                should_notify=True,
                notification_message=f'You are blacklisted from InterChat. Reason: {reason}',
                infraction_id=infraction_id,
            )

        elif check_type == 'server_blacklist':
            logger.info(f'Message blocked: server {guild_id} globally blacklisted')
            return ValidationResult(
                is_valid=False,
                reason='Server is globally blacklisted',
                should_notify=True,
                notification_message=f'Your server is blacklisted from InterChat. Reason: {reason}',
                infraction_id=infraction_id,
            )
        else:
            logger.error(f'Unknown check type: {check_type}')
            return ValidationResult(is_valid=True)

    # Redis caching methods using centralized cache manager
    async def _get_validation_cache(self, cache_key: str) -> Optional[ValidationResult]:
        """Get cached validation result from Redis."""
        # Extract user_id, guild_id, hub_id from cache_key
        parts = cache_key.split(':')
        if len(parts) >= 4:  # validation:user_id:guild_id:hub_id
            user_id, guild_id, hub_id = parts[1], parts[2], parts[3]
            cached_data = await validation_cache.get_validation_result(user_id, guild_id, hub_id)
            if cached_data and 'is_valid' in cached_data:
                try:
                    return ValidationResult(
                        is_valid=bool(cached_data.get('is_valid')),
                        reason=cached_data.get('reason'),
                        should_notify=bool(cached_data.get('should_notify', False)),
                        notification_message=cached_data.get('notification_message'),
                        infraction_id=cached_data.get('infraction_id'),
                        spam_action=cached_data.get('spam_action'),
                    )
                except Exception:
                    # Ignore malformed cache entry
                    pass
        return None

    async def _cache_validation_result(self, cache_key: str, result: ValidationResult, ttl: int):
        """Cache validation result in Redis with TTL."""
        # Extract user_id, guild_id, hub_id from cache_key
        parts = cache_key.split(':')
        if len(parts) >= 4:  # validation:user_id:guild_id:hub_id
            user_id, guild_id, hub_id = parts[1], parts[2], parts[3]
            result_dict = result._asdict()
            await validation_cache.set_validation_result(
                user_id, guild_id, hub_id, result_dict, ttl
            )

    def _get_cache_key(self, user_id: str, guild_id: str, hub_id: str) -> str:
        """Generate consistent cache key for validation results."""
        return f'validation:{user_id}:{guild_id}:{hub_id}'
