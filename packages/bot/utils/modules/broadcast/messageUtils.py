from typing import TYPE_CHECKING, Dict, List, Optional, Tu<PERSON>

import discord
from sqlmodel import and_, select

from utils.modules.common.message_utils import MessageContentProcessor, MessageStorageHelper
from utils.modules.common.validation_utils import TextValidators, URLValidators
from db.models import Broadcast, Hub, Message, User

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession

    from main import Bot
    from utils.modules.services.userService import UserService


def is_image_url(url: str) -> bool:
    """Check if URL is an image (png, webp, jpg, jpeg)."""
    return URLValidators.is_image_url(url)


def is_gif_url(url: str) -> bool:
    """Check if URL is a GIF."""
    return URLValidators.is_gif_url(url)


def is_tenor_gif(url: str) -> bool:
    """Check if URL is from Tenor."""
    return URLValidators.is_tenor_gif(url)


def format_message_with_attachments(
    content: str,
    attachments: List[discord.Attachment],
    stickers: List[discord.StickerItem],
) -> tuple[str, list]:
    """Process message content and add attachment/sticker URLs"""
    return MessageContentProcessor.process_message_content(content, attachments, stickers)


async def format_user_badges(bot: 'Bot', user_id: int, usersvc: 'UserService') -> str:
    """Format user badges"""
    from utils.modules.common.message_utils import MessageFormatter

    return await MessageFormatter.format_user_badges(bot, user_id, usersvc)


def create_allowed_mentions_for_broadcast(
    replied_user_id: Optional[int],
) -> discord.AllowedMentions:
    """
    Create AllowedMentions object for broadcasts.
    Only allows mentions for replies going to the original server.
    """
    # Block all mentions by default
    allowed_mentions = discord.AllowedMentions.none()

    # Only allow user mention if this is a reply going to the original server
    if replied_user_id:
        allowed_mentions.users = [discord.Object(id=replied_user_id)]

    return allowed_mentions


def is_reply_mention_required(target_server_id: str, original_server_id: Optional[str]) -> bool:
    """Determine if reply mention should be included based on configuration.
    This can be extended to check bot settings or user preferences.
    """
    if original_server_id and target_server_id == original_server_id:
        return True
    return False


def create_reply_embed(
    message_reference: discord.MessageReference | None,
    reply_data: Optional[Tuple[Message, User, str]],
    target_channel_id: str,
    target_server_id: str,
    broadcast_message_id: Optional[str] = None,
) -> Optional[discord.Embed]:
    """Create reply embed if message is a reply"""
    from utils.modules.common.message_utils import MessageEmbedBuilder

    return MessageEmbedBuilder.create_reply_embed(
        message_reference, reply_data, target_channel_id, target_server_id, broadcast_message_id
    )


def create_embed_from_message_and_author(message: 'Message', author: 'User') -> discord.Embed:
    """Helper method to create embed from message and author"""
    from utils.modules.common.message_utils import MessageEmbedBuilder

    return MessageEmbedBuilder.create_message_embed(message, author)


def build_discord_jump_link(guild_id: str, channel_id: str, message_id: str) -> str:
    """Build a Discord jump link for a message."""
    return f'https://discord.com/channels/{guild_id}/{channel_id}/{message_id}'


async def get_broadcast_ids_for_channels(
    session: 'AsyncSession', original_message_id: str, channel_ids: List[str]
) -> Dict[str, str]:
    """Batch fetch broadcast message IDs for an original message across many channels.

    Returns a mapping of channelId -> broadcastId.
    """
    if not channel_ids:
        return {}

    stmt = select(Broadcast.channelId, Broadcast.id).where(
        and_(Broadcast.messageId == original_message_id, col(Broadcast.channelId).in_(channel_ids))
    )
    result = await session.exec(stmt)
    rows = result.all()

    mapping: Dict[str, str] = {}
    for channel_id, broadcast_id in rows:
        if channel_id and broadcast_id:
            mapping[channel_id] = broadcast_id
    return mapping


async def store_message_and_broadcasts(
    message: discord.Message,
    hub: Hub,
    processed_content: str,
    broadcast_message_ids: List[Tuple[str, str, str]],
    session: 'AsyncSession',
    referred_message: Optional[Message] = None,
) -> None:
    try:
        message_obj = MessageStorageHelper.create_message_object(
            message, hub, processed_content, referred_message
        )

        # Use merge for upsert behavior (handles conflicts automatically)
        session.add(message_obj)

        # Create broadcast objects if any exist
        if broadcast_message_ids:
            broadcast_objects = [
                Broadcast(
                    id=broadcast_id,
                    messageId=str(message.id),
                    channelId=channel_id,
                    guildId=guild_id,
                )
                for broadcast_id, channel_id, guild_id in broadcast_message_ids
            ]

            # Add all broadcast objects to session
            session.add_all(broadcast_objects)

        await session.commit()

    except Exception as e:
        await session.rollback()
        raise RuntimeError(f'Failed to store message and broadcasts: {e}') from e


def validate_discord_id(discord_id: str) -> bool:
    """Validate that a Discord ID is a valid snowflake."""
    return TextValidators.validate_discord_id(discord_id).is_valid


def truncate_text(text: str, max_length: int, suffix: str = '...') -> str:
    """Safely truncate text with proper suffix handling."""
    return TextValidators.truncate_text(text, max_length, suffix)
