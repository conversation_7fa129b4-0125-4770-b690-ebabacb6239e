from typing import TYPE_CHECKING, Optional
import discord
from sqlmodel import select
from db.models import Infraction
from utils.constants import logger

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession


class NotificationManager:
    """Handles DM notifications for validation failures"""

    def __init__(self, session: 'AsyncSession'):
        self.session = session

    async def send_validation_notification(
        self,
        user: discord.User | discord.Member,
        notification_message: str,
        infraction_id: Optional[str] = None,
    ) -> bool:
        """Send a DM notification to a user and update the notified status if applicable."""
        try:
            # Send DM to user
            await user.send(notification_message)
            logger.info(f'Sent validation notification to user {user.id}')

            # Update notified status if infraction_id is provided
            if infraction_id:
                await self._mark_as_notified(infraction_id)

            return True

        except discord.Forbidden:
            logger.warning(f'Could not send DM to user {user.id} - DMs disabled')
            return False
        except discord.HTTPException as e:
            logger.error(f'Failed to send DM to user {user.id}: {e}')
            return False
        except Exception as e:
            logger.error(f'Unexpected error sending notification to user {user.id}: {e}')
            return False

    async def _mark_as_notified(self, infraction_id: str) -> None:
        """
        Mark an infraction as notified in the database.
        Only works for Infraction records, not Blacklist/ServerBlacklist.
        """
        try:
            # Try to find and update an Infraction record
            stmt = select(Infraction).where(Infraction.id == infraction_id)
            result = await self.session.exec(stmt)
            infraction = result.first()

            if infraction:
                infraction.notified = True
                await self.session.commit()
                logger.debug(f'Marked infraction {infraction_id} as notified')
            else:
                logger.debug(f'Could not find infraction {infraction_id} to mark as notified')

        except Exception as e:
            logger.error(f'Error marking infraction {infraction_id} as notified: {e}')

    async def send_new_account_notification(self, user: discord.User | discord.Member) -> bool:
        """Send notification to users with accounts that are too new."""
        message = (
            'Your account is too new to send messages through InterChat. '
            'Please wait until your account is older than 7 days and try again.'
        )

        return await self.send_validation_notification(user, message)
