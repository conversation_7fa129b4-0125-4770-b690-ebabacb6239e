from typing import Callable, TypeVar, Awaitable, Union
import inspect

from utils.modules.events.eventDispatcher import HubEventType

T = TypeVar('T', bound=Callable[..., Awaitable[None]])


def hub_event_listener(event_name: Union[HubEventType, HubEventType]):
    def decorator(func: T) -> T:
        if not inspect.iscoroutinefunction(func):
            raise TypeError(
                f'@hub_event_listener must decorate an async function, got {func.__name__}'
            )

        func._hub_event_listener = (
            event_name.value
            if isinstance(event_name, HubEventType)
            else event_name or func.__name__.removeprefix('on_')
        )
        return func

    return decorator
