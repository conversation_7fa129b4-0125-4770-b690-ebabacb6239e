from utils.modules.events.eventDispatcher import (
    event_dispatcher,
    HubEventType,
    dispatch_user_action,
    dispatch_server_action,
    dispatch_hub_management,
    dispatch_connection_event,
    create_hub_event,
)
from datetime import datetime
from typing import Literal, Optional, Union


LimitedMuteEvent = Literal[HubEventType.USER_MUTE, HubEventType.SERVER_MUTE]
LimitedBanEvent = Literal[HubEventType.USER_BAN, HubEventType.SERVER_BAN]


class HubLogger:
    """
    Utility class to simplify hub logging in command implementations.

    This class provides a interface for dispatching hub events
    without needing to remember all the parameter names and types.
    """

    @staticmethod
    async def log_user_warn(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        user_id: str,
        user_name: str,
        reason: Optional[str] = None,
    ):
        """Log a user warning event."""
        await dispatch_user_action(
            action_type=HubEventType.USER_WARN,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_user_id=user_id,
            target_user_name=user_name,
            reason=reason,
        )

    @staticmethod
    async def log_ban(
        action_type: Union[HubEventType, LimitedBanEvent],
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        target_id: str,
        target_name: str,
        reason: Optional[str] = None,
        duration: Optional[str] = None,
        expires_at: Optional[datetime] = None,
    ):
        """Log a ban event."""
        if action_type == HubEventType.USER_BAN:
            await HubLogger.log_user_ban(
                hub_id=hub_id,
                hub_name=hub_name,
                moderator_id=moderator_id,
                moderator_name=moderator_name,
                user_id=target_id,
                user_name=target_name,
                reason=reason,
                duration=duration,
                expires_at=expires_at,
            )
        else:
            await HubLogger.log_server_ban(
                hub_id=hub_id,
                hub_name=hub_name,
                moderator_id=moderator_id,
                moderator_name=moderator_name,
                server_id=target_id,
                server_name=target_name,
                reason=reason,
                duration=duration,
                expires_at=expires_at,
            )

    @staticmethod
    async def log_user_ban(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        user_id: str,
        user_name: str,
        reason: Optional[str] = None,
        duration: Optional[str] = None,
        expires_at: Optional[datetime] = None,
    ):
        """Log a user ban event."""
        await dispatch_user_action(
            action_type=HubEventType.USER_BAN,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_user_id=user_id,
            target_user_name=user_name,
            reason=reason,
            duration=duration,
            expires_at=expires_at,
        )

    @staticmethod
    async def log_user_unban(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        user_id: str,
        user_name: str,
        reason: Optional[str] = None,
    ):
        """Log a user unban event."""
        await dispatch_user_action(
            action_type=HubEventType.USER_UNBAN,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_user_id=user_id,
            target_user_name=user_name,
            reason=reason,
        )

    @staticmethod
    async def log_mute(
        action_type: Union[HubEventType, LimitedMuteEvent],
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        target_id: str,
        target_name: str,
        reason: Optional[str] = None,
        duration: Optional[str] = None,
        expires_at: Optional[datetime] = None,
    ):
        """Log a mute event."""
        if action_type == HubEventType.USER_MUTE:
            await HubLogger.log_user_mute(
                hub_id=hub_id,
                hub_name=hub_name,
                moderator_id=moderator_id,
                moderator_name=moderator_name,
                user_id=target_id,
                user_name=target_name,
                reason=reason,
                duration=duration,
                expires_at=expires_at,
            )
        else:
            await HubLogger.log_server_mute(
                hub_id=hub_id,
                hub_name=hub_name,
                moderator_id=moderator_id,
                moderator_name=moderator_name,
                server_id=target_id,
                server_name=target_name,
                reason=reason,
                duration=duration,
                expires_at=expires_at,
            )

    @staticmethod
    async def log_user_mute(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        user_id: str,
        user_name: str,
        reason: Optional[str] = None,
        duration: Optional[str] = None,
        expires_at: Optional[datetime] = None,
    ):
        """Log a user mute event."""
        await dispatch_user_action(
            action_type=HubEventType.USER_MUTE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_user_id=user_id,
            target_user_name=user_name,
            reason=reason,
            duration=duration,
            expires_at=expires_at,
        )

    @staticmethod
    async def log_user_unmute(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        user_id: str,
        user_name: str,
        reason: Optional[str] = None,
    ):
        """Log a user unmute event."""
        await dispatch_user_action(
            action_type=HubEventType.USER_UNMUTE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_user_id=user_id,
            target_user_name=user_name,
            reason=reason,
        )

    @staticmethod
    async def log_server_warn(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        server_id: str,
        server_name: str,
        reason: Optional[str] = None,
    ):
        """Log a server warning event."""
        await dispatch_server_action(
            action_type=HubEventType.SERVER_WARN,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_server_id=server_id,
            target_server_name=server_name,
            reason=reason,
        )

    @staticmethod
    async def log_server_ban(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        server_id: str,
        server_name: str,
        reason: Optional[str] = None,
        duration: Optional[str] = None,
        expires_at: Optional[datetime] = None,
    ):
        """Log a server ban event."""
        await dispatch_server_action(
            action_type=HubEventType.SERVER_BAN,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_server_id=server_id,
            target_server_name=server_name,
            reason=reason,
            duration=duration,
            expires_at=expires_at,
        )

    @staticmethod
    async def log_server_unban(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        server_id: str,
        server_name: str,
        reason: Optional[str] = None,
    ):
        """Log a server unban event."""
        await dispatch_server_action(
            action_type=HubEventType.SERVER_UNBAN,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_server_id=server_id,
            target_server_name=server_name,
            reason=reason,
        )

    @staticmethod
    async def log_server_mute(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        server_id: str,
        server_name: str,
        reason: Optional[str] = None,
        duration: Optional[str] = None,
        expires_at: Optional[datetime] = None,
    ):
        """Log a server mute event."""
        await dispatch_server_action(
            action_type=HubEventType.SERVER_MUTE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_server_id=server_id,
            target_server_name=server_name,
            reason=reason,
            duration=duration,
            expires_at=expires_at,
        )

    @staticmethod
    async def log_server_unmute(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        server_id: str,
        server_name: str,
        reason: Optional[str] = None,
    ):
        """Log a server unmute event."""
        await dispatch_server_action(
            action_type=HubEventType.SERVER_UNMUTE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            target_server_id=server_id,
            target_server_name=server_name,
            reason=reason,
        )

    @staticmethod
    async def log_hub_create(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        reason: Optional[str] = None,
    ):
        """Log a hub creation event."""
        await dispatch_hub_management(
            action_type=HubEventType.HUB_CREATE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            reason=reason,
        )

    @staticmethod
    async def log_hub_delete(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        reason: Optional[str] = None,
    ):
        """Log a hub deletion event."""
        await dispatch_hub_management(
            action_type=HubEventType.HUB_DELETE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            reason=reason,
        )

    @staticmethod
    async def log_hub_update(
        hub_id: str,
        hub_name: str,
        moderator_id: str,
        moderator_name: str,
        reason: Optional[str] = None,
    ):
        """Log a hub update event."""
        await dispatch_hub_management(
            action_type=HubEventType.HUB_UPDATE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            reason=reason,
        )

    @staticmethod
    async def log_connection_add(
        hub_id: str,
        hub_name: str,
        server_id: str,
        server_name: str,
        moderator_id: Optional[str] = None,
        moderator_name: Optional[str] = None,
    ):
        """Log a server connection event."""
        await dispatch_connection_event(
            action_type=HubEventType.CONNECTION_ADD,
            hub_id=hub_id,
            hub_name=hub_name,
            server_id=server_id,
            server_name=server_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
        )

    @staticmethod
    async def log_connection_remove(
        hub_id: str,
        hub_name: str,
        server_id: str,
        server_name: str,
        moderator_id: Optional[str] = None,
        moderator_name: Optional[str] = None,
    ):
        """Log a server disconnection event."""
        await dispatch_connection_event(
            action_type=HubEventType.CONNECTION_REMOVE,
            hub_id=hub_id,
            hub_name=hub_name,
            server_id=server_id,
            server_name=server_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
        )

    @staticmethod
    async def log_message_delete(
        hub_id: str,
        hub_name: str,
        message_id: str,
        channel_id: str,
        original_content: str,
        moderator_id: Optional[str] = None,
        moderator_name: Optional[str] = None,
        reason: Optional[str] = None,
    ):
        """Log a message deletion event."""
        event = create_hub_event(
            event_type=HubEventType.MESSAGE_DELETE,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            message_id=message_id,
            channel_id=channel_id,
            original_content=original_content,
            reason=reason,
        )
        await event_dispatcher.dispatch_hub_event(event)

    @staticmethod
    async def log_message_edit(
        hub_id: str,
        hub_name: str,
        message_id: str,
        channel_id: str,
        original_content: str,
        new_content: str,
        moderator_id: Optional[str] = None,
        moderator_name: Optional[str] = None,
        reason: Optional[str] = None,
    ):
        """Log a message edit event."""
        event = create_hub_event(
            event_type=HubEventType.MESSAGE_EDIT,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            message_id=message_id,
            channel_id=channel_id,
            original_content=original_content,
            new_content=new_content,
            reason=reason,
        )
        await event_dispatcher.dispatch_hub_event(event)

    @staticmethod
    async def log_message_report(
        hub_id: str,
        hub_name: str,
        message_id: str,
        channel_id: str,
        original_content: str,
        moderator_id: str,
        moderator_name: str,
        reason: Optional[str] = None,
    ):
        """Log a message report event."""
        event = create_hub_event(
            event_type=HubEventType.MESSAGE_REPORT,
            hub_id=hub_id,
            hub_name=hub_name,
            moderator_id=moderator_id,
            moderator_name=moderator_name,
            message_id=message_id,
            channel_id=channel_id,
            original_content=original_content,
            reason=reason,
        )
        await event_dispatcher.dispatch_hub_event(event)
