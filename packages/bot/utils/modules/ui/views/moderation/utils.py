from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import View

from utils.modules.common.error_utils import send_error_message, send_success_message
from utils.modules.core.checks import interaction_check

if TYPE_CHECKING:
    from main import Bot

MAX_SELECT_OPTIONS = 25
DEFAULT_VIEW_TIMEOUT = 300
MAX_REASON_LENGTH = 400
MIN_REASON_LENGTH = 3
MAX_DURATION_LENGTH = 20
MIN_DURATION_LENGTH = 1
MAX_DESCRIPTION_LENGTH = 100

PLACEHOLDER_REPUTATION = 0
PLACEHOLDER_INFRACTIONS = 0


class BaseModerationView(View):
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        locale: str,
        timeout: float = DEFAULT_VIEW_TIMEOUT,
    ):
        super().__init__(timeout=timeout)
        self.bot = bot
        self.moderator = moderator
        self.locale = locale
        self.constants = bot.constants

    async def validate_interaction(
        self, interaction: discord.Interaction, expected_user: Optional[discord.abc.User] = None
    ) -> bool:
        """Validate that the interaction is from the expected user."""
        target_user = expected_user or self.moderator
        return await interaction_check(interaction, target_user, interaction.user)  # type: ignore[arg-type]

    async def send_error(self, interaction: discord.Interaction['Bot'], message: str) -> None:
        """Send an error message to the user."""
        await send_error_message(interaction, message, ephemeral=True)

    async def send_success(self, interaction: discord.Interaction['Bot'], message: str) -> None:
        """Send a success message to the user."""
        await send_success_message(interaction, message, ephemeral=True)
