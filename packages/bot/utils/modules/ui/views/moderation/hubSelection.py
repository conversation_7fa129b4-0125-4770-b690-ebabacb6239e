from collections.abc import Sequence
from typing import TYPE_CHECKING, Optional

import discord

from db.models import (
    Hub,
)
from utils.modules.core.i18n import t
from utils.modules.core.moderation import mod_panel_embed

from .utils import (
    MAX_DESCRIPTION_LENGTH,
    MAX_SELECT_OPTIONS,
    PLACEHOLDER_INFRACTIONS,
    BaseModerationView,
)

if TYPE_CHECKING:
    from main import Bot


class HubSelectionView(BaseModerationView):
    """View for selecting a hub when no message is provided.

    This view is displayed when a moderator has access to multiple hubs
    and needs to select which hub they want to moderate.
    """

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        user_hubs: Sequence[Hub],
        locale: str,
    ):
        super().__init__(bot, moderator, locale)
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.user_hubs = user_hubs

        # Add hub selection dropdown
        self.add_item(HubSelectDropdown(self.user_hubs, self))

    async def update_with_hub(self, interaction: discord.Interaction, selected_hub: Hub):
        """Update the view after a hub is selected - show the mod panel."""
        from .modPanel import ModPanelView

        # Create the main mod panel
        view = ModPanelView(
            self.bot,
            self.moderator,
            self.target_user,
            self.target_server,
            self.target_message,
            selected_hub,
            self.locale,
        )

        # Create embed
        embed = mod_panel_embed(
            self.bot,
            selected_hub,
            self.target_user,
            self.target_server,
            self.target_message,
            self.target_message.content if self.target_message else '',
            user_infractions=PLACEHOLDER_INFRACTIONS,
            server_infractions=PLACEHOLDER_INFRACTIONS,
            _locale=self.locale,
        )

        await interaction.response.edit_message(embed=embed, view=view)


class HubSelectDropdown(discord.ui.Select):
    def __init__(self, hubs: Sequence[Hub], parent_view: HubSelectionView):
        self.locale = parent_view.locale
        options = []
        seen_ids = set()  # Track hub IDs to prevent duplicates

        for hub in hubs[:MAX_SELECT_OPTIONS]:
            seen_ids.add(hub.id)

            description = hub.shortDescription or t('ui.common.noDescription', locale=self.locale)
            if len(description) > MAX_DESCRIPTION_LENGTH:
                description = description[:MAX_DESCRIPTION_LENGTH]

            options.append(
                discord.SelectOption(
                    label=hub.name,
                    value=hub.id,
                    description=description,
                )
            )

        # Ensure we have at least one option
        if not options:
            options.append(
                discord.SelectOption(
                    label='No hubs available',
                    value='none',
                    description='No hubs available for moderation',
                )
            )

        super().__init__(
            placeholder=t('ui.moderation.hubSelect.placeholder', locale=self.locale),
            options=options,
            min_values=1,
            max_values=1,
        )
        self.hubs = hubs
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        """Handle hub selection from the dropdown."""
        if not await self.parent_view.validate_interaction(interaction):
            return

        selected_hub_id = self.values[0]

        # Handle the "none" option case
        if selected_hub_id == 'none':
            await self.parent_view.send_error(
                interaction,
                'No hubs available for moderation.',
            )
            return

        selected_hub = next((hub for hub in self.hubs if hub.id == selected_hub_id), None)

        if selected_hub:
            await self.parent_view.update_with_hub(interaction, selected_hub)
        else:
            await self.parent_view.send_error(
                interaction,
                t(
                    'responses.moderation.errors.selectedHubNotFound',
                    locale=self.locale,
                ),
            )
