from __future__ import annotations

from typing import TYPE_CHECKING, Optional, overload

import discord
from discord.ui import <PERSON><PERSON>, button
from sqlmodel import select
from sqlalchemy.orm import joinedload

from db.models import (
    <PERSON><PERSON>,
    <PERSON>bReport,
    ReportStatus,
)
from utils.modules.core.i18n import t
from utils.modules.core.moderation import get_user_moderated_hubs, mod_panel_embed
from utils.modules.ui.views.moderation.modPanel import ModPanelView
from utils.utils import load_user_locale
from utils.constants import logger

from .utils import PLACEHOLDER_INFRACTIONS, BaseModerationView

if TYPE_CHECKING:
    from main import Bot


class ReportActionView(BaseModerationView):
    def __init__(
        self,
        bot: 'Bot',
        report_id: str,
        jump_url: Optional[str] = None,
    ):
        super().__init__(bot, None, 'en', None)  # pyright: ignore[reportArgumentType]
        self.report_handler = ReportHandler(bot)

        self.open_custom_id = f'report:open_mod_panel:{report_id}'
        self.resolve_custom_id = f'report:resolve:{report_id}'
        self.ignore_custom_id = f'report:ignore:{report_id}'
        self.jump_url = jump_url

        # Assign custom_ids to the actual buttons after View init
        self.open_mod_panel.custom_id = self.open_custom_id
        self.resolve_report.custom_id = self.resolve_custom_id
        self.ignore_report.custom_id = self.ignore_custom_id

        # Add emotes
        self.open_mod_panel.emoji = self.bot.emotes.hammer_icon
        self.resolve_report.emoji = self.bot.emotes.tick
        self.ignore_report.emoji = self.bot.emotes.x_icon

        if self.jump_url:
            self.add_item(
                Button(
                    emoji=bot.emotes.reply,
                    label='Jump to Message',
                    url=self.jump_url,
                    style=discord.ButtonStyle.link,
                    row=2,
                ),
            )

    @button(label='Resolve', style=discord.ButtonStyle.success)
    async def resolve_report(self, interaction: discord.Interaction['Bot'], button: Button):
        """Resolve the report."""
        locale = await load_user_locale(interaction)
        await self._handle_report_resolution(interaction, action='resolve', locale=locale)

    @button(label='Ignore', style=discord.ButtonStyle.secondary)
    async def ignore_report(self, interaction: discord.Interaction['Bot'], button: Button):
        """Ignore the report."""
        locale = await load_user_locale(interaction)
        await self._handle_report_resolution(interaction, action='ignore', locale=locale)

    async def _handle_report_resolution(
        self, interaction: discord.Interaction['Bot'], action: str, locale: str
    ):
        """Handle report resolution (resolve/ignore)."""
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        report_id = self.report_handler.parse_report_button_id(custom_id)

        if not report_id:
            await self.send_error(
                interaction, t('responses.report.errors.notFoundOrDeleted', locale)
            )
            return

        try:
            await self._process_report_resolution(interaction, report_id, action, locale)
        except Exception:
            logger.exception(
                'Error processing report resolution for report_id=%s, action=%s', report_id, action
            )

            await self.send_error(
                interaction,
                t('responses.report.errors.processingFailed', locale),
            )

    @button(label='Open Mod Panel', style=discord.ButtonStyle.primary)
    async def open_mod_panel(self, interaction: discord.Interaction['Bot'], button: Button):
        """Open the moderation panel for this report."""
        locale = await load_user_locale(interaction)
        # Parse report_id from the button's custom_id
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        report_id = self.report_handler.parse_report_button_id(custom_id)

        if not report_id:
            await self.send_error(
                interaction, t('responses.errors.missingAppealReference', locale=locale)
            )
            return

        try:
            await self._handle_open_mod_panel(interaction, report_id, locale)
        except Exception as e:
            logger.exception('Error opening mod panel for report_id=%s: %s', report_id, e)
            await self.send_error(
                interaction,
                t('responses.moderation.errors.openPanelFailed', locale=locale),
            )

    async def _handle_open_mod_panel(
        self, interaction: discord.Interaction['Bot'], report_id: str, locale: str
    ):
        """Handle opening the moderation panel."""
        # Fetch context from DB
        context = await self.report_handler.fetch_report_context(report_id)
        if not context:
            await self.send_error(interaction, 'Report not found or has been deleted.')
            return

        # Resolve Discord targets
        (
            target_user,
            target_server,
            target_message,
        ) = await self.report_handler.resolve_report_targets(context)

        # For reports, infer the hub from the reported message
        report_hub_id = context.hubId
        if not report_hub_id:
            await self.send_error(interaction, 'Unable to determine hub for this report.')
            return

        # Check if user can moderate the specific hub for this report
        user_hubs = await get_user_moderated_hubs(interaction.client, str(interaction.user.id))
        if not user_hubs:
            await self.send_error(interaction, 'You do not moderate any hubs.')
            return

        # Find the specific hub for this report
        report_hub = next((hub for hub in user_hubs if hub.id == report_hub_id), None)
        if not report_hub:
            await self.send_error(interaction, 'You do not have permission to moderate this hub.')
            return

        await self._show_single_hub_panel(
            interaction, report_hub, locale, target_user, target_server, target_message
        )

    async def _show_single_hub_panel(
        self,
        interaction: discord.Interaction['Bot'],
        hub: Hub,
        locale: str,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
    ):
        """Show moderation panel for a single hub."""
        view = ModPanelView(
            self.bot,
            interaction.user,
            target_user,
            target_server,
            target_message,
            hub,
            locale,
        )

        embed = mod_panel_embed(
            self.bot,
            hub,
            target_user,
            target_server,
            target_message,
            target_message.content if target_message else '',
            PLACEHOLDER_INFRACTIONS,
            PLACEHOLDER_INFRACTIONS,
            locale,
        )
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    async def _process_report_resolution(
        self, interaction: discord.Interaction['Bot'], report_id: str, action: str, locale: str
    ):
        """Process the report resolution."""
        # Load report context for permissions and current status
        context = await self.report_handler.fetch_report_context(report_id)
        if not context:
            await self.send_error(
                interaction, t('responses.report.errors.notFoundOrDeleted', locale)
            )
            return

        # Permission check: user must moderate this hub
        user_hubs = await get_user_moderated_hubs(self.bot, str(interaction.user.id))
        if not any(h.id == context.hubId for h in user_hubs):
            await self.send_error(
                interaction, t('responses.moderation.errors.notModeratorForHub', locale=locale)
            )
            return

        # Check if already resolved/ignored
        current_status = context.status
        if current_status in (ReportStatus.RESOLVED, ReportStatus.IGNORED):
            await self.send_error(
                interaction,
                t(
                    'responses.report.errors.alreadyHandled',
                    locale=locale,
                    status=current_status.value.lower(),
                ),
            )
            await self._refresh_report_view(
                interaction, report_id, current_status, interaction.user, locale
            )
            return

        # Update status
        new_status = ReportStatus.RESOLVED if action == 'resolve' else ReportStatus.IGNORED
        success = await self.report_handler.update_report_status(
            report_id, new_status, str(interaction.user.id)
        )

        if not success:
            await self.send_error(
                interaction, t('responses.report.errors.updateFailed', locale=locale)
            )
            return

        # Update the view and confirm
        await self._refresh_report_view(
            interaction, report_id, new_status, interaction.user, locale
        )
        action_label = t(
            'responses.report.success.actionPast',
            locale=locale,
            action=('resolved' if action == 'resolve' else 'ignored'),
        )
        await self.send_success(interaction, action_label)

        # Notify reporter via DM on resolution (privacy-preserving)
        if new_status == ReportStatus.RESOLVED:
            try:
                # Use context we already fetched
                reporter_id = context.reporterId
                if reporter_id:
                    user_obj = self.bot.get_user(int(reporter_id)) or await self.bot.fetch_user(
                        int(reporter_id)
                    )
                    if user_obj:
                        dm_message = t('responses.report.dm.resolved', locale=locale)
                        try:
                            await user_obj.send(dm_message)
                            logger.debug(
                                'DM sent to reporter_id=%s for report resolution', reporter_id
                            )
                        except discord.Forbidden:
                            logger.debug(
                                'Reporter_id=%s has DMs disabled; skipping DM', reporter_id
                            )
            except Exception:
                # Never fail the interaction due to DM issues
                pass

    async def _refresh_report_view(
        self,
        interaction: discord.Interaction['Bot'],
        report_id: str,
        status: ReportStatus,
        moderator: discord.abc.User,
        locale: str,
    ):
        """Refresh the report view with updated status."""
        new_view = ReportActionView(self.bot, report_id, jump_url=self.jump_url)

        if status == ReportStatus.RESOLVED:
            new_view.resolve_report.label = t(
                'ui.report.status.resolvedBy', locale, name=moderator.name
            )
            new_view.resolve_report.disabled = True
            new_view.remove_item(new_view.ignore_report)
        elif status == ReportStatus.IGNORED:
            new_view.ignore_report.label = t(
                'ui.report.status.ignoredBy', locale, name=moderator.name
            )
            new_view.ignore_report.disabled = True
            new_view.remove_item(new_view.resolve_report)

        new_view.open_mod_panel.style = discord.ButtonStyle.secondary
        await interaction.response.edit_message(view=new_view)


class ReportHandler:
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    def parse_report_button_id(self, custom_id: str) -> Optional[str]:
        """Parse report ID from button custom_id."""
        parts = custom_id.split(':')

        if len(parts) != 3 or parts[0] != 'report':
            return None

        return parts[2] if parts[2] and parts[2] != '0' else None

    async def fetch_report_context(self, report_id: str) -> Optional[HubReport]:
        async with self.bot.db.get_session() as session:
            stmt = (
                select(HubReport)
                .options(joinedload(HubReport.message))  # pyright: ignore[reportArgumentType]
                .where(HubReport.id == report_id)
            )
            result = await session.scalar(stmt)
        return result

    async def resolve_report_targets(
        self, context: HubReport
    ) -> tuple[
        Optional[discord.User | discord.Member], Optional[discord.Guild], Optional[discord.Message]
    ]:
        """Resolve Discord objects from report context."""
        target_user = None
        target_server = None

        # Resolve user
        if context.reportedUserId:
            target_user = await DiscordTargetResolver.resolve_user(
                self.bot, context.reportedUserId, context.reportedServerId
            )

        # Resolve server
        if context.reportedServerId:
            target_server = DiscordTargetResolver.resolve_server(self.bot, context.reportedServerId)

        # Resolve message
        target_message = await DiscordTargetResolver.resolve_message(
            self.bot, context.message.channelId, context.message.id
        )

        return target_user, target_server, target_message

    async def update_report_status(
        self, report_id: str, new_status: ReportStatus, moderator_id: str
    ) -> bool:
        """Update report status in database."""
        try:
            async with self.bot.db.get_session() as session:
                report = await session.get(HubReport, report_id)

                if not report:
                    return False

                report.status = new_status
                report.handledBy = moderator_id
                from datetime import datetime

                report.handledAt = datetime.now()
                await session.commit()
                return True
        except Exception:
            return False


class DiscordTargetResolver:
    """Utility class for resolving Discord objects from IDs."""

    @staticmethod
    @overload
    async def resolve_user(
        client: discord.Client, user_id: str, server_id: str
    ) -> Optional[discord.Member]: ...
    @staticmethod
    @overload
    async def resolve_user(
        client: discord.Client, user_id: str, server_id: None
    ) -> Optional[discord.User]: ...
    @staticmethod
    async def resolve_user(
        client: discord.Client, user_id: str, server_id: Optional[str] = None
    ) -> Optional[discord.User | discord.Member]:
        try:
            if server_id:
                guild = client.get_guild(int(server_id))
                if guild:
                    member = guild.get_member(int(user_id))
                    if member:
                        return member
                    try:
                        return await guild.fetch_member(int(user_id))
                    except discord.NotFound:
                        pass
            return await client.fetch_user(int(user_id))
        except (ValueError, discord.NotFound, discord.HTTPException):
            return None

    @staticmethod
    def resolve_server(client: discord.Client, server_id: str) -> Optional[discord.Guild]:
        try:
            return client.get_guild(int(server_id))
        except ValueError:
            return None

    @staticmethod
    async def resolve_message(
        client: discord.Client, channel_id: str, message_id: str
    ) -> Optional[discord.Message]:
        """Resolve a message from channel and message IDs."""
        try:
            channel = client.get_channel(int(channel_id))
            if not channel:
                channel = await client.fetch_channel(int(channel_id))

            if isinstance(
                channel,
                (
                    discord.TextChannel,
                    discord.Thread,
                    discord.VoiceChannel,
                    discord.StageChannel,
                    discord.DMChannel,
                ),
            ) and hasattr(channel, 'fetch_message'):
                return await channel.fetch_message(int(message_id))
        except (ValueError, discord.NotFound, discord.HTTPException):
            pass
        return None
