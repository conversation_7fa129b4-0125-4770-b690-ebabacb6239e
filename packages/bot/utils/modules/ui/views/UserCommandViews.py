from typing import TYPE_CHECKING

import discord
from discord.ui import <PERSON><PERSON>, <PERSON>, button

from utils.modules.common.database import DatabaseQueries, DatabaseUtils
from utils.modules.core.checks import interaction_check
from db.models import User
from utils.modules.errors import customDiscord
from utils.modules.core.i18n import t
from utils.modules.ui.views.BaseView import BaseView
from utils.utils import (
    load_locales,
    load_user_locale,
)

if TYPE_CHECKING:
    from main import Bot


class UserButtons(BaseView):
    def __init__(self, bot: 'Bot', user: discord.User):
        super().__init__(bot, user, timeout=300)
        self.bot = bot
        self.user = user

    def setup_button(self):
        self.callback.emoji = self.bot.emotes.trophy_icon

    @button(style=discord.ButtonStyle.grey)
    async def callback(self, interaction: discord.Interaction['Bot'], _button: Button):
        await interaction.response.defer()
        if not await interaction_check(interaction, self.user, interaction.user):
            return

        await interaction.followup.send(
            t('responses.user.achievements.placeholder', locale='en'), ephemeral=True
        )


class LocaleView(BaseView):
    def __init__(self, bot: 'Bot', user: discord.User | discord.Member, locale: str):
        super().__init__(bot, user, timeout=120)
        self.locale = locale

        self.add_back_button(
            PreferencesView(self.bot, self.user, locale),
            embed_title=t('ui.preferences.title', locale=self.locale),
            embed_description=t('ui.preferences.description', locale=self.locale),
            row=2,
        )

    async def load_options(self) -> None:
        options = []
        for locale in await load_locales():
            options.append(
                discord.SelectOption(
                    emoji=locale['flag'], label=locale['long'], value=locale['short']
                )
            )

        for item in self.children:
            if isinstance(item, discord.ui.Select):
                item.options = options
                item.disabled = False
                break

    @discord.ui.select(
        placeholder=t('ui.setup.select.placeholder', locale='en'),
        options=[
            discord.SelectOption(
                label=t('ui.setup.select.loadingLabel', locale='en'),
                description=t('ui.setup.select.loadingDescription', locale='en'),
                value='LOADING',
            )
        ],
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def on_submit(
        self, interaction: discord.Interaction['Bot'], discord_select: discord.ui.Select
    ):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        selected_locale = discord_select.values[0]

        async with self.bot.db.get_session() as session:
            user = await DatabaseQueries.fetch_user_by_id(session, str(interaction.user.id))
            if not user:
                raise customDiscord.InvalidInput()

            user.locale = selected_locale
            await session.commit()

        locale_name = None
        for locale in await load_locales():
            if locale['short'] == selected_locale:
                locale_name = locale['long']
                break

        embed = discord.Embed(
            title=t('responses.setup.locale.successTitle', locale=selected_locale),
            description=t(
                'responses.setup.locale.successDescription',
                locale=selected_locale,
                tick=self.bot.emotes.tick,
                locale_name=locale_name,
            ),
            color=discord.Color.green(),
        )
        await interaction.edit_original_response(embed=embed, view=None)


class BadgeVisibility(BaseView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.Member | discord.User,
        current: 'User',
    ):
        super().__init__(bot, user, timeout=120)
        self.current = current
        self.locale = current.locale or 'en'
        self.load_button()

        self.add_back_button(
            PreferencesView(self.bot, self.user, self.locale),
            embed_title=t('ui.preferences.title', locale=self.locale),
            embed_description=t('ui.preferences.description', locale=self.locale),
            row=2,
        )

    def load_button(self):
        if self.current.showBadges:
            self.callback.label = t('ui.preferences.badges.buttons.hide', locale=self.locale)
            self.callback.style = discord.ButtonStyle.red
        else:
            self.callback.label = t('ui.preferences.badges.buttons.show', locale=self.locale)
            self.callback.style = discord.ButtonStyle.green
        self.callback.disabled = False

    @button(
        label=t('ui.setup.select.loadingLabel', locale='en'),
        style=discord.ButtonStyle.grey,
        disabled=True,
    )
    async def callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            new_value = not self.current.showBadges
            user = await DatabaseQueries.fetch_user_by_id(session, str(interaction.user.id))
            if not user:
                raise customDiscord.InvalidInput()

            user.showBadges = new_value
            await session.commit()

            self.current.showBadges = new_value

            self.load_button()

            embed = discord.Embed(
                title=t('ui.preferences.badges.title', locale=self.locale),
                description=t(
                    'ui.preferences.badges.description',
                    locale=self.locale,
                    status=t('ui.preferences.badges.visible', locale=self.locale)
                    if new_value
                    else t('ui.preferences.badges.hidden', locale=self.locale),
                ),
                color=self.constants.color,
            )
            embed.set_author(
                name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
            )

            await interaction.edit_original_response(embed=embed, view=self)


class MentionReplyView(BaseView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.Member | discord.User,
        current: User,
    ):
        super().__init__(bot, user, timeout=120)
        self.current = current
        self.locale = current.locale or 'en'

        self.load_button()
        self.add_back_button(
            PreferencesView(self.bot, self.user, self.locale),
            embed_title=t('ui.preferences.title', locale=self.locale),
            embed_description=t('ui.preferences.description', locale=self.locale),
            row=2,
        )

    def load_button(self):
        if self.current.mentionOnReply:
            self.callback.label = 'Disable'
            self.callback.style = discord.ButtonStyle.red
        else:
            self.callback.label = 'Enable'
            self.callback.style = discord.ButtonStyle.green
        self.callback.disabled = False

    @button(label='Loading...', style=discord.ButtonStyle.grey, disabled=True)
    async def callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            new_value = not self.current.mentionOnReply
            user = await DatabaseQueries.fetch_user_by_id(session, str(interaction.user.id))
            if not user:
                raise customDiscord.InvalidInput()

            user.mentionOnReply = new_value
            await session.commit()

            self.current.mentionOnReply = new_value
            self.load_button()

            user_locale = await load_user_locale(interaction)
            status = (
                t('ui.preferences.replyMentions.mentioned', locale=user_locale)
                if new_value
                else t('ui.preferences.replyMentions.notMentioned', locale=user_locale)
            )
            embed = discord.Embed(
                title=t('ui.preferences.replyMentions.title', locale=user_locale),
                description=t(
                    'ui.preferences.replyMentions.description', locale=user_locale, status=status
                ),
                color=self.constants.color,
            )
            embed.set_author(
                name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
            )

            await interaction.edit_original_response(embed=embed, view=self)


class GeneralView(BaseView):
    def __init__(self, bot: 'Bot', user: discord.Member | discord.User, locale: str):
        super().__init__(bot, user, timeout=120)
        self.locale = locale
        self.setup_options()

        self.add_back_button(
            PreferencesView(self.bot, self.user, self.locale),
            embed_title=t('ui.preferences.title', locale=self.locale),
            embed_description=t('ui.preferences.description', locale=self.locale),
            row=2,
        )

    def setup_options(self):
        options = [
            discord.SelectOption(
                emoji=self.bot.emotes.lightbulb_icon,
                label='Badge Visibility',
                description='Badge visibility on Hub messages',
                value='bvis',
            ),
            discord.SelectOption(
                emoji=self.bot.emotes.mention_icon,
                label='Mention On Reply',
                description='Should you be mentioned on a message reply?',
                value='mreply',
            ),
        ]

        for item in self.children:
            if isinstance(item, discord.ui.Select):
                item.options = options
                item.disabled = False
                item.placeholder = t('ui.preferences.select.placeholder', locale='en')
                break

    @discord.ui.select(
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def on_submit(
        self, interaction: discord.Interaction['Bot'], discord_select: discord.ui.Select
    ):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        result = await DatabaseUtils.get_user(str(interaction.user.id))

        if not result:
            user_locale = await load_user_locale(interaction)
            embed = discord.Embed(
                title=t('responses.errors.errorTitle', locale=user_locale),
                description=f'{self.bot.emotes.x_icon} {t("ui.preferences.errors.noPreferences", locale=user_locale)}',
                color=self.constants.color,
            )
            return await interaction.edit_original_response(embed=embed, view=None)

        match discord_select.values[0]:
            case 'bvis':
                user_locale = await load_user_locale(interaction)
                status = (
                    t('ui.preferences.badges.visible', locale=user_locale)
                    if result.showBadges
                    else t('ui.preferences.badges.hidden', locale=user_locale)
                )
                embed = discord.Embed(
                    title=t('ui.preferences.badges.title', locale=user_locale),
                    description=t(
                        'ui.preferences.badges.currentDescription',
                        locale=user_locale,
                        status=status,
                    ),
                    color=self.constants.color,
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                view = BadgeVisibility(self.bot, self.user, result)
                await interaction.edit_original_response(embed=embed, view=view)

            case 'mreply':
                user_locale = await load_user_locale(interaction)
                status = (
                    t('ui.preferences.replyMentions.mentioned', locale=user_locale)
                    if result.mentionOnReply
                    else t('ui.preferences.replyMentions.notMentioned', locale=user_locale)
                )
                embed = discord.Embed(
                    title=t('ui.preferences.replyMentions.title', locale=user_locale),
                    description=t(
                        'ui.preferences.replyMentions.currentDescription',
                        locale=user_locale,
                        status=status,
                    ),
                    color=self.constants.color,
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                view = MentionReplyView(self.bot, self.user, result)
                await interaction.edit_original_response(embed=embed, view=view)

            case _:
                raise customDiscord.InvalidInput()


class PreferencesView(BaseView):
    def __init__(self, bot: 'Bot', user: discord.User | discord.Member, locale: str):
        super().__init__(bot, user, timeout=120)
        self.bot = bot
        self.user = user
        self.constants = bot.constants
        self.locale = locale
        self.load_options()

    def load_options(self):
        options = [
            discord.SelectOption(
                emoji=self.bot.emotes.globe_icon,
                label='Language',
                description='Change the language the bot responds to you in.',
                value='locale',
            ),
            discord.SelectOption(
                emoji=self.bot.emotes.gear_icon,
                label='General',
                description='Edit badge visibility, reply preferences and more!',
                value='general',
            ),
        ]
        for item in self.children:
            if isinstance(item, discord.ui.Select):
                item.options = options
                item.disabled = False
                item.placeholder = t('ui.preferences.select.category', locale=self.locale)
                break

    @discord.ui.select(
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def on_submit(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.Select[View]
    ) -> None:
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        match select.values[0]:
            case 'locale':
                locale = await load_user_locale(interaction)
                view = LocaleView(self.bot, self.user, locale)
                await view.load_options()
                embed = discord.Embed(
                    title=t('ui.preferences.locale.title', locale=locale),
                    description=t('ui.preferences.locale.description', locale=locale),
                    color=self.constants.color,
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                await interaction.edit_original_response(embed=embed, view=view)

            case 'general':
                locale = await load_user_locale(interaction)
                embed = discord.Embed(
                    title=t('ui.preferences.general.title', locale=locale),
                    description=t('ui.preferences.general.description', locale=locale),
                    color=self.constants.color,
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                view = GeneralView(self.bot, self.user, locale)
                await interaction.edit_original_response(embed=embed, view=view)

            case _:
                raise customDiscord.InvalidInput()
