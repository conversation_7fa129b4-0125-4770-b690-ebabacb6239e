import discord
from discord.ui import button, <PERSON><PERSON>
from typing import TYPE_CHECKING

from sqlalchemy.dialects.postgresql import insert as pg_insert
from db.models import HubRulesAcceptance, Hub
from utils.modules.ui.views.BaseView import BaseView
from utils.constants import logger
from utils.utils import upsert_user

if TYPE_CHECKING:
    from main import Bot


class HubRulesAcceptanceView(BaseView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        channel_id: str,
    ):
        super().__init__(bot, user, timeout=300)  # 5 minute timeout for rules acceptance
        self.hub = hub
        self.channel_id = channel_id
        self.accept_rules_btn.emoji = self.bot.emotes.tick
        self.decline_rules_btn.emoji = self.bot.emotes.timeout_icon

    @button(label='Accept Rules', style=discord.ButtonStyle.green, emoji='✅', row=0)
    async def accept_rules_btn(self, interaction: discord.Interaction['Bot'], button: <PERSON><PERSON>):
        """Handle rule acceptance."""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        try:
            async with self.bot.db.get_session() as session:
                # Ensure the user exists in the database before creating rules acceptance
                await upsert_user(self.user, session)

                stmt = (
                    pg_insert(HubRulesAcceptance)
                    .values(userId=str(self.user.id), hubId=self.hub.id)
                    .on_conflict_do_nothing(index_elements=['userId', 'hubId'])
                )

                await session.execute(stmt)
                await session.commit()

            # Create success embed
            embed = discord.Embed(
                title=f'{self.bot.emotes.tick} Rules Accepted',
                description=f'Thank you for accepting the rules for **{self.hub.name}**!\n\nYou can now participate freely in this hub. Your messages will be shared across all connected servers.',
                color=discord.Color.green(),
            )
            embed.set_footer(text='Welcome to the community!')

            await interaction.edit_original_response(embed=embed, view=None)
            logger.info(f'User {self.user.id} accepted rules for hub {self.hub.id}')

        except Exception as e:
            logger.error(
                f'Failed to accept rules for user {self.user.id} in hub {self.hub.id}: {e}'
            )

            embed = discord.Embed(
                title=f'{self.bot.emotes.x_icon} Error',
                description='Something went wrong while processing your rule acceptance. Please try again.',
                color=discord.Color.red(),
            )
            await interaction.edit_original_response(embed=embed, view=self)

    @button(label='Maybe Later', style=discord.ButtonStyle.grey, row=0)
    async def decline_rules_btn(self, interaction: discord.Interaction['Bot'], button: Button):
        """Handle rule decline."""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        # Create decline embed with information
        embed = discord.Embed(
            title=f'{self.bot.emotes.timeout_icon} Rules Not Accepted',
            description=(
                f'You have chosen not to accept the rules for **{self.hub.name}** at this time.\n\n'
                f'**What this means:**\n'
                f'- Your messages will not be shared across the hub\n'
                f'- You can still read messages from other servers\n'
                f'- You will see these rules again when you send your next message'
            ),
            color=discord.Color.orange(),
        )
        embed.add_field(
            name='💡 Changed your mind?',
            value='Send another message in this channel to see the rules again and accept them.',
            inline=False,
        )

        await interaction.edit_original_response(embed=embed, view=None)
        logger.info(f'User {self.user.id} declined rules for hub {self.hub.id}')
