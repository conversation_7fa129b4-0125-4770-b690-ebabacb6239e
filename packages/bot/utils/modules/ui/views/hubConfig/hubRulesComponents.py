from typing import TYPE_CHECKING, Callable, List

import discord

from utils.modules.common.error_utils import ResponseBuilder
from utils.modules.core.i18n import t

if TYPE_CHECKING:
    from main import Bot


class RulesEmbedBuilder:
    """Builder for creating rules-related embeds."""

    @staticmethod
    def create_rules_display_embed(
        bot: 'Bot',
        constants,
        rules: List[str],
        current_page: int,
        rules_per_page: int,
        max_rules: int,
    ) -> discord.Embed:
        """Create the main rules display embed."""
        embed = discord.Embed(color=constants.color)

        # Add title with attractive header
        embed.description = (
            f'### {bot.emotes.rules_icon} {t("ui.hub.config.options.rules.label", locale="en")}\n'
            f'{t("ui.hub.config.options.rules.description", locale="en")}'
        )

        if not rules:
            embed.add_field(
                name=f'{bot.emotes.info_icon} {t("ui.hub.rules.display.noRulesTitle", locale="en")}',
                value=t('ui.hub.rules.display.noRulesDescription', locale='en'),
                inline=False,
            )
        else:
            # Calculate pagination
            total_rules = len(rules)
            total_pages = (total_rules - 1) // rules_per_page + 1
            start_idx = current_page * rules_per_page
            end_idx = min(start_idx + rules_per_page, total_rules)

            # Display rules for current page
            rules_text = ''
            for i in range(start_idx, end_idx):
                rule_number = i + 1
                rule_text = rules[i]
                # Truncate long rules for display
                if len(rule_text) > 150:
                    rule_text = f'{rule_text[:150]}...'
                rules_text += f'**{rule_number}.** {rule_text}\n'

            # Remove trailing newlines
            rules_text = rules_text.rstrip()

            embed.add_field(
                name=f'{bot.emotes.scale_icon} {t("ui.hub.rules.display.currentRulesTitle", locale="en", current=total_rules, max=max_rules)}',
                value=rules_text,
                inline=False,
            )

            # Add pagination info if needed
            if total_pages > 1:
                embed.set_footer(
                    text=t(
                        'ui.common.pagination.page',
                        locale='en',
                        current=current_page + 1,
                        total=total_pages,
                    )
                )

        return embed

    @staticmethod
    def create_rule_action_embed(
        bot: 'Bot',
        constants,
        rule_text: str,
        rule_index: int,
    ) -> discord.Embed:
        """Create embed for showing rule actions."""
        embed = discord.Embed(
            title=f'{bot.emotes.rules_icon} {t("ui.hub.rules.display.ruleSelected", locale="en", number=rule_index + 1)}',
            description=f'**{t("ui.hub.rules.display.currentRule", locale="en")}**\n{rule_text}',
            color=constants.color,
        )
        return embed

    @staticmethod
    def create_delete_confirmation_embed(
        bot: 'Bot',
        rule_text: str,
        rule_index: int,
    ) -> discord.Embed:
        """Create confirmation embed for rule deletion."""
        embed = discord.Embed(
            title=f'{bot.emotes.alert_icon} {t("ui.hub.rules.deleteRule.confirmation", locale="en")}',
            description=f'**Rule {rule_index + 1}:**\n{rule_text}',
            color=discord.Color.orange(),
        )
        embed.add_field(
            name=f'⚠️ {t("ui.hub.rules.warnings.deleteWarning", locale="en")}',
            value=t('ui.hub.rules.warnings.deleteWarningDescription', locale='en'),
            inline=False,
        )
        return embed

    @staticmethod
    def create_error_embed(bot: 'Bot', error_message: str) -> discord.Embed:
        """Create standardized error embed."""

        return ResponseBuilder.create_error_embed(
            bot, error_message, t('ui.common.titles.error', locale='en')
        )

    @staticmethod
    def create_success_embed(bot: 'Bot', message: str) -> discord.Embed:
        """Create standardized success embed."""

        return ResponseBuilder.create_success_embed(
            bot, message, t('ui.common.titles.success', locale='en')
        )


class RulesUIBuilder:
    """Builder for creating rules UI components."""

    @staticmethod
    def create_rule_selection_options(
        bot: 'Bot',
        rules: List[str],
        start_idx: int,
        end_idx: int,
    ) -> List[discord.SelectOption]:
        """Create selection options for rules dropdown."""
        options = []
        for i in range(start_idx, end_idx):
            rule = rules[i]
            # Truncate rule text for option display
            rule_preview = rule[:80] + '...' if len(rule) > 80 else rule
            options.append(
                discord.SelectOption(
                    label=f'Rule {i + 1}',
                    description=rule_preview,
                    value=str(i),
                    emoji=bot.emotes.rules_icon,
                )
            )
        return options

    @staticmethod
    def create_action_buttons(
        bot: 'Bot',
        edit_callback: Callable,
        delete_callback: Callable,
        cancel_callback: Callable,
    ) -> List[discord.ui.Button]:
        """Create action buttons for rule management."""
        buttons = []

        # Edit button
        edit_button = discord.ui.Button(
            label=t('ui.hub.rules.editRule.label', locale='en') or 'Edit Rule',
            emoji=bot.emotes.edit_icon,
            style=discord.ButtonStyle.primary,
        )
        edit_button.callback = edit_callback
        buttons.append(edit_button)

        # Delete button
        delete_button = discord.ui.Button(
            label=t('ui.hub.rules.deleteRule.label', locale='en') or 'Delete Rule',
            emoji=bot.emotes.deleteDanger_icon,
            style=discord.ButtonStyle.danger,
        )
        delete_button.callback = delete_callback
        buttons.append(delete_button)

        # Cancel button
        cancel_button = discord.ui.Button(
            label=t('ui.setup.buttons.cancel', locale='en') or 'Cancel',
            emoji=bot.emotes.x_icon,
            style=discord.ButtonStyle.secondary,
        )
        cancel_button.callback = cancel_callback
        buttons.append(cancel_button)

        return buttons

    @staticmethod
    def create_confirmation_buttons(
        bot: 'Bot',
        confirm_callback: Callable,
        cancel_callback: Callable,
    ) -> List[discord.ui.Button]:
        """Create confirmation buttons for deletion."""
        buttons = []

        # Confirm delete button
        confirm_button = discord.ui.Button(
            label=t('ui.hub.rules.deleteRule.label', locale='en') or 'Delete Rule',
            emoji=bot.emotes.deleteDanger_icon,
            style=discord.ButtonStyle.danger,
        )
        confirm_button.callback = confirm_callback
        buttons.append(confirm_button)

        # Cancel button
        cancel_button = discord.ui.Button(
            label=t('ui.setup.buttons.cancel', locale='en') or 'Cancel',
            emoji=bot.emotes.x_icon,
            style=discord.ButtonStyle.secondary,
        )
        cancel_button.callback = cancel_callback
        buttons.append(cancel_button)

        return buttons
