from typing import TYPE_CHECKING, List, Optional

import discord

from db.models import AntiSwearRule, BlockWordAction, Hub
from utils.modules.services.profanityConfigService import ProfanityConfigService
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.views.hubConfig.utils import BaseHubView

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.profanitySettingsView import ProfanitySettingsView


class ProfanityActionSelectionView(BaseHubView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        permission,
        rule_name: str,
        parent_view: 'ProfanitySettingsView',
        locale: str,
        existing_rule: Optional['AntiSwearRule'] = None,  # For editing existing rules
        timeout: int = 300,
    ):
        super().__init__(bot, user, hub, permission, locale, timeout)
        self.parent_view = parent_view
        self.rule_name = rule_name
        self.existing_rule = existing_rule
        self.selected_actions: List[BlockWordAction] = []
        self.mute_duration_minutes: Optional[int] = None
        self.log_violations: bool = True

        # If editing existing rule, load its current settings
        if self.existing_rule:
            self.selected_actions = list(self.existing_rule.actions or [])
            self.mute_duration_minutes = self.existing_rule.muteDurationMinutes

        # Build the UI components
        self._build()

    def _build(self):
        self.clear_items()

        # Default selected actions
        if not self.selected_actions:
            self.selected_actions = [BlockWordAction.BLOCK_MESSAGE, BlockWordAction.SEND_ALERT]

        # Multi-select for actions
        options = [
            discord.SelectOption(
                label='Block Message',
                value=BlockWordAction.BLOCK_MESSAGE.value,
                emoji='🚫',
                default=BlockWordAction.BLOCK_MESSAGE in self.selected_actions,
            ),
            discord.SelectOption(
                label='Warn User',
                value=BlockWordAction.WARN.value,
                emoji='⚠️',
                default=BlockWordAction.WARN in self.selected_actions,
            ),
            discord.SelectOption(
                label='Mute User',
                value=BlockWordAction.MUTE.value,
                emoji='🔇',
                default=BlockWordAction.MUTE in self.selected_actions,
            ),
            discord.SelectOption(
                label='Ban User',
                value=BlockWordAction.BAN.value,
                emoji='🔨',
                default=BlockWordAction.BAN in self.selected_actions,
            ),
            discord.SelectOption(
                label='Send Alert',
                value=BlockWordAction.SEND_ALERT.value,
                emoji='📢',
                default=BlockWordAction.SEND_ALERT in self.selected_actions,
            ),
        ]
        action_count = len(self.selected_actions)
        self.action_select = discord.ui.Select(
            placeholder=f'Select actions... ({action_count} selected)',
            options=options,
            min_values=1,
            max_values=len(options),
            row=0,
        )

        async def on_select(interaction: discord.Interaction['Bot']):
            # Update selected actions from select values
            try:
                self.selected_actions = [BlockWordAction[v] for v in self.action_select.values]
            except Exception:
                self.selected_actions = []

            # Rebuild the select options with updated defaults
            updated_options = [
                discord.SelectOption(
                    label='Block Message',
                    value=BlockWordAction.BLOCK_MESSAGE.value,
                    emoji='🚫',
                    default=BlockWordAction.BLOCK_MESSAGE in self.selected_actions,
                ),
                discord.SelectOption(
                    label='Warn User',
                    value=BlockWordAction.WARN.value,
                    emoji='⚠️',
                    default=BlockWordAction.WARN in self.selected_actions,
                ),
                discord.SelectOption(
                    label='Mute User',
                    value=BlockWordAction.MUTE.value,
                    emoji='🔇',
                    default=BlockWordAction.MUTE in self.selected_actions,
                ),
                discord.SelectOption(
                    label='Ban User',
                    value=BlockWordAction.BAN.value,
                    emoji='🔨',
                    default=BlockWordAction.BAN in self.selected_actions,
                ),
                discord.SelectOption(
                    label='Send Alert',
                    value=BlockWordAction.SEND_ALERT.value,
                    emoji='📢',
                    default=BlockWordAction.SEND_ALERT in self.selected_actions,
                ),
            ]
            self.action_select.options = updated_options

            # Update placeholder to show selection count
            action_count = len(self.selected_actions)
            self.action_select.placeholder = f'Select actions... ({action_count} selected)'

            # Refresh embed
            embed = self.create_embed()
            await interaction.response.edit_message(embed=embed, view=self)

        self.action_select.callback = on_select

        # Configuration and create/update buttons
        config_btn = discord.ui.Button(
            label='Configure Settings',
            emoji=self.bot.emotes.gear_icon,
            style=discord.ButtonStyle.primary,
            row=1,
        )
        action_btn = discord.ui.Button(
            label='Update Rule' if self.existing_rule else 'Create Rule',
            emoji='💾' if self.existing_rule else self.bot.emotes.tick,
            style=discord.ButtonStyle.success,
            row=1,
        )
        config_btn.callback = self._configure_settings
        action_btn.callback = self._update_rule if self.existing_rule else self._create_rule

        self.add_item(self.action_select)
        self.add_item(config_btn)
        self.add_item(action_btn)
        self.add_back_button(self.parent_view, row=2)

    def create_embed(self) -> discord.Embed:
        mode = 'Edit' if self.existing_rule else 'Create'
        desc = f'### {self.bot.emotes.gear_icon if self.existing_rule else self.bot.emotes.plus_icon} {mode} Rule: {self.rule_name}\n'
        desc += f'{"Update the actions" if self.existing_rule else "Select the actions"} to take when this rule is triggered.\n\n'

        desc += '**Selected Actions:**\n'
        if self.selected_actions:
            for action in self.selected_actions:
                action_desc = self._get_action_description(action)
                desc += f'- {action_desc}\n'
        else:
            desc += '- None selected\n'

        desc += '\n**Configuration:**\n'
        if self.mute_duration_minutes:
            desc += f'- Mute Duration: {self.mute_duration_minutes} minutes\n'
        desc += f'- Log Violations: {"Yes" if self.log_violations else "No"}\n'

        embed = discord.Embed(description=desc, color=self.bot.constants.color)
        embed.set_footer(text=f'Hub: {self.hub.name}')
        return embed

    def _get_action_description(self, action: BlockWordAction) -> str:
        """Get a user-friendly description for an action."""
        descriptions = {
            BlockWordAction.BLOCK_MESSAGE: '🚫 Block the message',
            BlockWordAction.WARN: '⚠️ Warn the user',
            BlockWordAction.MUTE: f'🔇 Mute user ({self.mute_duration_minutes or 60} minutes)',
            BlockWordAction.BAN: '🔨 Ban the user',
            BlockWordAction.SEND_ALERT: '📢 Send alert to moderators',
        }
        return descriptions.get(action, action.value)

    async def _toggle_action(
        self,
        interaction: discord.Interaction,
        action: BlockWordAction,
        button: discord.ui.Button,
    ):
        """Toggle an action on/off."""
        if action in self.selected_actions:
            self.selected_actions.remove(action)
            button.style = discord.ButtonStyle.secondary
        else:
            self.selected_actions.append(action)
            button.style = discord.ButtonStyle.success

        embed = self.create_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    async def _configure_settings(self, interaction: discord.Interaction['Bot']):
        mute_duration_input = discord.ui.TextInput(
            label='Mute Duration (minutes)',
            placeholder='Duration for mute action (default: 60)',
            default=str(self.mute_duration_minutes or 60),
            max_length=5,
            required=False,
        )
        log_input = discord.ui.TextInput(
            label='Log Violations (true/false)',
            placeholder='Whether to log violations (default: true)',
            default=str(self.log_violations).lower(),
            max_length=5,
            required=False,
        )

        modal = CustomModal(
            title='Configure Rule Settings',
            options=[
                ('mute_duration', mute_duration_input),
                ('log_violations', log_input),
            ],
        )
        await interaction.response.send_modal(modal)
        if await modal.wait():
            return
        if not modal.interaction:
            return

        if modal.saved_items['mute_duration'].value:
            self.mute_duration_minutes = max(1, int(modal.saved_items['mute_duration'].value))

        log_str = modal.saved_items['log_violations'].value.strip().lower()
        if log_str in ('true', 'false'):
            self.log_violations = log_str == 'true'

        embed = self.create_embed()
        await modal.interaction.followup.send(
            f'{self.bot.emotes.gear_icon} Settings updated!', ephemeral=True
        )
        await interaction.edit_original_response(embed=embed, view=self)

    async def _create_rule(self, interaction: discord.Interaction['Bot']):
        """Create the rule with selected actions and settings."""
        if not self.selected_actions:
            await interaction.response.send_message(
                f'{self.bot.emotes.cross} Please select at least one action before creating the rule.',
                ephemeral=True,
            )
            return

        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            success, rule = await svc.create_rule(
                hub_id=self.hub.id,
                name=self.rule_name,
                created_by=str(self.user.id),
                actions=self.selected_actions,
                mute_duration_minutes=self.mute_duration_minutes,
            )

        if success and rule:
            # Send ephemeral confirmation and navigate back to parent view
            await interaction.response.send_message(
                f'{self.bot.emotes.tick} Rule "{self.rule_name}" created successfully!',
                ephemeral=True,
            )
            # Since we already responded, we need to manually rebuild the parent view
            await self.parent_view.load_data()
            embed = self.parent_view.create_embed()
            await interaction.edit_original_response(embed=embed, view=self.parent_view)
            return
        else:
            await interaction.response.send_message(
                f'{self.bot.emotes.cross} Failed to create rule. Please try again.', ephemeral=True
            )

    async def _update_rule(self, interaction: discord.Interaction['Bot']):
        """Update the existing rule with selected actions and settings."""
        if not self.selected_actions:
            await interaction.response.send_message(
                f'{self.bot.emotes.cross} Please select at least one action before updating the rule.',
                ephemeral=True,
            )
            return

        if not self.existing_rule:
            await interaction.response.send_message(
                f'{self.bot.emotes.cross} No existing rule to update.', ephemeral=True
            )
            return

        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            success = await svc.update_rule(
                rule_id=self.existing_rule.id,
                actions=self.selected_actions,
                mute_duration_minutes=self.mute_duration_minutes,
                log_violations=self.log_violations,
            )

        if success:
            # Send success message and navigate back to parent
            await interaction.response.send_message(
                f'{self.bot.emotes.tick} Rule "{self.rule_name}" updated successfully!',
                ephemeral=True,
            )
            # Since we already responded, we need to manually rebuild the parent view
            await self.parent_view.load_data()
            embed = self.parent_view.create_embed()
            await interaction.edit_original_response(embed=embed, view=self.parent_view)
            return
        else:
            await interaction.response.send_message(
                f'{self.bot.emotes.cross} Failed to update rule. Please try again.', ephemeral=True
            )
