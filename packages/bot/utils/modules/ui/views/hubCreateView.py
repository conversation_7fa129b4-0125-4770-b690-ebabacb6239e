from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, TYPE_CHECKING

import discord
from discord.ui import View, Button, TextInput
from sqlmodel import col, select
from sqlalchemy.exc import IntegrityError

from utils.modules.core.i18n import t
from utils.constants import logger
from db.models import Hub
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.common.validation_utils import TextValidators

if TYPE_CHECKING:
    from main import Bot


@dataclass
class _HubDraft:
    name: Optional[str] = None
    short_description: Optional[str] = None
    private: Optional[bool] = None  # True=Private, False=Public


class HubCreationView(View):
    def __init__(self, bot: Bot, user: discord.abc.User, locale):
        super().__init__(timeout=300)
        self.bot: Bot = bot
        self.constants = bot.constants
        self.initiator: discord.abc.User = user
        self.message: Optional[discord.Message] = None
        self.draft = _HubDraft()
        self.locale = locale

        # Initial button (Create)
        self.create_button = Button(
            label=t('ui.common.labels.create', locale=locale),
            style=discord.ButtonStyle.blurple,
            emoji=bot.emotes.wand_icon,
        )
        self.create_button.callback = self._on_click_create
        self.add_item(self.create_button)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.initiator.id:
            await interaction.response.send_message("You can't use this interface.", ephemeral=True)
            return False
        return True

    # ========= Stage 1: Modal (name + short description) =========
    async def _on_click_create(self, interaction: discord.Interaction):
        name_input = TextInput(
            label=t('ui.hub.creation.modal.hubName', locale=self.locale),
            placeholder='InterChat',
            required=True,
            min_length=2,
            max_length=50,
        )
        short_desc_input = TextInput(
            label=t('ui.hub.creation.modal.shortLabel', locale=self.locale),
            placeholder='This hub has cool people!',
            required=True,
            min_length=10,
            max_length=100,
            style=discord.TextStyle.paragraph,
        )

        modal = CustomModal(
            title=t('ui.hub.creation.modal.title', locale=self.locale),
            options=[('hub_name', name_input), ('short_desc', short_desc_input)],
        )

        await interaction.response.send_modal(modal)
        submitted = not await modal.wait()
        if not submitted:
            return  # timed out / closed

        # Extract values
        hub_name = modal.saved_items['hub_name'].value.strip()
        short_desc = modal.saved_items['short_desc'].value.strip()

        # Validate inputs
        err = await self._validate_name_and_desc(hub_name, short_desc)
        if err:
            embed = discord.Embed(
                title=t('ui.hub.creation.errors.invalidInput', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {err}',
                color=discord.Color.red(),
            )
            return await interaction.followup.send(embed=embed, ephemeral=True)

        # Persist draft
        self.draft.name = hub_name
        self.draft.short_description = short_desc

        # Next stage: visibility selection
        await self._show_visibility_selection(interaction)

    async def _validate_name_and_desc(self, name: str, short_desc: str) -> Optional[str]:
        name_validation = TextValidators.validate_hub_name(name)
        if not name_validation.is_valid:
            return t('ui.hub.creation.errors.name', locale=self.locale)

        # Validate description length
        desc_validation = TextValidators.validate_length(short_desc, 10, 100, 'short description')
        if not desc_validation.is_valid:
            return t('ui.hub.creation.errors.shortDescription', locale=self.locale)

        # Ensure unique (case-insensitive)
        async with self.bot.db.get_session() as session:
            existing = (await session.exec(select(Hub).where(col(Hub.name).ilike(name)))).first()
            if existing:
                return t('ui.hub.creation.errors.unique', locale=self.locale)
        return None

    # ========= Stage 2: Visibility selection =========
    async def _show_visibility_selection(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title=t('ui.hub.creation.visibility.title', locale=self.locale),
            description=t(
                'ui.hub.creation.visibility.description',
                locale=self.locale,
                discoverHubs='https://interchat.tech/hubs',
            ),
            color=self.constants.color,
        )

        # Replace view with Public / Private buttons
        self.clear_items()
        public_btn = Button(
            label=t('ui.hub.creation.visibility.buttons.public', locale=self.locale),
            style=discord.ButtonStyle.green,
            emoji=self.bot.emotes.unlock_icon,
        )
        private_btn = Button(
            label=t('ui.hub.creation.visibility.buttons.private', locale=self.locale),
            style=discord.ButtonStyle.grey,
            emoji=self.bot.emotes.lock_icon,
        )
        public_btn.callback = self._on_select_public
        private_btn.callback = self._on_select_private
        self.add_item(public_btn)
        self.add_item(private_btn)

        await self._safe_edit(interaction, embed=embed, view=self)

    async def _on_select_public(self, interaction: discord.Interaction):
        self.draft.private = False
        await self._show_preview(interaction)

    async def _on_select_private(self, interaction: discord.Interaction):
        self.draft.private = True
        await self._show_preview(interaction)

    # ========= Stage 3: Preview + Confirm/Cancel =========
    async def _show_preview(self, interaction: discord.Interaction):
        name = self.draft.name or '(missing)'
        sdesc = self.draft.short_description or '(missing)'
        visibility = (
            t('ui.hub.creation.visibility.buttons.private', locale=self.locale)
            if self.draft.private
            else t('ui.hub.creation.visibility.buttons.public', locale=self.locale)
        )
        vemoji = self.bot.emotes.lock_icon if self.draft.private else self.bot.emotes.unlock_icon

        embed = discord.Embed(
            title=t('ui.hub.creation.preview.title', locale=self.locale),
            description=(t('ui.hub.creation.preview.description', locale=self.locale)),
            color=self.constants.color,
        )
        embed.add_field(
            name=t('ui.hub.creation.preview.fields.title', locale=self.locale),
            value=t(
                'ui.hub.creation.preview.fields.value',
                locale=self.locale,
                nemoji=self.bot.emotes.house_icon,
                hubName=name,
                sdemoji=self.bot.emotes.hash_icon,
                shortDescription=sdesc,
                vemoji=vemoji,
                visibility=visibility,
                ldemoji=self.bot.emotes.wiki_icon,
                dashboard='https://interchat.tech/dashboard',
            ),
        )
        embed.set_footer(text=t('ui.hub.creation.preview.footer', locale=self.locale))

        self.clear_items()
        confirm_btn = Button(
            label=t('ui.common.labels.create', locale=self.locale),
            style=discord.ButtonStyle.success,
            emoji=self.bot.emotes.tick,
        )
        cancel_btn = Button(
            label=t('ui.common.labels.cancel', locale=self.locale),
            style=discord.ButtonStyle.grey,
            emoji=self.bot.emotes.x_icon,
        )
        confirm_btn.callback = self._on_confirm
        cancel_btn.callback = self._on_cancel
        self.add_item(confirm_btn)
        self.add_item(cancel_btn)

        await self._safe_edit(interaction, embed=embed, view=self)

    # ========= Stage 4: Create or Cancel =========
    async def _on_confirm(self, interaction: discord.Interaction):
        # Re-validate (race conditions / uniqueness)
        assert self.draft.name and self.draft.short_description and self.draft.private is not None
        err = await self._validate_name_and_desc(self.draft.name, self.draft.short_description)
        if err:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {err}',
                color=discord.Color.red(),
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)

        # Persist
        hub_id: Optional[str] = None
        try:
            async with self.bot.db.get_session() as session:
                hub = Hub(
                    name=self.draft.name,
                    shortDescription=self.draft.short_description,
                    description='None',
                    ownerId=str(interaction.user.id),
                    iconUrl='',
                    rules=[],
                    private=bool(self.draft.private),
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                    lastActive=datetime.now(),
                )
                session.add(hub)
                await session.commit()
                hub_id = hub.id
        except IntegrityError:
            # Likely unique constraint on hub name
            await interaction.response.send_message(
                embed=discord.Embed(
                    title=t('ui.common.titles.error', locale=self.locale),
                    description=f'{self.bot.emotes.x_icon} {t("ui.hub.creation.errors.unique", locale=self.locale)}',
                    color=discord.Color.red(),
                ),
                ephemeral=True,
            )
            return
        except Exception as e:
            logger.error(f'Failed to create hub: {e}')
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("ui.hub.creation.errors.unhandled", locale=self.locale, supportServer=self.constants.support_invite)}',
                color=discord.Color.red(),
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)

        dashboard_url = f'https://interchat.tech/dashboard/hubs/{hub_id}'
        public_url = f'https://interchat.tech/hubs/{hub_id}'

        desc_lines = [
            f'{self.bot.emotes.tick} {t("ui.hub.creation.complete.description", locale=self.locale, dashboard=dashboard_url)}'
        ]
        if self.draft.private is False:
            desc_lines.append(
                f'{self.bot.emotes.dot} {t("ui.hub.creation.complete.public", locale=self.locale, public=public_url)}'
            )
        else:
            desc_lines.append(
                f'{self.bot.emotes.dot} {t("ui.hub.creation.complete.private", locale=self.locale)}'
            )

        embed = discord.Embed(
            description='\n'.join(desc_lines),
            color=discord.Color.green(),
        )

        self.clear_items()  # end of flow
        await self._safe_edit(interaction, embed=embed, view=None)
        self.stop()

    async def _on_cancel(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title=t('ui.common.titles.cancelled', locale=self.locale),
            description=f'{self.bot.emotes.x_icon} {t("ui.hub.creation.other.cancelled", locale=self.locale)}',
            color=discord.Color.red(),
        )
        self.clear_items()
        await self._safe_edit(interaction, embed=embed, view=None)
        self.stop()

    # ========= Helpers =========
    async def _safe_edit(
        self, interaction: discord.Interaction, *, embed: discord.Embed, view: Optional[View]
    ):
        try:
            if interaction.response.is_done():
                await interaction.edit_original_response(embed=embed, view=view)
            else:
                await interaction.response.edit_message(embed=embed, view=view)
        except discord.NotFound:
            if view:
                # Original message deleted; try sending a new ephemeral followup
                await interaction.followup.send(embed=embed, view=view, ephemeral=True)
