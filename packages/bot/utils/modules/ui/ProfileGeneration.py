import discord

from jinja2 import Template
from playwright.async_api import async_playwright
import os
import asyncio

from data.profile import profile_template

_browser = None
_browser_lock = asyncio.Lock()


async def get_browser():
    global _browser
    async with _browser_lock:
        if _browser is None or not _browser.is_connected():
            playwright = await async_playwright().start()
            _browser = await playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                ],
            )
        return _browser


async def capture_html_screenshot(
    html_content: str, output_path: str, width: int = 800, height: int = 600
):
    browser = await get_browser()

    context = await browser.new_context(
        viewport={'width': width, 'height': height}, device_scale_factor=1
    )

    try:
        page = await context.new_page()

        await page.set_content(html_content, wait_until='domcontentloaded')

        try:
            await page.wait_for_selector('.profile-card', timeout=3000)
            profile_card = await page.query_selector('.profile-card')
            await profile_card.screenshot(path=output_path, type='png')
        except Exception:
            await page.screenshot(path=output_path, type='png', full_page=True)

    finally:
        await context.close()


async def generate_profile(interaction: discord.Interaction, achievements, badges):
    template = Template(profile_template)

    is_interaction = isinstance(interaction, discord.Interaction)
    user = interaction.user if is_interaction else interaction.author

    author = interaction.author
    username = user.display_name or user.name

    words = username.split()
    if len(words) >= 2:
        initials = words[0][0].upper() + words[1][0].upper()
    else:
        initials = username[0].upper() if username else '?'

    avatar_url = user.avatar.url if user.avatar else None

    profile_data = {
        'username': f'@{user.name}',
        'guild_tag': f'#{interaction.guild.name}' if interaction.guild else '#Direct Message',
        'avatar_url': avatar_url,
        'avatar_initials': initials,
        'message_count': '0',
        'global_rank': '#0',
        'server_rank': '#0',
    }

    rendered_html = template.render(profile=profile_data, achievements=achievements, badges=badges)

    profile_dir = 'data/profiles'
    os.makedirs(profile_dir, exist_ok=True)

    screenshot_path = f'{profile_dir}/{user.id}_profile.png'
    await capture_html_screenshot(rendered_html, screenshot_path, width=1180, height=800)
    return screenshot_path


async def cleanup_browser():
    global _browser
    if _browser:
        await _browser.close()
        _browser = None


async def cleanup(path):
    os.remove(path)
