from typing import TYPE_CHECKING
from discord import app_commands
import discord

from db.models import <PERSON><PERSON>, HubModerator

from sqlmodel import select, or_, col

if TYPE_CHECKING:
    from main import Bot


# Hubs : Visiblity - Public
async def _hubp_autocomplete(
    interaction: discord.Interaction['Bot'], current: str
) -> list[app_commands.Choice[str]]:
    async with interaction.client.db.get_session() as session:
        stmt = (
            select(Hub.name)
            .where(col(Hub.private).is_(False), col(Hub.name).contains(current))
            .limit(25)
        )
        hubs = (await session.exec(stmt)).all()

    choices = []

    for hub in hubs:
        hub_name = hub[0]
        choices.append(app_commands.Choice(name=hub_name, value=hub_name))
    return choices[:25]


def hubp_autocomplete(func):
    return app_commands.autocomplete(hub=_hubp_autocomplete)(func)


# Hubs : Moderator+
async def _hubm_autocomplete(
    interaction: discord.Interaction['Bot'], current: str
) -> list[app_commands.Choice[str]]:
    user_id = str(interaction.user.id)

    async with interaction.client.db.get_session() as session:
        stmt = select(Hub.name).where(
            or_(Hub.ownerId == user_id, Hub.moderators.any(HubModerator.userId == user_id)),
            col(Hub.name).contains(current),
        )
        hubs = (await session.exec(stmt)).all()

    choices = []
    for hub in hubs:
        name = hub[0]
        choices.append(app_commands.Choice(name=name, value=name))

    return choices[:25]


def hubm_autocomplete(func):
    return app_commands.autocomplete(hub=_hubm_autocomplete)(func)


# Hub : Owner
async def _hubo_autocomplete(
    interaction: discord.Interaction['Bot'], current: str
) -> list[app_commands.Choice[str]]:
    user_id = str(interaction.user.id)

    async with interaction.client.db.get_session() as session:
        stmt = select(Hub.name).where(Hub.ownerId == user_id, col(Hub.name).contains(current))
        hubs = (await session.exec(stmt)).all()

    choices = []
    for hub in hubs:
        hub_name = hub[0]
        choices.append(app_commands.Choice(name=hub_name, value=hub_name))

    return choices[:25]


def hubo_autocomplete(func):
    return app_commands.autocomplete(hub=_hubo_autocomplete)(func)
