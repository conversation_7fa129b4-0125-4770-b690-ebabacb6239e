from io import By<PERSON><PERSON>
from typing import TYPE_CHECKING, Optional
import discord
from discord.ext import commands

from jinja2 import Template
from playwright.async_api import async_playwright
import asyncio

from utils.utils import load_profile_data, abbreviate_number
from data.profile import profile_template
from data.leaderboard import leaderboard_template

if TYPE_CHECKING:
    from main import Bot
    from utils.interfaces import InterChatBadge

_browser = None
_browser_lock = asyncio.Lock()


async def get_browser():
    global _browser
    async with _browser_lock:
        if _browser is None or not _browser.is_connected():
            playwright = await async_playwright().start()
            _browser = await playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                ],
            )
        return _browser


async def capture_html_screenshot(
    html_content: str, width: int = 800, height: int = 600, selector: Optional[str] = None
) -> BytesIO:
    browser = await get_browser()

    context = await browser.new_context(
        viewport={'width': width, 'height': height}, device_scale_factor=1
    )

    buffer = BytesIO()

    try:
        page = await context.new_page()
        await page.set_content(html_content, wait_until='networkidle')

        try:
            # Use provided selector or default to profile-card
            target_selector = selector or '.profile-card'
            await page.wait_for_selector(target_selector, timeout=3000)
            target_element = await page.query_selector(target_selector)
            ss_buffer = await target_element.screenshot(type='png')  # pyright: ignore[reportOptionalMemberAccess]
            buffer.write(ss_buffer)
        except Exception:
            ss_buffer = await page.screenshot(type='png', full_page=True)
            buffer.write(ss_buffer)

        buffer.seek(0)
        return buffer
    finally:
        await context.close()


async def generate_profile(
    ctx: discord.Interaction['Bot'] | commands.Context['Bot'],
    achievements: list[dict[str, str]],
    badges: list['InterChatBadge'],
    user: discord.User | discord.Member,
) -> BytesIO:
    template = Template(profile_template)
    username = user.display_name or user.name

    words = username.split()
    if len(words) >= 2:
        initials = words[0][0].upper() + words[1][0].upper()
    else:
        initials = username[0].upper() if username else '?'

    avatar_url = user.avatar.url if user.avatar else None
    loaded_data = await load_profile_data(user)

    profile_data = {
        'username': f'@{user.name}',
        'guild_tag': (f'#{ctx.guild.name}' if ctx.guild else '#Direct Message'),
        'avatar_url': avatar_url,
        'avatar_initials': initials,
        'message_count': abbreviate_number(loaded_data.messageCount if loaded_data else 0),
        'global_rank': '#1',
        'reputation': loaded_data.reputation if loaded_data else 0,
    }

    rendered_html = template.render(profile=profile_data, achievements=achievements, badges=badges)

    return await capture_html_screenshot(rendered_html, width=1180, height=800)


async def generate_leaderboard(
    leaderboard_data: list[dict],
    stat1_label: str = 'Messages',
    stat2_label: str = 'Reputation',
) -> BytesIO:
    template = Template(leaderboard_template)

    processed_users = []
    for user_data in leaderboard_data:
        username = user_data.get('username', 'Unknown')

        # Generate initials
        words = username.split()
        if len(words) >= 2:
            initials = words[0][0].upper() + words[1][0].upper()
        else:
            initials = username[0].upper() if username else 'U'

        stat1_value = user_data.get('stat1_value', 0)
        stat2_value = user_data.get('stat2_value', 0)

        processed_user = {
            'rank': user_data.get('rank', 1),
            'username': username,
            'guild_tag': user_data.get('guild_tag', 'InterChat'),
            'avatar_url': user_data.get('avatar_url'),
            'avatar_initials': initials,
            'stat1_value': f'{stat1_value:,}',
            'stat2_value': f'{stat2_value:,}',
            'stat1_change': user_data.get('stat1_change'),
            'stat2_change': user_data.get('stat2_change'),
            'stat1_change_class': _get_change_class(user_data.get('stat1_change')),
            'stat2_change_class': _get_change_class(user_data.get('stat2_change')),
        }
        processed_users.append(processed_user)

    rendered_html = template.render(
        stat1_label=stat1_label, stat2_label=stat2_label, users=processed_users
    )

    return await capture_html_screenshot(
        rendered_html, width=900, height=800, selector='.leaderboard-container'
    )


def _get_change_class(change):
    if not change:
        return 'neutral'
    if change.startswith('+'):
        return 'positive'
    elif change.startswith('-'):
        return 'negative'
    return 'neutral'


async def cleanup_browser():
    global _browser
    if _browser:
        await _browser.close()
        _browser = None
