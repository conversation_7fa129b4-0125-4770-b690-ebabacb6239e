from typing import TYPE_CHECKING, Any, Callable, Optional
import discord
from discord.ui import Modal, TextInput

if TYPE_CHECKING:
    from main import Bot


class CustomModal(Modal, title='Edit Reason'):
    def __init__(
        self,
        title: str,
        options: list[tuple[str, TextInput[Any]]],
        args: Optional[dict] = None,
        callback: Optional[Callable[['CustomModal', discord.Interaction['Bot']], None]] = None,
    ):
        super().__init__(title=title)
        if args is None:
            args = {}
        self.saved_items = {}
        self.args = args
        self._custom_callback = callback
        self.interaction = None

        for name, option in options:
            self.add_item(option)
            self.saved_items[name] = option

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        for key, item in self.saved_items.items():
            setattr(self, key, item)
        self.interaction = interaction
        await interaction.response.defer(**self.args)

        if self._custom_callback:
            await self._custom_callback(self, interaction)  # type: ignore[override]

        self.stop()
