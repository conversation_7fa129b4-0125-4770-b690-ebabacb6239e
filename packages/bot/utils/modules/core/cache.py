import asyncio
import json
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from utils.constants import logger, redis_client

if TYPE_CHECKING:
    from utils.interfaces import InterChatBadge


class CacheManager:
    # Default TTL values (in seconds)
    DEFAULT_TTL = 300  # 5 minutes
    SHORT_TTL = 30  # 30 seconds
    MEDIUM_TTL = 600  # 10 minutes
    LONG_TTL = 3600  # 1 hour

    # Cache key prefixes for organization
    PREFIXES = {
        'user_badges': 'badges:user',
        'validation': 'validation',
        'webhook': 'webhook',
        'user_data': 'user',
        'hub_data': 'hub',
        'connection': 'connection',
        'rate_limit': 'rate_limit',
        'spam_check': 'spam_check',
        'spam_warnings': 'spam_warnings',
        'previous_message': 'previous_message',
    }

    def __init__(self):
        self.redis = redis_client
        self._lock = asyncio.Lock()

    def _build_key(self, prefix: str, *args: str) -> str:
        """Build a cache key with proper prefix and arguments."""
        if prefix not in self.PREFIXES:
            raise ValueError(f'Unknown cache prefix: {prefix}')

        base_key = self.PREFIXES[prefix]
        if args:
            return f'{base_key}:{":".join(str(arg) for arg in args)}'
        return base_key

    async def get(self, prefix: str, *args: str) -> Optional[Any]:
        """Get a value from cache."""
        try:
            key = self._build_key(prefix, *args)
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.warning(f'Cache get failed for {prefix}:{args}: {e}')
            return None

    async def set(self, prefix: str, *args: str, value: Any, ttl: int = DEFAULT_TTL) -> bool:
        """Set a value in cache with TTL."""
        try:
            key = self._build_key(prefix, *args)
            serialized_value = json.dumps(value, default=str)
            await self.redis.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.warning(f'Cache set failed for {prefix}:{args}: {e}')
            return False

    async def delete(self, prefix: str, *args: str) -> bool:
        """Delete a value from cache."""
        try:
            key = self._build_key(prefix, *args)
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.warning(f'Cache delete failed for {prefix}:{args}: {e}')
            return False

    async def exists(self, prefix: str, *args: str) -> bool:
        """Check if a key exists in cache."""
        try:
            key = self._build_key(prefix, *args)
            return bool(await self.redis.exists(key))
        except Exception as e:
            logger.warning(f'Cache exists check failed for {prefix}:{args}: {e}')
            return False

    async def increment(self, prefix: str, *args: str, amount: int = 1) -> Optional[int]:
        """Increment a numeric value in cache."""
        try:
            key = self._build_key(prefix, *args)
            return await self.redis.incrby(key, amount)
        except Exception as e:
            logger.warning(f'Cache increment failed for {prefix}:{args}: {e}')
            return None

    async def expire(self, prefix: str, *args: str, ttl: int) -> bool:
        """Set expiration time for a key."""
        try:
            key = self._build_key(prefix, *args)
            await self.redis.expire(key, ttl)
            return True
        except Exception as e:
            logger.warning(f'Cache expire failed for {prefix}:{args}: {e}')
            return False

    async def get_multiple(self, keys: List[tuple]) -> Dict[str, Any]:
        """Get multiple values from cache efficiently."""
        try:
            cache_keys = [self._build_key(prefix, *args) for prefix, *args in keys]
            values = await self.redis.mget(cache_keys)

            result = {}
            for i, (prefix, *args) in enumerate(keys):
                key_str = f'{prefix}:{":".join(str(arg) for arg in args)}'
                if values[i]:
                    result[key_str] = json.loads(values[i])
                else:
                    result[key_str] = None

            return result
        except Exception as e:
            logger.warning(f'Cache get_multiple failed: {e}')
            return {}

    async def set_multiple(self, items: Dict[tuple, tuple]) -> bool:
        """Set multiple values in cache efficiently."""
        try:
            pipe = self.redis.pipeline()
            for (prefix, *args), (value, ttl) in items.items():
                key = self._build_key(prefix, *args)
                serialized_value = json.dumps(value, default=str)
                pipe.setex(key, ttl, serialized_value)

            await pipe.execute()
            return True
        except Exception as e:
            logger.warning(f'Cache set_multiple failed: {e}')
            return False

    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern."""
        try:
            count = 0
            async for key in self.redis.scan_iter(pattern):
                await self.redis.delete(key)
                count += 1
            return count
        except Exception as e:
            logger.warning(f'Cache clear_pattern failed for {pattern}: {e}')
            return 0

    async def get_ttl(self, prefix: str, *args: str) -> Optional[int]:
        """Get the TTL of a key."""
        try:
            key = self._build_key(prefix, *args)
            return await self.redis.ttl(key)
        except Exception as e:
            logger.warning(f'Cache get_ttl failed for {prefix}:{args}: {e}')
            return None


class UserBadgeCache:
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager

    async def get_user_badges(self, user_id: str) -> Optional[tuple[bool, List['InterChatBadge']]]:
        """Get cached user badge data."""
        data = await self.cache.get('user_badges', user_id)
        if data:
            return data['show_badges'], data['badges']
        return None

    async def set_user_badges(
        self,
        user_id: str,
        show_badges: bool,
        badges: List['InterChatBadge'],
        ttl: int = CacheManager.DEFAULT_TTL,
    ) -> bool:
        """Cache user badge data."""
        data = {'show_badges': show_badges, 'badges': badges}
        return await self.cache.set('user_badges', user_id, value=data, ttl=ttl)

    async def clear_user_badges(self, user_id: str) -> bool:
        """Clear cached user badge data."""
        return await self.cache.delete('user_badges', user_id)

    async def clear_all_badges(self) -> int:
        """Clear all cached badge data."""
        return await self.cache.clear_pattern(f'{self.cache.PREFIXES["user_badges"]}:*')


class ValidationCache:
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager

    async def get_validation_result(
        self, user_id: str, guild_id: str, hub_id: str
    ) -> Optional[dict]:
        """Get cached validation result."""
        return await self.cache.get('validation', user_id, guild_id, hub_id)

    async def set_validation_result(
        self,
        user_id: str,
        guild_id: str,
        hub_id: str,
        result: dict,
        ttl: int = CacheManager.SHORT_TTL,
    ) -> bool:
        """Cache validation result."""
        return await self.cache.set('validation', user_id, guild_id, hub_id, value=result, ttl=ttl)

    async def clear_validation_result(self, user_id: str, guild_id: str, hub_id: str) -> bool:
        """Clear cached validation result."""
        return await self.cache.delete('validation', user_id, guild_id, hub_id)


class WebhookCache:
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager

    async def get_webhook_url(self, channel_id: str) -> Optional[str]:
        """Get cached webhook URL."""
        return await self.cache.get('webhook', channel_id)

    async def set_webhook_url(
        self, channel_id: str, webhook_url: str, ttl: int = CacheManager.MEDIUM_TTL
    ) -> bool:
        """Cache webhook URL."""
        return await self.cache.set('webhook', channel_id, value=webhook_url, ttl=ttl)

    async def clear_webhook_url(self, channel_id: str) -> bool:
        """Clear cached webhook URL."""
        return await self.cache.delete('webhook', channel_id)


class PreviousMessageCache:
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager

    async def get_previous_author(self, hub_id: str) -> Optional[str]:
        """Get the author ID of the previous message in a hub."""
        return await self.cache.get('previous_message', hub_id)

    async def set_previous_author(
        self, hub_id: str, author_id: str, ttl: int = CacheManager.DEFAULT_TTL
    ) -> bool:
        return await self.cache.set('previous_message', hub_id, value=author_id, ttl=ttl)

    async def clear_previous_author(self, hub_id: str) -> bool:
        return await self.cache.delete('previous_message', hub_id)


# Global cache manager instance
cache_manager = CacheManager()
user_badge_cache = UserBadgeCache(cache_manager)
validation_cache = ValidationCache(cache_manager)
webhook_cache = WebhookCache(cache_manager)
previous_message_cache = PreviousMessageCache(cache_manager)
