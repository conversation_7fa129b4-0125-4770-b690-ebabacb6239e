import aiohttp
import asyncio
from utils.constants import constants, logger


async def pingServer(session):
    try:
        async with session.get(constants.heartbeat_url):
            return {200: 'Success'}
    except Exception as e:
        logger.error(f'Failed to send heartbeat. Service may be marked as unavailable - {e}')


async def pingServerLoop():
    if not constants.production:
        return

    async with aiohttp.ClientSession() as session:
        try:
            while True:
                await pingServer(session)
                await asyncio.sleep(30)
        except asyncio.CancelledError:
            logger.info('Ping loop cancelled.')
