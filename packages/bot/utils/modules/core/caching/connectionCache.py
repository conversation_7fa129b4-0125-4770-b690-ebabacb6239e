"""
High-performance Redis-based caching for Connection+Hub data.
Reduces database load from 1000 queries/sec to cache hits.
"""

import asyncio
import json
from typing import Optional, Tuple, Dict, Any
import redis.asyncio as redis
from utils.constants import logger
from db.models import Connection, Hub, HubRulesAcceptance


class ConnectionHubCache:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.cache_ttl = 300  # 5 minutes TTL for hot data
        self.batch_invalidation_queue = asyncio.Queue()

    async def get_connection_hub_data(
        self, channel_id: str, user_id: str
    ) -> Optional[Tuple[Dict[str, Any], Dict[str, Any], Optional[Dict[str, Any]]]]:
        """
        Get cached connection+hub+rules data.
        Returns tuple of (connection_dict, hub_dict, rules_dict) or None.
        """
        cache_key = f'conn_hub:{channel_id}'
        rules_key = f'hub_rules:{channel_id}:{user_id}'

        try:
            # Use pipeline for atomic multi-get
            pipe = self.redis.pipeline()
            pipe.get(cache_key)
            pipe.get(rules_key)
            results = await pipe.execute()

            conn_hub_data, rules_data = results

            if conn_hub_data is None:
                return None

            # Parse cached data
            data = json.loads(conn_hub_data)
            connection_dict = data['connection']
            hub_dict = data['hub']
            rules_dict = json.loads(rules_data) if rules_data else None

            return (connection_dict, hub_dict, rules_dict)

        except Exception as e:
            logger.warning(f'Cache get failed for channel {channel_id}: {e}')
            return None

    async def set_connection_hub_data(
        self,
        channel_id: str,
        user_id: str,
        connection: Connection,
        hub: Hub,
        rules_acceptance: Optional[HubRulesAcceptance] = None,
    ):
        """Cache connection+hub data with TTL."""
        cache_key = f'conn_hub:{channel_id}'
        rules_key = f'hub_rules:{channel_id}:{user_id}'

        try:
            # Convert SQLAlchemy models to dicts
            conn_hub_data = {
                'connection': {
                    'id': connection.id,
                    'channelId': connection.channelId,
                    'hubId': connection.hubId,
                    'serverId': connection.serverId,
                    'webhookURL': connection.webhookURL,
                    'connected': connection.connected,
                    'parentId': connection.parentId,
                },
                'hub': {
                    'id': hub.id,
                    'name': hub.name,
                    'description': hub.description,
                    'ownerId': hub.ownerId,
                    'private': hub.private,
                    'locked': hub.locked,
                    'nsfw': hub.nsfw,
                    'settings': hub.settings,
                    'rules': hub.rules,
                },
            }

            rules_data = None
            if rules_acceptance:
                rules_data = {
                    'id': rules_acceptance.id,
                    'userId': rules_acceptance.userId,
                    'hubId': rules_acceptance.hubId,
                    'acceptedAt': rules_acceptance.acceptedAt.isoformat()
                    if rules_acceptance.acceptedAt
                    else None,
                }

            # Use pipeline for atomic multi-set
            pipe = self.redis.pipeline()
            pipe.setex(cache_key, self.cache_ttl, json.dumps(conn_hub_data))
            if rules_data:
                pipe.setex(rules_key, self.cache_ttl, json.dumps(rules_data))
            await pipe.execute()

        except Exception as e:
            logger.warning(f'Cache set failed for channel {channel_id}: {e}')

    async def invalidate_connection(self, channel_id: str):
        """Invalidate cache for a specific connection."""
        cache_key = f'conn_hub:{channel_id}'
        rules_pattern = f'hub_rules:{channel_id}:*'

        try:
            # Delete connection cache
            await self.redis.delete(cache_key)

            # Delete all rules for this channel
            async for key in self.redis.scan_iter(match=rules_pattern):
                await self.redis.delete(key)

        except Exception as e:
            logger.warning(f'Cache invalidation failed for channel {channel_id}: {e}')

    async def invalidate_hub(self, hub_id: str):
        """Invalidate all connections for a hub."""
        try:
            # This is expensive, so queue it for batch processing
            await self.batch_invalidation_queue.put(('hub', hub_id))
        except Exception as e:
            logger.warning(f'Hub invalidation queueing failed for hub {hub_id}: {e}')


# Global cache instance
_connection_cache: Optional[ConnectionHubCache] = None


def init_connection_cache(redis_client: redis.Redis) -> ConnectionHubCache:
    """Initialize the global connection cache."""
    global _connection_cache
    _connection_cache = ConnectionHubCache(redis_client)
    return _connection_cache


def get_connection_cache() -> ConnectionHubCache:
    """Get the global connection cache instance."""
    if _connection_cache is None:
        raise RuntimeError('Connection cache not initialized. Call init_connection_cache() first.')
    return _connection_cache
