import discord
from utils.modules.core.moderation import fetch_original_message
from utils.modules.services.BaseService import BaseService
from utils.modules.ui.views.reactionView import ReactionView

from sqlmodel import col, select
from sqlalchemy.orm import selectinload

from db.models import HubMessageReaction, Message, Connection, Broadcast
from utils.constants import logger

from typing import TYPE_CHECKING, Union, Optional, List, Tuple

if TYPE_CHECKING:
    from main import Bot
    from sqlmodel.ext.asyncio.session import AsyncSession


class ReactionService(BaseService):
    async def fetch_reaction_for_message(
        self, session: 'AsyncSession', message_id: str, emoji_id: str
    ) -> Optional[Tuple[Message, Optional[HubMessageReaction]]]:
        """Fetch original message and specific reaction in a single query."""
        # Try to fetch message with reaction directly
        stmt = (
            select(Message, HubMessageReaction)
            .outerjoin(
                HubMessageReaction,
                (col(Message.id) == HubMessageReaction.messageId)
                & (HubMessageReaction.emoji == emoji_id),
            )
            .where(Message.id == message_id)
        )
        result = await session.exec(stmt)
        row = result.first()

        if row:
            return (row[0], row[1])

        # If not found, try through broadcast relationship
        stmt = (
            select(Message, HubMessageReaction)
            .join(Broadcast, col(Message.id) == Broadcast.messageId)
            .outerjoin(
                HubMessageReaction,
                (col(Message.id) == HubMessageReaction.messageId)
                & (HubMessageReaction.emoji == emoji_id),
            )
            .where(Broadcast.id == message_id)
        )
        result = await session.exec(stmt)
        row = result.first()
        return (row[0], row[1]) if row else None

    async def fetch_all_reactions_for_message(
        self, session: 'AsyncSession', message_id: str
    ) -> Optional[Tuple[Message, List[HubMessageReaction]]]:
        """Fetch original message and all its reactions in a single optimized query."""
        # Try to fetch message with all reactions directly
        stmt = (
            select(Message)
            .options(selectinload(Message.reactions))  # pyright: ignore[reportArgumentType]
            .where(col(Message.id) == message_id)
        )
        message = await session.scalar(stmt)

        if message:
            return (message, message.reactions)

        # If not found, try through broadcast relationship
        stmt = (
            select(Message)
            .join(Broadcast, col(Message.id) == Broadcast.messageId)
            .options(selectinload(Message.reactions))  # pyright: ignore[reportArgumentType]
            .where(Broadcast.id == message_id)
        )
        message = await session.scalar(stmt)

        if message:
            return (message, message.reactions)

        return None

    async def create_view(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        message: discord.Message,
        emoji: Union[str, discord.Emoji],
    ) -> ReactionView:
        """Create a reaction view and ensure the reaction entry exists in the database."""
        emoji_id = str(emoji) if isinstance(emoji, str) else str(emoji.id)
        await self.create_reaction_entry(bot, message, user, emoji_id)
        return ReactionView(bot, message, emoji)

    async def create_reaction_entry(
        self,
        bot: 'Bot',
        message: discord.Message,
        user: discord.User | discord.Member,
        emoji_id: str,
    ) -> bool:
        """Create a reaction entry in the database if it doesn't exist."""
        async with bot.db.get_session() as session:
            result = await self.fetch_reaction_for_message(session, str(message.id), emoji_id)
            if not result:
                # NOTE: This check is needed as we use this method directly in onReaction
                # event. If you change it, make sure to update the event handling logic accordingly.
                return False

            original_msg, existing_reaction = result

            if existing_reaction:
                return False

            new_reaction = HubMessageReaction(
                messageId=original_msg.id,
                emoji=emoji_id,
                users=[str(user.id)],
            )
            session.add(new_reaction)
            await session.commit()
            return True

    async def broadcast_reaction(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        message: discord.Message,
        emoji: Union[str, discord.Emoji],
    ) -> bool:
        """Broadcast a reaction update across all connected channels in the hub."""
        async with bot.db.get_session() as session:
            # First verify the message exists and get the hub info
            original_msg = await fetch_original_message(session, str(message.id))
            if not original_msg:
                logger.warning(f'Could not find original message for broadcast: {message.id}')
                return False

            # Get all webhook URLs for broadcasts of this message
            stmt = (
                select(Connection.webhookURL, Broadcast.id)
                .join(Broadcast, col(Broadcast.channelId) == Connection.channelId)
                .where(
                    Connection.hubId == original_msg.hubId,
                    Broadcast.messageId == original_msg.id,
                )
            )
            result = (await session.exec(stmt)).all()

        if not result:
            logger.warning(f'No broadcast connections found for message {message.id}')
            return False

        view: ReactionView = await self.create_view(bot, user, message, emoji)
        success_count = 0

        for row in result:
            webhook_url, broadcast_msg_id = row
            if not webhook_url:
                logger.warning(f'Missing webhook URL for broadcast {broadcast_msg_id}')
                continue

            try:
                if not bot.http_session:
                    logger.error('Bot HTTP session is not available')
                    continue

                webhook = discord.Webhook.from_url(webhook_url, session=bot.http_session)
                await webhook.edit_message(int(broadcast_msg_id), view=view)
                success_count += 1
            except discord.NotFound:
                logger.warning(f'Webhook message {broadcast_msg_id} not found')

        return success_count > 0

    async def update_reacted(
        self,
        bot: 'Bot',
        message: discord.Message,
        emoji: Union[str, discord.Emoji],
        user: discord.User | discord.Member,
    ) -> bool:
        """Update the reaction user list by adding or removing a user."""
        message_id = str(message.id)
        emoji_id = emoji if isinstance(emoji, str) else str(emoji.id)
        user_id = str(user.id)

        async with bot.db.get_session() as session:
            result = await self.fetch_reaction_for_message(session, message_id, emoji_id)
            if not result:
                logger.warning(f'Could not find original message for {message_id}')
                return False

            original_msg, reaction = result

            if not reaction:
                # Create a new reaction entry
                reaction = HubMessageReaction(
                    messageId=original_msg.id,
                    emoji=emoji_id,
                    users=[user_id],
                )
                session.add(reaction)
                await session.commit()
                return True

            # Toggle user in the reaction
            if user_id in reaction.users:
                reaction.users.remove(user_id)
                await session.commit()
                return False
            else:
                reaction.users.append(user_id)
                await session.commit()
                return True

    async def update_button_content(
        self, bot: 'Bot', message: discord.Message, emoji: Union[str, discord.Emoji]
    ) -> bool:
        """Update the reaction button content with the current count."""
        if not message or not message.components:
            return False

        message_id = str(message.id)
        emoji_id = emoji if isinstance(emoji, str) else str(emoji.id)
        custom_id = f'reaction_{message_id}_{emoji_id}'

        async with bot.db.get_session() as session:
            result = await self.fetch_reaction_for_message(session, message_id, emoji_id)
            if not result:
                logger.warning(f'Could not find original message for {message_id}')
                return False

            _, reaction_entry = result
            count = len(reaction_entry.users) if reaction_entry else 0

        try:
            view = discord.ui.View.from_message(message)

            for item in view.children:
                if isinstance(item, discord.ui.Button) and item.custom_id == custom_id:
                    item.label = str(count)
                    item.emoji = emoji
                    break

            await message.edit(view=view)
            return True
        except discord.HTTPException as e:
            logger.error(f'Failed to update button content for message {message_id}: {e}')
            return False

    async def get_reaction_count(self, bot: 'Bot', message_id: str, emoji_id: str) -> int:
        """Get the number of users who reacted with a specific emoji."""
        async with bot.db.get_session() as session:
            result = await self.fetch_reaction_for_message(session, message_id, emoji_id)
            if not result:
                return 0

            _, reaction = result
            return len(reaction.users) if reaction else 0

    async def has_user_reacted(
        self, bot: 'Bot', message_id: str, emoji_id: str, user_id: str
    ) -> bool:
        """Check if a specific user has reacted with a specific emoji."""
        async with bot.db.get_session() as session:
            result = await self.fetch_reaction_for_message(session, message_id, emoji_id)
            if not result:
                return False

            _, reaction = result
            return reaction is not None and user_id in reaction.users
