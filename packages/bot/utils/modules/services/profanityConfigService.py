from typing import Awaitable, Callable, List, Optional, Tuple

from sqlmodel import and_, col, select

from utils.constants import constants
from utils.constants import logger
from db.models import (
    AntiSwearPattern,
    AntiSwearRule,
    AntiSwearWhitelist,
    BlockWordAction,
    PatternMatchType,
)
from utils.modules.core.profanity.filter_service import ProfanityFilterService
from utils.modules.services.BaseService import BaseService
from utils.modules.common.service_utils import CacheOperationHelper


class ProfanityConfigService(BaseService):
    async def _reload_cache_for_rule(self, rule_id: str) -> None:
        """Helper method to reload cache for a rule by finding its hub."""
        rule = await self.get_rule(rule_id)
        if rule:
            await self.reload_rules_cache(rule.hubId)

    async def _execute_with_cache_reload[T](
        self,
        operation_func: Callable[[], Awaitable[T]],
        hub_id: Optional[str] = None,
        rule_id: Optional[str] = None,
    ):
        async def cache_reload():
            if hub_id:
                await self.reload_rules_cache(hub_id)
            elif rule_id:
                await self._reload_cache_for_rule(rule_id)

        result = await CacheOperationHelper.execute_with_cache_reload(
            operation_func,
            cache_reload,
            f'Profanity config operation failed (hub_id={hub_id}, rule_id={rule_id})',
        )
        return result if result is not None else False

    async def list_rules(self, hub_id: str) -> List[AntiSwearRule]:
        result = await self.session.exec(
            select(AntiSwearRule)
            .where(AntiSwearRule.hubId == hub_id)
            .order_by(col(AntiSwearRule.createdAt))
        )
        return list(result.all())

    async def get_rule(self, rule_id: str) -> Optional[AntiSwearRule]:
        return (
            await self.session.exec(select(AntiSwearRule).where(AntiSwearRule.id == rule_id))
        ).first()

    async def create_rule(
        self,
        *,
        hub_id: str,
        name: str,
        created_by: str,
        actions: Optional[List[BlockWordAction]] = None,
        enabled: bool = True,
        mute_duration_minutes: Optional[int] = None,
    ) -> Tuple[bool, Optional[AntiSwearRule]]:
        rule = AntiSwearRule(
            hubId=hub_id,
            name=name.strip()[:100],
            createdBy=created_by,
            enabled=enabled,
            muteDurationMinutes=mute_duration_minutes,
            actions=(actions or [BlockWordAction.BLOCK_MESSAGE, BlockWordAction.SEND_ALERT]),
        )

        async def _create_operation():
            self.session.add(rule)
            return True, rule

        result = await self._execute_with_cache_reload(_create_operation, hub_id=hub_id)
        return result if isinstance(result, tuple) else (False, None)

    async def update_rule(
        self,
        rule_id: str,
        **updates,
    ) -> bool:
        rule = await self.get_rule(rule_id)
        if not rule:
            return False

        # Only allow specific fields to be updated
        allowed = {
            'name',
            'enabled',
            'muteDurationMinutes',
            'actions',
        }

        async def _update_operation():
            for k, v in updates.items():
                if k in allowed:
                    setattr(rule, k, v)
            return True

        return await self._execute_with_cache_reload(_update_operation, rule_id=rule_id)

    async def delete_rule(self, rule_id: str) -> bool:
        rule = await self.get_rule(rule_id)
        if not rule:
            return False

        hub_id = rule.hubId  # Store hub_id before deletion

        async def _delete_operation():
            await self.session.delete(rule)
            return True

        return await self._execute_with_cache_reload(_delete_operation, hub_id=hub_id)

    async def list_patterns(self, rule_id: str) -> List[AntiSwearPattern]:
        result = await self.session.exec(
            select(AntiSwearPattern)
            .where(AntiSwearPattern.ruleId == rule_id)
            .order_by(AntiSwearPattern.pattern)
        )
        return list(result.all())

    async def add_pattern(
        self, *, hub_id: str, rule_id: str, pattern: str, match_type: PatternMatchType
    ) -> bool:
        pattern = pattern.strip()
        if not pattern:
            return False

        async def _add_operation() -> bool:
            db_pattern = AntiSwearPattern(ruleId=rule_id, pattern=pattern, matchType=match_type)
            self.session.add(db_pattern)
            return True

        return await self._execute_with_cache_reload(_add_operation, hub_id=hub_id)

    async def delete_pattern(self, *, hub_id: str, pattern_id: str) -> bool:
        pat = (
            await self.session.exec(
                select(AntiSwearPattern).where(AntiSwearPattern.id == pattern_id)
            )
        ).first()
        if not pat:
            return False

        async def _delete_operation():
            await self.session.delete(pat)
            return True

        return await self._execute_with_cache_reload(_delete_operation, hub_id=hub_id)

    async def list_whitelist(self, rule_id: str) -> List[AntiSwearWhitelist]:
        result = await self.session.exec(
            select(AntiSwearWhitelist)
            .where(AntiSwearWhitelist.ruleId == rule_id)
            .order_by(AntiSwearWhitelist.word)
        )
        return list(result.all())

    async def add_whitelist(
        self, *, rule_id: str, word: str, created_by: str, reason: Optional[str] = None
    ) -> bool:
        word = word.strip()
        if not word:
            return False

        # Check if word already exists
        exists = (
            await self.session.exec(
                select(AntiSwearWhitelist).where(
                    and_(AntiSwearWhitelist.ruleId == rule_id, AntiSwearWhitelist.word == word)
                )
            )
        ).first()
        if exists:
            return True

        # Get rule to validate it exists
        rule = await self.get_rule(rule_id)
        if not rule:
            return False

        async def _add_operation():
            wl = AntiSwearWhitelist(ruleId=rule_id, word=word, createdBy=created_by, reason=reason)
            self.session.add(wl)
            return True

        return await self._execute_with_cache_reload(_add_operation, rule_id=rule_id)

    async def delete_whitelist(self, *, rule_id: str, whitelist_id: str) -> bool:
        wl = (
            await self.session.exec(
                select(AntiSwearWhitelist).where(AntiSwearWhitelist.id == whitelist_id)
            )
        ).first()
        if not wl:
            return False

        # Get rule to validate it exists
        rule = await self.get_rule(rule_id)
        if not rule:
            return False

        async def _delete_operation():
            await self.session.delete(wl)
            return True

        return await self._execute_with_cache_reload(_delete_operation, rule_id=rule_id)

    async def reload_rules_cache(self, hub_id: str) -> None:
        """Reload the in-memory matcher for a hub."""
        try:
            pfs = ProfanityFilterService(self.session, str(constants.client_id))
            await pfs.reload_rules(hub_id)
        except Exception as e:
            logger.debug(f'reload_rules_cache failed (non-fatal): {e}')
