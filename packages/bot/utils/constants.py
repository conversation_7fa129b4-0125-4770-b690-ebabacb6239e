import ast
import logging
import os
import sys

import redis.asyncio as redis_async
from dotenv import load_dotenv

load_dotenv()


def _to_bool(value: str | None, default: bool = False) -> bool:
    if value is None:
        return default
    return str(value).strip().lower() in {'1', 'true', 'yes', 'on'}


class InterchatConstants:
    client_id = int(os.getenv('CLIENT_ID') or 0)  # will be set on bot startup
    production = str(os.getenv('ENVIRONMENT') or 'development').lower() == 'production'
    debug = _to_bool(os.getenv('DEBUG'), default=False)
    token = str(os.getenv('DISCORD_TOKEN') or os.getenv('TOKEN') or '')
    database_url = str(os.getenv('DATABASE_URL'))
    redis_uri = str(os.getenv('REDIS_URI'))
    prefix = str(os.getenv('PREFIX'))
    color = 0x9172D8
    support_invite = str(os.getenv('SUPPORT_INVITE') or 'https://interchat.tech/support')
    donate_link = str(os.getenv('DONATE_LINK'))
    sentry_dsn = str(os.getenv('SENTRY_DSN'))
    dev_guild_id = int(os.getenv('DEV_GUILD_ID') or 0)
    staff_role_id = int(os.getenv('STAFF_ROLE_ID') or 0)
    sentry_send_default_pii = _to_bool(os.getenv('SENTRY_PII'), default=False)
    nsfw_detector_url = str(
        os.getenv('NSFW_DETECTOR_URL') or 'http://localhost:8000/v1/detect/urls'
    )
    enable_nsfw_detection = _to_bool(os.getenv('ENABLE_NSFW'), default=True)
    staff_report_channel_id = int(os.getenv('STAFF_REPORT_CHANNEL_ID') or 0)
    rate_limits = {'commands': {'limit': 5, 'period': 5}, 'webhook': {'limit': 3, 'period': 300}}
    heartbeat_url: str = str(os.getenv('HEARTBEAT_URL'))

    def __init__(self):
        # frozen set of user IDs
        self.auth_users = self._get_auth_users()

    @property  # changes on runtime... probably
    def version(self):
        version = str(os.getenv('INTERCHAT_VERSION'))
        if str(os.getenv('ENVIRONMENT')).lower() == 'development':
            return f'DEV {version}'

        return version

    def _get_auth_users(self) -> list[int]:
        raw = os.getenv('AUTH')
        if not raw:
            return []
        try:
            parsed = ast.literal_eval(raw)
            return [int(x) for x in parsed] if isinstance(parsed, (list, tuple)) else []
        except Exception:
            logger.warning('AUTH is not a valid list; using empty list')
            return []


constants = InterchatConstants()

redis_client = redis_async.from_url(
    constants.redis_uri,
    max_connections=100,
    socket_connect_timeout=10,
    socket_timeout=10,
    retry_on_timeout=True,
    decode_responses=True,
)

if not constants.production:
    from rich.logging import RichHandler
    from rich.theme import Theme
    from rich.console import Console

    console = Console(
        theme=Theme(
            {
                'logging.level.info': '#a6e3a1',
                'logging.level.debug': '#8aadf4',
                'logging.level.warning': '#f9e2af',
                'logging.level.error': '#f38ba8',
            }
        )
    )
    handler = RichHandler(tracebacks_width=200, console=console)
else:
    handler = logging.StreamHandler()  # plain logs for prod


handler.setFormatter(logging.Formatter('%(name)s: %(message)s'))
level = logging.DEBUG if constants.debug else logging.INFO

logger = logging.getLogger('InterChat')
logger.setLevel(level)
logger.addHandler(handler)
logger.propagate = False

watch_log = logging.getLogger('cogwatch')
watch_log.setLevel(level)
watch_log.addHandler(handler)
watch_log.propagate = False

discord_logger = logging.getLogger('discord')
discord_logger.setLevel(level)
discord_logger.addHandler(handler)
discord_logger.propagate = False

discord_http_logger = logging.getLogger('discord.http')
discord_http_logger.setLevel(logging.INFO)
discord_http_logger.addHandler(handler)
discord_http_logger.propagate = False


def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # Log the exception with full traceback
    logger.critical('Uncaught exception', exc_info=(exc_type, exc_value, exc_traceback))
