import json
import os
import re
from datetime import datetime, timedelta
from typing import TYPE_CHECKING, Optional, Union

import discord
from discord.ext import commands
from sqlmodel import desc, select
from sqlalchemy.orm import selectinload

from utils.modules.common.database import DatabaseQueries
from utils.modules.common.user_utils import (
    check_user,
    load_profile_data,
    load_user_locale,
    upsert_user,
)
from utils.modules.common.validation_utils import DurationValidators, TextValidators
from db.models import Hub, UserAchievement

if TYPE_CHECKING:
    from sqlmodel.ext.asyncio.session import AsyncSession

    from main import Bot

discordInteraction = Union[commands.Context, discord.Interaction]


# backward compatibility (aka im too lazy)
__all__ = [
    'check_user',
    'load_profile_data',
    'load_user_locale',
    'upsert_user',
]


async def chunk_guilds(guilds):
    for guild in sorted(guilds, key=lambda g: g.member_count, reverse=True):
        if guild.chunked is False:
            await guild.chunk(cache=True)


def parse_discord_emoji(emoji_input: discord.Emoji | str | None) -> str:
    if not emoji_input:
        return '❓'

    def as_img(eid, name, animated):
        return f'<img src="https://cdn.discordapp.com/emojis/{eid}.{"gif" if animated else "png"}" alt="{name}" class="custom-emoji">'

    if isinstance(emoji_input, discord.Emoji):
        return as_img(emoji_input.id, emoji_input.name, getattr(emoji_input, 'animated', False))

    match = re.match(r'<(a?):([^:]+):(\d+)>', str(emoji_input))
    if match:
        animated, name, emoji_id = match.groups()
        return as_img(emoji_id, name, bool(animated))
    return '❓'


async def fetch_achievements(
    bot: 'Bot',
    user: discord.User | discord.Member,
    limit: int = 6,
) -> list[dict[str, str]]:
    achievement_list: list[dict[str, str]] = []

    async with bot.db.get_session() as session:
        stmt = (
            select(UserAchievement)
            .where(UserAchievement.userId == str(user.id))
            .options(selectinload(UserAchievement.achievement))  # pyright: ignore[reportArgumentType]
            .order_by(desc(UserAchievement.unlockedAt))
            .limit(limit)
        )
        result = (await session.exec(stmt)).all()

        for ua in result:
            achievement = ua.achievement
            if not achievement:
                continue

            data = {
                'icon': parse_discord_emoji(achievement.badgeEmoji),
                'title': achievement.name,
                'description': achievement.description,
            }
            achievement_list.append(data)

    return achievement_list


def abbreviate_number(n):
    if n >= 1_000_000_000:
        return f'{n / 1_000_000_000:.1f}B'
    elif n >= 1_000_000:
        return f'{n / 1_000_000:.1f}M'
    elif n >= 1_000:
        return f'{n / 1_000:.1f}k'
    else:
        return str(n)


DURATION_UNITS = {
    's': 1000,
    'm': 60 * 1000,
    'h': 60 * 60 * 1000,
    'd': 24 * 60 * 60 * 1000,
    'w': 7 * 24 * 60 * 60 * 1000,
    'mo': 30 * 24 * 60 * 60 * 1000,
    'y': 12 * 30 * 24 * 60 * 60 * 1000,
}


def parse_duration(s: str):
    success, result = DurationValidators.parse_duration(s)
    if success and isinstance(result, int):
        return result
    else:
        raise ValueError(result)


def ms_to_datetime(duration_ms: int) -> datetime:
    return datetime.now() + timedelta(milliseconds=duration_ms)


async def load_locales():
    with open('./data/localeMap.json', 'r', encoding='utf-8') as f:
        locale_map = json.load(f)

    abbrev_to_name = {v['code']: k for k, v in locale_map.items()}

    found_codes: set[str] = set()

    # Detect top-level locale files like locales/en.yaml, locales/fr.yaml
    for root, _, files in os.walk('./locales'):
        # Only consider the top-level for legacy files
        if os.path.abspath(root) == os.path.abspath('./locales'):
            for file in files:
                if file.endswith(('.yaml', '.yml')):
                    found_codes.add(file.split('.')[0])
        # Also consider subdirectories like locales/en/*.yml
        else:
            # The immediate directory name under locales is the language code
            lang_code = os.path.basename(root)
            for file in files:
                if file.endswith(('.yaml', '.yml')):
                    found_codes.add(lang_code)

    available_locales = []
    for lang_code in sorted(found_codes):
        if lang_code in abbrev_to_name:
            full_name = abbrev_to_name[lang_code]
            flag = locale_map[full_name].get('flag', '🌐')
            available_locales.append({'short': lang_code, 'long': full_name, 'flag': flag})
        else:
            # Fallback entry if not present in localeMap.json
            available_locales.append({'short': lang_code, 'long': lang_code, 'flag': '🌐'})

    available_locales.sort(key=lambda x: x['long'])
    return available_locales


async def get_hub(hub_id: str, session: 'AsyncSession') -> Optional[Hub]:
    """Get hub data from database"""
    return await DatabaseQueries.fetch_hub_by_id(session, hub_id)


def sanitize_webhook_username(name: str) -> str:
    """Sanitize a username for use with Discord webhooks."""
    return TextValidators.sanitize_webhook_username(name)


def ms_to_human(ms: int, max_parts=3) -> str:
    """Convert milliseconds to human-readable duration format."""
    if ms < 60_000:
        return '< 1 minute'

    UNITS = [
        ('y', 365 * 24 * 60 * 60),
        ('mo', 30 * 24 * 60 * 60),
        ('w', 7 * 24 * 60 * 60),
        ('d', 24 * 60 * 60),
        ('h', 60 * 60),
        ('m', 60),
        ('s', 1),
    ]

    total_seconds = ms // 1000
    parts = []

    for unit_name, unit_seconds in UNITS:
        if total_seconds >= unit_seconds:
            unit_count = total_seconds // unit_seconds
            parts.append(f'{int(unit_count)}{unit_name}')
            total_seconds %= unit_seconds

            if len(parts) >= max_parts:
                break

    return ' '.join(parts) if parts else '< 1 minute'
