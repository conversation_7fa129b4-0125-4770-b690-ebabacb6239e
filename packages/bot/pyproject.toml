[project]
name = "bot"
version = "5.0.4"
description = "InterChat is a discord bot that lets you talk across servers"
readme = "README.md"
license = { file = "LICENCE" }
requires-python = ">=3.12"
dependencies = [
  "aiofiles",
  "aiohttp",
  "asyncpg",
  "cogwatch",
  "discord",
  "dotenv",
  "jinja2",
  "jishaku",
  "playwright",
  "cuid2",
  "psutil",
  "pyyaml",
  "redis",
  "sentry-sdk",
  "rich",
  "db",
]

[tool.uv.sources]
db = { workspace = true }

[dependency-groups]
dev = ["alembic", "psycopg2-binary", "pyright", "ruff>=0.12.11", "watchdog"]

[tool.ruff]
line-length = 100

[tool.ruff.format]
quote-style = "single"

[tool.uv]
package = false
