from db.models import Badges


badges = {
    Badges.VOTER: {
        'name': 'Voter',
        'description': 'Voted for InterChat in the last 12 hours',
        'icon': 'voter_badge',
    },
    Badges.SUPPORTER: {
        'name': 'Supporter',
        'description': 'Donates to InterChat',
        'icon': 'donator_badge',
    },
    Badges.TRANSLATOR: {
        'name': 'Translator',
        'description': 'Submit translations to InterChat',
        'icon': 'translator_badge',
    },
    Badges.STAFF: {
        'name': 'InterChat Staff',
        'description': 'InterChat staff member',
        'icon': 'staff_badge',
    },
    Badges.BETA_TESTER: {
        'name': 'Beta Tester',
        'description': 'Tested a pre-release version of InterChat',
        'icon': 'beta_tester_badge',
    },
}
