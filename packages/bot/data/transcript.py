transcript_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Channel Transcript</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #36393f;
            color: #dcddde;
            line-height: 1.375;
        }
        
        .transcript-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .channel-header {
            background-color: #2f3136;
            padding: 16px 20px;
            border-radius: 8px 8px 0 0;
            border-bottom: 1px solid #40444b;
            margin-bottom: 0;
        }
        
        .channel-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .channel-icon {
            font-size: 20px;
            color: #8e9297;
        }
        
        .channel-name {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
        }
        
        .channel-topic {
            color: #b9bbbe;
            font-size: 14px;
            margin-top: 4px;
        }
        
        .transcript-info {
            background-color: #2f3136;
            padding: 12px 20px;
            border-bottom: 1px solid #40444b;
            font-size: 14px;
            color: #b9bbbe;
        }
        
        .messages-container {
            background-color: #2f3136;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
        }
        
        .message {
            padding: 8px 20px;
            border-left: 4px solid transparent;
            position: relative;
            display: flex;
            gap: 16px;
        }
        
        .message:hover {
            background-color: rgba(79, 84, 92, 0.16);
        }
        
        .message.reply {
            border-left-color: #4f545c;
            background-color: rgba(79, 84, 92, 0.06);
        }
        
        .message.system {
            background-color: rgba(114, 137, 218, 0.05);
            border-left-color: #7289da;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .avatar-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .message-content {
            flex: 1;
            min-width: 0;
        }
        
        .message-header {
            display: flex;
            align-items: baseline;
            gap: 8px;
            margin-bottom: 4px;
        }
        
        .author-name {
            font-weight: 500;
            color: #ffffff;
            font-size: 16px;
        }
        
        .author-name.bot {
            color: #5865f2;
        }
        
        .bot-tag {
            background-color: #5865f2;
            color: white;
            font-size: 10px;
            padding: 1px 4px;
            border-radius: 3px;
            font-weight: 500;
            text-transform: uppercase;
            vertical-align: middle;
        }
        
        .timestamp {
            font-size: 12px;
            color: #a3a6aa;
            font-weight: 400;
        }
        
        .message-text {
            font-size: 16px;
            line-height: 1.375;
            word-wrap: break-word;
            color: #dcddde;
        }
        
        .mention {
            background-color: rgba(88, 101, 242, 0.3);
            color: #dee0fc;
            padding: 0 2px;
            border-radius: 3px;
        }
        
        .channel-mention {
            background-color: rgba(88, 101, 242, 0.15);
            color: #00aff4;
            padding: 0 2px;
            border-radius: 3px;
        }
        
        .role-mention {
            background-color: rgba(88, 101, 242, 0.3);
            color: #dee0fc;
            padding: 0 2px;
            border-radius: 3px;
        }
        
        .embed {
            max-width: 520px;
            border-left: 4px solid #202225;
            background-color: #2f3136;
            border-radius: 4px;
            padding: 8px 16px 16px 12px;
            margin-top: 4px;
        }
        
        .embed.colored {
            border-left-color: #00d166;
        }
        
        .embed-author {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .embed-title {
            font-size: 16px;
            font-weight: 600;
            color: #00aff4;
            margin-bottom: 4px;
        }
        
        .embed-description {
            font-size: 14px;
            color: #dcddde;
            line-height: 1.375;
        }
        
        .attachments {
            margin-top: 4px;
        }
        
        .attachment {
            background-color: #2f3136;
            border: 1px solid #40444b;
            border-radius: 8px;
            padding: 12px;
            margin-top: 4px;
            display: inline-block;
            max-width: 400px;
        }
        
        .attachment-name {
            color: #00aff4;
            font-size: 14px;
            text-decoration: none;
        }
        
        .attachment-size {
            color: #a3a6aa;
            font-size: 12px;
        }
        
        .reactions {
            display: flex;
            gap: 4px;
            margin-top: 8px;
            flex-wrap: wrap;
        }
        
        .reaction {
            background-color: #2f3136;
            border: 1px solid #40444b;
            border-radius: 8px;
            padding: 3px 6px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .reaction-count {
            color: #b9bbbe;
            font-size: 13px;
        }
        
        .message-edited {
            color: #a3a6aa;
            font-size: 10px;
            margin-left: 4px;
        }
        
        code {
            background-color: #2f3136;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 85%;
            font-family: 'Courier New', monospace;
        }
        
        pre {
            background-color: #2f3136;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
            overflow-x: auto;
        }
        
        pre code {
            background-color: transparent;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="transcript-container">
        <div class="channel-header">
            <div class="channel-info">
                <span class="channel-icon">#</span>
                <span class="channel-name">{{ channel_name }}</span>
            </div>
            {% if channel_topic %}
            <div class="channel-topic">{{ channel_topic }}</div>
            {% endif %}
        </div>
        
        <div class="transcript-info">
            Generated on {{ generation_date }} | {{ message_count }} messages
            {% if date_range %}| {{ date_range }}{% endif %}
        </div>
        
        <div class="messages-container">
            {% for message in messages %}
            <div class="message{% if message.is_system %} system{% endif %}{% if message.is_reply %} reply{% endif %}">
                {% if message.avatar_url %}
                <img src="{{ message.avatar_url }}" alt="Avatar" class="avatar">
                {% else %}
                <div class="avatar-placeholder">{{ message.avatar_initials }}</div>
                {% endif %}
                
                <div class="message-content">
                    <div class="message-header">
                        <span class="author-name{% if message.is_bot %} bot{% endif %}">{{ message.author_name }}</span>
                        {% if message.is_bot %}
                        <span class="bot-tag">BOT</span>
                        {% endif %}
                        <span class="timestamp">{{ message.timestamp }}</span>
                        {% if message.is_edited %}
                        <span class="message-edited">(edited)</span>
                        {% endif %}
                    </div>
                    
                    {% if message.content %}
                    <div class="message-text">{{ message.content|safe }}</div>
                    {% endif %}
                    
                    {% if message.embeds %}
                    {% for embed in message.embeds %}
                    <div class="embed{% if embed.color %} colored{% endif %}">
                        {% if embed.author %}
                        <div class="embed-author">{{ embed.author }}</div>
                        {% endif %}
                        {% if embed.title %}
                        <div class="embed-title">{{ embed.title }}</div>
                        {% endif %}
                        {% if embed.description %}
                        <div class="embed-description">{{ embed.description|safe }}</div>
                        {% endif %}
                    </div>
                    {% endfor %}
                    {% endif %}
                    
                    {% if message.attachments %}
                    <div class="attachments">
                        {% for attachment in message.attachments %}
                        <div class="attachment">
                            <a href="{{ attachment.url }}" class="attachment-name">{{ attachment.filename }}</a>
                            {% if attachment.size %}
                            <div class="attachment-size">{{ attachment.size }}</div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    {% if message.reactions %}
                    <div class="reactions">
                        {% for reaction in message.reactions %}
                        <div class="reaction">
                            <span>{{ reaction.emoji }}</span>
                            <span class="reaction-count">{{ reaction.count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
"""
