profile_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Profile Card</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #1a1f2e 0%, #2d3548 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .profile-card {
            width: 800px;
            background: linear-gradient(145deg, #242b3d 0%, #1e2332 100%);
            border-radius: 20px;
            padding: 32px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
        }

        .header {
            display: flex;
            align-items: center;
            gap: 24px;
            margin-bottom: 32px;
        }

        .avatar {
            width: 96px;
            height: 96px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: 700;
            color: white;
            position: relative;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
            background-image: url('{{ profile.avatar_url }}');
            background-size: cover;
            background-position: center;
        }

        .avatar::after {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
            z-index: -1;
            opacity: 0.5;
        }

        .avatar-fallback {
            background-image: none !important;
        }

        .user-info {
            flex: 1;
        }

        .username {
            font-size: 28px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .guild-tag {
            font-size: 16px;
            color: #8b93a6;
            font-weight: 500;
        }

        .stats-box {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 32px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 24px;
            align-items: center;
        }

        .message-stat {
            text-align: center;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #8b93a6;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .ranks-section {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .rank-item {
            text-align: center;
        }

        .rank-position {
            font-size: 24px;
            font-weight: 700;
            color: #6366f1;
            margin-bottom: 4px;
        }

        .rank-label {
            font-size: 12px;
            color: #8b93a6;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .achievements-section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 18px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 2px;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .achievement {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .achievement-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
            /* Removed the green background and border-radius */
        }

        /* Only add background for text/emoji that isn't a custom Discord emoji */
        .achievement-icon:not(:has(.custom-emoji)) {
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            color: white;
        }

        .custom-emoji {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }

        .achievement-info {
            flex: 1;
            min-width: 0;
        }

        .achievement-info h4 {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 2px;
        }

        .achievement-info p {
            font-size: 12px;
            color: #8b93a6;
        }
    </style>
</head>
<body>
    <div class="profile-card">
        <div class="header">
            <div class="avatar {{ 'avatar-fallback' if not profile.avatar_url else '' }}">
                {{ profile.avatar_initials if not profile.avatar_url else '' }}
            </div>
            <div class="user-info">
                <div class="username">{{ profile.username }}</div>
                <div class="guild-tag">{{ profile.guild_tag }}</div>
            </div>
        </div>

        <div class="stats-box">
            <div class="stats-grid">
                <div class="message-stat">
                    <div class="stat-value">{{ profile.message_count }}</div>
                    <div class="stat-label">Messages</div>
                </div>
                <div class="ranks-section">
                    <div class="rank-item">
                        <div class="rank-position">{{ profile.global_rank }}</div>
                        <div class="rank-label">Global</div>
                    </div>
                    <div class="rank-item">
                        <div class="rank-position">{{ profile.reputation }}</div>
                        <div class="rank-label">Reputation</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="achievements-section">
            <h2 class="section-title">Achievements</h2>
            <div class="achievements-grid">
                {% for achievement in achievements %}
                <div class="achievement">
                    <div class="achievement-icon">{{ achievement.icon|safe }}</div>
                    <div class="achievement-info">
                        <h4>{{ achievement.title }}</h4>
                        <p>{{ achievement.description }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="achievements-section">
            <h2 class="section-title">Badges</h2>
            <div class="achievements-grid">
                {% for badge in badges %}
                <div class="achievement">
                    <div class="achievement-icon">{{ badge.icon|safe }}</div>
                    <div class="achievement-info">
                        <h4>{{ badge.title }}</h4>
                        <p>{{ badge.description }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</body>
</html>
"""
