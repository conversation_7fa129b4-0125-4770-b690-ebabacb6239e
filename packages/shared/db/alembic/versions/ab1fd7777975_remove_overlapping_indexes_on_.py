"""Remove overlapping indexes on Connection table

Revision ID: ab1fd7777975
Revises: b853447707a8
Create Date: 2025-09-09 10:44:09.162334

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "ab1fd7777975"
down_revision: Union[str, Sequence[str], None] = "b853447707a8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("Connection_channelId_connected_idx"), table_name="Connection")
    op.drop_index(op.f("Connection_hubId_connected_idx"), table_name="Connection")
    op.drop_index(op.f("Connection_hub_lookup_idx"), table_name="Connection")
    op.create_index(
        "Connection_hub_lookup_partial_idx",
        "Connection",
        ["channelId", "hubId", "serverId"],
        unique=False,
        postgresql_where=sa.text("connected = true"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "Connection_hub_lookup_partial_idx",
        table_name="Connection",
        postgresql_where=sa.text("connected = true"),
    )
    op.create_index(
        op.f("Connection_hub_lookup_idx"),
        "Connection",
        ["channelId", "hubId", "connected", "serverId"],
        unique=False,
    )
    op.create_index(
        op.f("Connection_hubId_connected_idx"),
        "Connection",
        ["hubId", "connected"],
        unique=False,
    )
    op.create_index(
        op.f("Connection_channelId_connected_idx"),
        "Connection",
        ["channelId", "connected"],
        unique=False,
    )
    # ### end Alembic commands ###
