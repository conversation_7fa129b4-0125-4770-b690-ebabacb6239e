"""broadcast guildid

Revision ID: b853447707a8
Revises:
Create Date: 2025-09-07 21:21:08.835381

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b853447707a8"
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # 1) Add guildId as nullable
    op.add_column(
        "Broadcast",
        sa.Column("guildId", sa.Text(), nullable=True),
    )
    # 2) Backfill using Message.guildId via messageId
    op.execute(
        'UPDATE "Broadcast" b '
        'SET "guildId" = m."guildId" '
        'FROM "Message" m '
        'WHERE m."id" = b."messageId"'
    )
    # 3) Alter the column to be NOT NULL now that it's populated
    op.alter_column("Broadcast", "guildId", nullable=False)


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("Broadcast", "guildId")
    # ### end Alembic commands ###
