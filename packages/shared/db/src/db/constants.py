import logging
import os
import sys

from dotenv import load_dotenv

load_dotenv()


def _to_bool(value: str | None, default: bool = False) -> bool:
    if value is None:
        return default
    return str(value).strip().lower() in {"1", "true", "yes", "on"}


production = _to_bool(os.getenv("PRODUCTION"), False)

if not production:
    from rich.logging import RichHandler
    from rich.theme import Theme
    from rich.console import Console

    console = Console(
        theme=Theme(
            {
                "logging.level.info": "#a6e3a1",
                "logging.level.debug": "#8aadf4",
                "logging.level.warning": "#f9e2af",
                "logging.level.error": "#f38ba8",
            }
        )
    )
    handler = RichHandler(tracebacks_width=200, console=console)
else:
    handler = logging.StreamHandler()  # plain logs for prod


handler.setFormatter(logging.Formatter("%(name)s: %(message)s"))

logger = logging.getLogger("database")
logger.setLevel(logging.INFO if production else logging.DEBUG)
logger.addHandler(handler)
logger.propagate = False


def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # Log the exception with full traceback
    logger.critical("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))
