import enum
from datetime import datetime
from typing import Any, ClassVar, List, Optional

from cuid2 import Cuid
from sqlalchemy import (
    <PERSON>olean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    Table,
    Text,
    UniqueConstraint,
    func,
    text,
)
from sqlalchemy.dialects.postgresql import ARRAY, ENUM
from sqlmodel import Field, Relationship, SQLModel

cuid = Cuid()


# Enums
class HubModeratorRole(enum.Enum):
    MODERATOR = 'MODERATOR'
    MANAGER = 'MANAGER'


class HubActivityLevel(enum.Enum):
    LOW = 'LOW'
    MEDIUM = 'MEDIUM'
    HIGH = 'HIGH'


class InfractionType(enum.Enum):
    BAN = 'BAN'
    BLACKLIST = 'BLACKLIST'
    MUTE = 'MUTE'
    WARNING = 'WARNING'


class InfractionStatus(enum.Enum):
    ACTIVE = 'ACTIVE'
    REVOKED = 'REVOKED'
    APPEALED = 'APPEALED'


class AppealStatus(enum.Enum):
    PENDING = 'PENDING'
    ACCEPTED = 'ACCEPTED'
    REJECTED = 'REJECTED'


class BlockWordAction(enum.Enum):
    BLOCK_MESSAGE = 'BLOCK_MESSAGE'
    SEND_ALERT = 'SEND_ALERT'
    WARN = 'WARN'
    MUTE = 'MUTE'
    BAN = 'BAN'
    BLACKLIST = 'BLACKLIST'


class PatternMatchType(enum.Enum):
    EXACT = 'EXACT'
    PREFIX = 'PREFIX'
    SUFFIX = 'SUFFIX'
    WILDCARD = 'WILDCARD'


class AlertSeverity(enum.Enum):
    LOW = 'LOW'
    MEDIUM = 'MEDIUM'
    HIGH = 'HIGH'
    CRITICAL = 'CRITICAL'


class Badges(enum.Enum):
    VOTER = 'VOTER'
    SUPPORTER = 'SUPPORTER'
    TRANSLATOR = 'TRANSLATOR'
    DEVELOPER = 'DEVELOPER'
    STAFF = 'STAFF'
    BETA_TESTER = 'BETA_TESTER'


class ReportStatus(enum.Enum):
    PENDING = 'PENDING'
    RESOLVED = 'RESOLVED'
    IGNORED = 'IGNORED'


class BlacklistType(enum.Enum):
    PERMANENT = 'PERMANENT'
    TEMPORARY = 'TEMPORARY'


class LeaderboardPeriod(enum.Enum):
    DAILY = 'DAILY'
    WEEKLY = 'WEEKLY'
    MONTHLY = 'MONTHLY'
    ALL_TIME = 'ALL_TIME'


class LeaderboardType(enum.Enum):
    USER = 'USER'
    SERVER = 'SERVER'
    HUB = 'HUB'


# Enum PostgreSQL Types
HubModeratorRoleEnum = ENUM(HubModeratorRole, name='Role')
HubActivityLevelEnum = ENUM(HubActivityLevel, name='HubActivityLevel')
InfractionTypeEnum = ENUM(InfractionType, name='InfractionType')
InfractionStatusEnum = ENUM(InfractionStatus, name='InfractionStatus')
AppealStatusEnum = ENUM(AppealStatus, name='AppealStatus')
BlockWordActionEnum = ENUM(BlockWordAction, name='BlockWordAction')
PatternMatchTypeEnum = ENUM(PatternMatchType, name='PatternMatchType')
AlertSeverityEnum = ENUM(AlertSeverity, name='AlertSeverity')
BadgesEnum = ENUM(Badges, name='Badges')
ReportStatusEnum = ENUM(ReportStatus, name='ReportStatus')
BlacklistTypeEnum = ENUM(BlacklistType, name='BlacklistType')
LeaderboardPeriodEnum = ENUM(LeaderboardPeriod, name='LeaderboardPeriod')
LeaderboardTypeEnum = ENUM(LeaderboardType, name='LeaderboardType')

# Association table for Hub-Tag many-to-many relationship
hub_tags = Table(
'_HubToTag',
    SQLModel.metadata,
    Column('A',ForeignKey('Hub.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False),
    Column('B',ForeignKey('Tag.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False),
    Index('_HubToTag_AB_unique', 'A', 'B', unique=True),
    Index('_HubToTag_B_index', 'B'),
)


class Hub(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Hub'

    # Primary fields
    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    name: str = Field(unique=True, nullable=False)
    description: str = Field(sa_column=Column(Text, nullable=False))
    ownerId: str = Field(foreign_key='User.id', nullable=False)
    iconUrl: str = Field(sa_column=Column(Text, nullable=False))
    shortDescription: Optional[str] = Field(default=None, max_length=100)

    # Relationships
    owner: 'User' = Relationship(back_populates='ownedHubs', sa_relationship_kwargs={'lazy': 'noload'})
    rulesAcceptances: List['HubRulesAcceptance'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    moderators: List['HubModerator'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    connections: List['Connection'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    tags: List['Tag'] = Relationship(back_populates='hubs', sa_relationship_kwargs={'secondary': hub_tags, 'lazy': 'noload'})
    upvotes: List['HubUpvote'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    reviews: List['HubReview'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    logConfig: Optional['HubLogConfig'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    blockWords: List['BlockWord'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    antiSwearRules: List['AntiSwearRule'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    infractions: List['Infraction'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    invites: List['HubInvite'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    messages: List['Message'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    reports: List['HubReport'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    activityMetrics: Optional['HubActivityMetrics'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    leaderboardEntries: List['LeaderboardEntry'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})
    announcements: List['HubAnnouncement'] = Relationship(back_populates='hub', sa_relationship_kwargs={'lazy': 'noload'})

    # Timestamps
    createdAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    lastActive: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    # Optional fields
    lastNameChange: Optional[datetime] = Field(default=None, sa_column=Column(DateTime, server_default=func.now()))
    bannerUrl: Optional[str] = None
    welcomeMessage: Optional[str] = None
    language: Optional[str] = None
    region: Optional[str] = None

    # Numeric fields
    settings: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    appealCooldownHours: int = Field(default=168, sa_column=Column(Integer, server_default=text('168'), nullable=False))
    weeklyMessageCount: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))

    # Boolean flags
    private: bool = Field(default=True, sa_column=Column(Boolean, server_default=text('true'), nullable=False))
    locked: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))
    nsfw: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))
    verified: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))
    partnered: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))
    featured: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))

    # Array field
    rules: List[str] = Field(default_factory=list, sa_column=Column(ARRAY(Text), nullable=False))

    # Enum field
    activityLevel: HubActivityLevel = Field(default=HubActivityLevel.LOW, sa_column=Column(    HubActivityLevelEnum,    server_default=HubActivityLevel.LOW.value,    nullable=False))

    __table_args__ = (
        Index('Hub_ownerId_idx', 'ownerId'),
        Index('Hub_verified_featured_private_idx', 'verified', 'featured', 'private'),
        Index('Hub_activityLevel_idx', 'activityLevel'),
        Index('Hub_language_idx', 'language'),
        Index('Hub_nsfw_idx', 'nsfw'),
        Index('Hub_weeklyMessageCount_idx', 'weeklyMessageCount'),
    )


class Tag(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Tag'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    name: str = Field(unique=True, nullable=False)
    category: Optional[str] = Field(default=None, sa_column=Column(Text))
    description: Optional[str] = Field(default=None, sa_column=Column(Text))
    color: Optional[str] = Field(default=None, sa_column=Column(Text))

    hubs: List['Hub'] = Relationship(back_populates='tags', sa_relationship_kwargs={'secondary': hub_tags, 'lazy': 'noload'})

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    isOfficial: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))
    usageCount: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))

    __table_args__ = (
        Index('Tag_category_idx', 'category'),
        Index('Tag_usageCount_idx', 'usageCount'),
    )


class HubUpvote(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubUpvote'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    hub: 'Hub' = Relationship(back_populates='upvotes', sa_relationship_kwargs={'lazy': 'noload'})
    user: 'User' = Relationship(back_populates='upvotedHubs', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubUpvote_userId_idx', 'userId'),
    )


class HubReview(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubReview'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    rating: int
    text: str = Field(sa_column=Column(Text, nullable=False))
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))

    hub: 'Hub' = Relationship(back_populates='reviews', sa_relationship_kwargs={'lazy': 'noload'})
    user: 'User' = Relationship(back_populates='reviews', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubReview_userId_idx', 'userId'),
    )


class HubModerator(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubModerator'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)
    role: HubModeratorRole = Field(default=HubModeratorRole.MODERATOR, sa_column=Column(HubModeratorRoleEnum, nullable=False))

    hub: 'Hub' = Relationship(back_populates='moderators', sa_relationship_kwargs={'lazy': 'noload'})
    user: 'User' = Relationship(back_populates='modPositions', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubModerator_userId_idx', 'userId'),
    )


class Connection(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Connection'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    channelId: str = Field(unique=True, nullable=False)
    invite: Optional[str] = Field(default=None, sa_column=Column(Text))
    webhookURL: str = Field(sa_column=Column(Text, nullable=False))
    serverId: str = Field(foreign_key='ServerData.id', nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    parentId: Optional[str] = None
    connected: bool = Field(default=True, sa_column=Column(Boolean, server_default=text('true'), nullable=False))

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    lastActive: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    server: 'ServerData' = Relationship(back_populates='connections', sa_relationship_kwargs={'lazy': 'noload'})
    hub: 'Hub' = Relationship(back_populates='connections', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('channelId', 'serverId'),UniqueConstraint('hubId', 'serverId'),
        Index('Connection_serverId_idx', 'serverId'),
        Index('Connection_hubId_idx', 'hubId'),
        Index('Connection_hubId_channelId_idx', 'hubId', 'channelId'),
        Index('Connection_lastActive_idx', 'lastActive'),
        Index('Connection_hub_lookup_partial_idx', 'channelId', 'hubId', 'serverId', postgresql_where=text('connected = true')),
        Index('Connection_channel_connected_partial_idx', 'channelId', 'connected', postgresql_where=text('connected = true')),
        Index('Connection_hub_connected_partial_idx', 'hubId', postgresql_where=text('connected = true')),
    )


class Infraction(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Infraction'

    id: str = Field(default_factory=lambda: cuid.generate(10), primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    moderatorId: str = Field(foreign_key='User.id', nullable=False)
    reason: str = Field(sa_column=Column(Text, nullable=False))
    expiresAt: Optional[datetime] = None
    userId: Optional[str] = Field(default=None, foreign_key='User.id')
    serverId: Optional[str] = Field(default=None, foreign_key='ServerData.id')
    serverName: Optional[str] = Field(default=None, sa_column=Column(Text))
    type: InfractionType = Field(sa_column=Column(InfractionTypeEnum, nullable=False))

    hub: 'Hub' = Relationship(back_populates='infractions', sa_relationship_kwargs={'lazy': 'noload'})
    moderator: 'User' = Relationship(back_populates='issuedInfractions', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Infraction.moderatorId]'})
    user: Optional['User'] = Relationship(back_populates='infractions', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Infraction.userId]'})
    server: Optional['ServerData'] = Relationship(back_populates='infractions', sa_relationship_kwargs={'lazy': 'noload'})
    appeals: List['Appeal'] = Relationship(back_populates='infraction', sa_relationship_kwargs={'lazy': 'noload'})

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    status: InfractionStatus = Field(default=InfractionStatus.ACTIVE, sa_column=Column(    InfractionStatusEnum,    server_default=InfractionStatus.ACTIVE.value,    nullable=False))
    notified: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))

    __table_args__ = (
        Index('Infraction_status_hubId_idx', 'status', 'hubId'),
        Index('Infraction_userId_idx', 'userId'),
        Index('Infraction_serverId_idx', 'serverId'),
        Index('Infraction_type_idx', 'type'),
        Index('Infraction_expiresAt_idx', 'expiresAt'),
        Index('Infraction_userId_hubId_status_type_expiresAt_idx', 'userId', 'hubId', 'status', 'type', 'expiresAt',    postgresql_where=text('"userId" IS NOT NULL')),
        Index('Infraction_serverId_hubId_status_type_expiresAt_idx', 'serverId', 'hubId', 'status', 'type', 'expiresAt',    postgresql_where=text('"serverId" IS NOT NULL')),
    )


class Appeal(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Appeal'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    infractionId: str = Field(foreign_key='Infraction.id', nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)
    reason: str = Field(sa_column=Column(Text, nullable=False))

    infraction: 'Infraction' = Relationship(back_populates='appeals', sa_relationship_kwargs={'lazy': 'noload'})
    user: 'User' = Relationship(back_populates='appeals', sa_relationship_kwargs={'lazy': 'noload'})

    createdAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    status: AppealStatus = Field(default=AppealStatus.PENDING, sa_column=Column(    AppealStatusEnum, server_default=AppealStatus.PENDING.value, nullable=False))

    __table_args__ = (
        Index('Appeal_infractionId_idx', 'infractionId'),
        Index('Appeal_userId_idx', 'userId'),
        Index('Appeal_status_idx', 'status'),
        Index('Appeal_createdAt_idx', 'createdAt'),
    )


class BlockWord(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'BlockWord'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    name: str
    createdBy: str = Field(foreign_key='User.id', nullable=False)

    createdAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))

    words: str
    actions: List[BlockWordAction] = Field(sa_column=Column(ARRAY(BlockWordActionEnum), nullable=False))

    hub: 'Hub' = Relationship(back_populates='blockWords', sa_relationship_kwargs={'lazy': 'noload'})
    creator: 'User' = Relationship(back_populates='blockWordsCreated', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('hubId', 'name'),
        Index('BlockWord_hubId_idx', 'hubId'),
    )


class AntiSwearRule(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'AntiSwearRule'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    name: str
    createdBy: str = Field(foreign_key='User.id', nullable=False)
    enabled: bool = Field(default=True, sa_column=Column(Boolean, server_default=text('true'), nullable=False))
    muteDurationMinutes: Optional[int] = None

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    actions: List[BlockWordAction] = Field(default_factory=list, sa_column=Column(    ARRAY(BlockWordActionEnum),    server_default=text('\'{}\'::"BlockWordAction"[]'),    nullable=False))

    hub: 'Hub' = Relationship(back_populates='antiSwearRules', sa_relationship_kwargs={'lazy': 'noload'})
    creator: 'User' = Relationship(back_populates='antiSwearRulesCreated', sa_relationship_kwargs={'lazy': 'noload'})
    patterns: List['AntiSwearPattern'] = Relationship(back_populates='rule', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    whitelists: List['AntiSwearWhitelist'] = Relationship(back_populates='rule', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})

    __table_args__ = (
        UniqueConstraint('hubId', 'name'),
        Index('AntiSwearRule_hubId_idx', 'hubId'),
    )


class AntiSwearPattern(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'AntiSwearPattern'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    ruleId: str = Field(foreign_key='AntiSwearRule.id', nullable=False)
    pattern: str
    matchType: PatternMatchType = Field(default=PatternMatchType.EXACT, sa_column=Column(    PatternMatchTypeEnum,    server_default=PatternMatchType.EXACT.value,    nullable=False))

    rule: 'AntiSwearRule' = Relationship(back_populates='patterns', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        Index('AntiSwearPattern_ruleId_idx', 'ruleId'),
        Index('AntiSwearPattern_matchType_idx', 'matchType'),
    )


class AntiSwearWhitelist(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'AntiSwearWhitelist'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    ruleId: str = Field(foreign_key='AntiSwearRule.id', nullable=False)
    word: str
    createdBy: str = Field(foreign_key='User.id', nullable=False)
    reason: Optional[str] = None
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    rule: 'AntiSwearRule' = Relationship(back_populates='whitelists', sa_relationship_kwargs={'lazy': 'noload'})
    creator: 'User' = Relationship(back_populates='antiSwearWhitelistsCreated', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('ruleId', 'word'),
        Index('AntiSwearWhitelist_ruleId_idx', 'ruleId'),
        Index('AntiSwearWhitelist_word_idx', 'word'),
    )


class HubLogConfig(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubLogConfig'

    id: str = Field(default_factory=lambda: cuid.generate(10), primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', unique=True, nullable=False)
    modLogsChannelId: Optional[str] = None
    modLogsRoleId: Optional[str] = None
    joinLeavesChannelId: Optional[str] = None
    joinLeavesRoleId: Optional[str] = None
    appealsChannelId: Optional[str] = None
    appealsRoleId: Optional[str] = None
    reportsChannelId: Optional[str] = None
    reportsRoleId: Optional[str] = None
    networkAlertsChannelId: Optional[str] = None
    networkAlertsRoleId: Optional[str] = None
    messageModerationChannelId: Optional[str] = None
    messageModerationRoleId: Optional[str] = None

    hub: 'Hub' = Relationship(back_populates='logConfig', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        Index('HubLogConfig_hubId_idx', 'hubId'),
    )


class HubInvite(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubInvite'

    code: str = Field(default_factory=lambda: cuid.generate(7),primary_key=True,unique=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    expires: Optional[datetime] = None
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    maxUses: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    uses: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))

    hub: 'Hub' = Relationship(back_populates='invites', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        Index('HubInvite_hubId_idx', 'hubId'),
    )


class HubRulesAcceptance(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubRulesAcceptance'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    acceptedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    user: 'User' = Relationship(back_populates='rulesAcceptances', sa_relationship_kwargs={'lazy': 'noload'})
    hub: 'Hub' = Relationship(back_populates='rulesAcceptances', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('userId', 'hubId'),
        Index('HubRulesAcceptance_hubId_userId_idx', 'hubId', 'userId'),
    )


class HubActivityMetrics(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubActivityMetrics'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', unique=True, nullable=False)

    lastUpdated: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    messagesLast24h: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    activeUsersLast24h: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    newConnectionsLast24h: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    messagesLast7d: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    activeUsersLast7d: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    newConnectionsLast7d: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    memberGrowthRate: float = Field(default=0.0, sa_column=Column(Float, server_default=text('0.0'), nullable=False))
    engagementRate: float = Field(default=0.0, sa_column=Column(Float, server_default=text('0.0'), nullable=False))

    hub: 'Hub' = Relationship(back_populates='activityMetrics', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        Index('HubActivityMetrics_hubId_idx', 'hubId'),
        Index('HubActivityMetrics_lastUpdated_idx', 'lastUpdated'),
    )


class DevAlerts(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'DevAlerts'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    title: str
    content: str
    imageUrl: Optional[str] = None
    thumbnailUrl: Optional[str] = None
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))


class HubAnnouncement(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubAnnouncement'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    title: str = Field(sa_column=Column(Text, nullable=False))
    content: str = Field(sa_column=Column(Text, nullable=False))
    frequencyMs: int
    previousAnnouncement: Optional[datetime] = None
    nextAnnouncement: Optional[datetime] = None
    imageUrl: Optional[str] = None
    thumbnailUrl: Optional[str] = None

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    hub: 'Hub' = Relationship(back_populates='announcements', sa_relationship_kwargs={'lazy': 'noload'})


class ServerData(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'ServerData'

    id: str = Field(primary_key=True, nullable=False)
    name: str = Field(sa_column=Column(Text, nullable=False))
    lastMessageAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    createdAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    inviteCode: Optional[str] = None
    messageCount: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    iconUrl: Optional[str] = None

    connections: List['Connection'] = Relationship(back_populates='server', sa_relationship_kwargs={'lazy': 'noload'})
    infractions: List['Infraction'] = Relationship(back_populates='server', sa_relationship_kwargs={'lazy': 'noload'})
    serverBlacklists: List['ServerBlacklist'] = Relationship(back_populates='server', sa_relationship_kwargs={'lazy': 'noload'})
    leaderboardEntries: List['LeaderboardEntry'] = Relationship(back_populates='server', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})

    __table_args__ = (
        Index('ServerData_lastMessageAt_idx', text('"lastMessageAt" DESC')),
        Index('ServerData_messageCount_idx', 'messageCount'),
        Index('ServerData_createdAt_idx', 'createdAt'),
    )


class ReputationLog(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'ReputationLog'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    giverId: str
    receiverId: str = Field(foreign_key='User.id', nullable=False)
    reason: str
    timestamp: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    automatic: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))

    receiver: 'User' = Relationship(back_populates='reputationLog', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        Index('ReputationLog_receiverId_idx', 'receiverId'),
        Index('ReputationLog_giverId_idx', 'giverId'),
    )

class Achievement(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Achievement'

    id: str = Field(primary_key=True, nullable=False)
    name: str
    description: str
    badgeEmoji: str
    badgeUrl: str = Field(sa_column=Column(Text, nullable=False))
    threshold: int = Field(default=1, nullable=False)
    secret: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))

    userAchievements: List['UserAchievement'] = Relationship(back_populates='achievement', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    userProgress: List['UserAchievementProgress'] = Relationship(back_populates='achievement', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})


class UserAchievement(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'UserAchievement'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)
    achievementId: str = Field(foreign_key='Achievement.id', nullable=False)
    unlockedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    user: 'User' = Relationship(back_populates='achievements', sa_relationship_kwargs={'lazy': 'noload'})
    achievement: 'Achievement' = Relationship(back_populates='userAchievements', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        UniqueConstraint('userId', 'achievementId'),
        Index('UserAchievement_userId_idx', 'userId'),
        Index('UserAchievement_achievementId_idx', 'achievementId'),
    )


class UserAchievementProgress(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'UserAchievementProgress'

    userId: str = Field(foreign_key='User.id', primary_key=True, nullable=False)
    achievementId: str = Field(foreign_key='Achievement.id', primary_key=True, nullable=False)
    currentValue: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))

    user: 'User' = Relationship(back_populates='achievementProgress', sa_relationship_kwargs={'lazy': 'noload'})
    achievement: 'Achievement' = Relationship(back_populates='userProgress', sa_relationship_kwargs={'lazy': 'noload'})


class Message(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Message'

    id: str = Field(primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    content: str
    imageUrl: Optional[str] = None
    channelId: str
    guildId: str
    authorId: str
    referredMessageId: Optional[str] = Field(default=None, foreign_key='Message.id')
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    hub: 'Hub' = Relationship(back_populates='messages', sa_relationship_kwargs={'lazy': 'noload'})
    hubReports: List['HubReport'] = Relationship(back_populates='message', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    globalReports: List['GlobalReport'] = Relationship(back_populates='message', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    broadcasts: List['Broadcast'] = Relationship(back_populates='message', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    reactions: List['HubMessageReaction'] = Relationship(back_populates='message', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})

    referredTo: Optional['Message'] = Relationship(back_populates='referredBy', sa_relationship_kwargs={'lazy': 'noload', 'remote_side': '[Message.id]', 'foreign_keys': '[Message.referredMessageId]'})
    referredBy: List['Message'] = Relationship(back_populates='referredTo', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Message.referredMessageId]'})

    __table_args__ = (
        Index('Message_hubId_idx', 'hubId'),
        Index('Message_authorId_idx', 'authorId'),
        Index('Message_guildId_idx', 'guildId'),
        Index('Message_referredMessageId_idx', 'referredMessageId'),
        Index('Message_createdAt_idx', text('"createdAt" DESC')),
        Index('Message_guildId_authorId_idx', 'guildId', 'authorId'),
        Index('Message_hubId_createdAt_idx', 'hubId', text('"createdAt" DESC')),
        Index('Message_guildId_hubId_idx', 'guildId', 'hubId'),
        Index('Message_channel_timestamp_idx', 'channelId', text('"createdAt" DESC')),
        Index('Message_author_timestamp_idx', 'authorId', text('"createdAt" DESC')),
    )


class HubMessageReaction(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubMessageReaction'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    messageId: str = Field(foreign_key='Message.id', nullable=False)
    emoji: str = Field(max_length=64, nullable=False)
    users: List[str] = Field(default_factory=list, sa_column=Column(ARRAY(Text), nullable=False))

    message: 'Message' = Relationship(back_populates='reactions')

    __table_args__ = (
        UniqueConstraint('messageId', 'emoji'),
    )


class Broadcast(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Broadcast'

    id: str = Field(primary_key=True, nullable=False)
    messageId: str = Field(foreign_key='Message.id', nullable=False)
    guildId: str
    channelId: str
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))

    message: 'Message' = Relationship(back_populates='broadcasts', sa_relationship_kwargs={'lazy': 'noload'})

    __table_args__ = (
        Index('Broadcast_messageId_idx', 'messageId'),
        Index('Broadcast_channelId_idx', 'channelId'),
        Index('Broadcast_createdAt_idx', text('"createdAt" DESC')),
        Index('Broadcast_messageId_channelId_idx', 'messageId', 'channelId'),
        Index('Broadcast_id_messageId_channelId_createdAt_idx', 'id', 'messageId', 'channelId', 'createdAt'),
    )


class GlobalReport(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'GlobalReport'

    id: str = Field(default_factory=lambda: cuid.generate(10), primary_key=True, nullable=False)
    reporterId: str = Field(foreign_key='User.id', nullable=False)
    reportedUserId: str = Field(foreign_key='User.id', nullable=False)
    reportedServerId: str
    messageId: Optional[str] = Field(default=None, foreign_key='Message.id')
    reason: str
    handledBy: Optional[str] = Field(default=None, foreign_key='User.id')
    handledAt: Optional[datetime] = None

    reporter: 'User' = Relationship(back_populates='globalReportsSubmitted', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[GlobalReport.reporterId]'})
    reportedUser: 'User' = Relationship(back_populates='globalReportsReceived', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[GlobalReport.reportedUserId]'})
    handler: Optional['User'] = Relationship(back_populates='globalReportsHandled', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[GlobalReport.handledBy]'})
    message: Optional['Message'] = Relationship(back_populates='globalReports', sa_relationship_kwargs={'lazy': 'noload'})

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    status: ReportStatus = Field(default=ReportStatus.PENDING, sa_column=Column(    ReportStatusEnum, server_default=ReportStatus.PENDING.value, nullable=False))

    __table_args__ = (
        Index('GlobalReport_status_idx', 'status'),
        Index('GlobalReport_createdAt_idx', 'createdAt'),
        Index('GlobalReport_reporterId_idx', 'reporterId'),
        Index('GlobalReport_messageId_idx', 'messageId'),
        Index('GlobalReport_handledBy_idx', 'handledBy'),
        Index('GlobalReport_reportedUserId_idx', 'reportedUserId'),
    )


class HubReport(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'HubReport'

    id: str = Field(default_factory=lambda: cuid.generate(10), primary_key=True, nullable=False)
    hubId: str = Field(foreign_key='Hub.id', nullable=False)
    reporterId: str = Field(foreign_key='User.id', nullable=False)
    messageId: Optional[str] = Field(sa_column=Column(String))
    reportedServerId: str = Field(sa_column=Column(String))

    reportedUserId: str = Field(foreign_key='User.id', nullable=False)
    reportedServerId: str
    messageId: Optional[str] = Field(default=None, foreign_key='Message.id')
    reason: str
    handledBy: Optional[str] = Field(default=None, foreign_key='User.id')
    handledAt: Optional[datetime] = None

    hub: 'Hub' = Relationship(back_populates='reports', sa_relationship_kwargs={'lazy': 'noload'})
    reporter: 'User' = Relationship(back_populates='hubReportsSubmitted', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[HubReport.reporterId]'})
    reportedUser: 'User' = Relationship(back_populates='hubReportsReceived', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[HubReport.reportedUserId]'})
    handler: Optional['User'] = Relationship(back_populates='hubReportsHandled', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[HubReport.handledBy]'})
    message: Optional['Message'] = Relationship(back_populates='hubReports', sa_relationship_kwargs={'lazy': 'noload'})

    createdAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    status: ReportStatus = Field(default=ReportStatus.PENDING, sa_column=Column(    ReportStatusEnum, server_default=ReportStatus.PENDING.value, nullable=False))

    __table_args__ = (
        Index('HubReport_hubId_idx', 'hubId'),
        Index('HubReport_status_idx', 'status'),
        Index('HubReport_createdAt_idx', 'createdAt'),
        Index('HubReport_reporterId_idx', 'reporterId'),
        Index('HubReport_messageId_idx', 'messageId'),
        Index('HubReport_handledBy_idx', 'handledBy'),
        Index('HubReport_reportedUserId_idx', 'reportedUserId'),
    )


class Blacklist(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'Blacklist'

    id: str = Field(default_factory=lambda: cuid.generate(10), primary_key=True, nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)
    moderatorId: str = Field(foreign_key='User.id', nullable=False)
    reason: str
    expiresAt: Optional[datetime] = None

    user: 'User' = Relationship(back_populates='blacklists', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Blacklist.userId]'})
    moderator: 'User' = Relationship(back_populates='issuedBlacklists', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Blacklist.moderatorId]'})

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    type: BlacklistType = Field(default=BlacklistType.PERMANENT, sa_column=Column(    BlacklistTypeEnum,    server_default=BlacklistType.PERMANENT.value,    nullable=False))

    __table_args__ = (
        Index('Blacklist_userId_idx', 'userId'),
        Index('Blacklist_expiresAt_idx', 'expiresAt'),
        Index('Blacklist_createdAt_idx', 'createdAt'),
        Index('Blacklist_userId_expiresAt_idx', 'userId', 'expiresAt'),
        Index('Blacklist_userId_type_idx', 'userId', 'type'),
    )


class ServerBlacklist(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'ServerBlacklist'

    id: str = Field(default_factory=lambda: cuid.generate(10), primary_key=True, nullable=False)
    serverId: str = Field(foreign_key='ServerData.id', nullable=False)
    moderatorId: str = Field(foreign_key='User.id', nullable=False)
    reason: str
    duration: Optional[int] = None
    expiresAt: Optional[datetime] = None

    server: 'ServerData' = Relationship(back_populates='serverBlacklists', sa_relationship_kwargs={'lazy': 'noload'})
    moderator: 'User' = Relationship(back_populates='issuedServerBlacklists', sa_relationship_kwargs={'lazy': 'noload'})

    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))
    type: BlacklistType = Field(default=BlacklistType.PERMANENT, sa_column=Column(    BlacklistTypeEnum,    server_default=BlacklistType.PERMANENT.value,    nullable=False))

    __table_args__ = (
        Index('ServerBlacklist_serverId_idx', 'serverId'),
        Index('ServerBlacklist_expiresAt_idx', 'expiresAt'),
        Index('ServerBlacklist_createdAt_idx', 'createdAt'),
        Index('ServerBlacklist_serverId_expiresAt_idx', 'serverId', 'expiresAt'),
        Index('ServerBlacklist_serverId_type_idx', 'serverId', 'type'),
    )


class LeaderboardEntry(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'LeaderboardEntry'

    id: str = Field(default_factory=cuid.generate, primary_key=True, nullable=False)
    userId: str = Field(foreign_key='User.id', nullable=False)
    hubId: Optional[str] = Field(default=None, foreign_key='Hub.id')
    serverId: Optional[str] = Field(default=None, foreign_key='ServerData.id')
    period: LeaderboardPeriod = Field(sa_column=Column(LeaderboardPeriodEnum, nullable=False))
    type: LeaderboardType = Field(sa_column=Column(LeaderboardTypeEnum, nullable=False))

    messageCount: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    score: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    rank: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))

    user: 'User' = Relationship(back_populates='leaderboardEntries', sa_relationship_kwargs={'lazy': 'noload'})
    hub: Optional['Hub'] = Relationship(back_populates='leaderboardEntries', sa_relationship_kwargs={'lazy': 'noload'})
    server: Optional['ServerData'] = Relationship(back_populates='leaderboardEntries', sa_relationship_kwargs={'lazy': 'noload'})

    lastActivityAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    createdAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), nullable=False))
    updatedAt: datetime = Field(sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False))

    __table_args__ = (
        Index('LeaderboardEntry_type_period_score_idx', 'type', 'period', 'score'),
        Index('LeaderboardEntry_userId_type_period_idx', 'userId', 'type', 'period'),
        Index('LeaderboardEntry_hubId_type_period_idx', 'hubId', 'type', 'period'),
        Index('LeaderboardEntry_serverId_type_period_idx', 'serverId', 'type', 'period'),
        Index('LeaderboardEntry_lastActivityAt_idx', 'lastActivityAt'),
    )


class User(SQLModel, table=True):
    __tablename__: ClassVar[Any] = 'User'

    id: str = Field(primary_key=True, nullable=False)
    name: Optional[str] = Field(default=None, sa_column=Column(Text))
    image: Optional[str] = Field(default=None, sa_column=Column(Text))

    ownedHubs: List['Hub'] = Relationship(back_populates='owner', sa_relationship_kwargs={'lazy': 'noload'})
    appeals: List['Appeal'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload'})
    infractions: List['Infraction'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Infraction.userId]'})
    issuedInfractions: List['Infraction'] = Relationship(back_populates='moderator', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Infraction.moderatorId]'})
    upvotedHubs: List['HubUpvote'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload'})
    reputationLog: List['ReputationLog'] = Relationship(back_populates='receiver', sa_relationship_kwargs={'lazy': 'noload'})
    modPositions: List['HubModerator'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload'})
    reviews: List['HubReview'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    blockWordsCreated: List['BlockWord'] = Relationship(back_populates='creator', sa_relationship_kwargs={'lazy': 'noload'})
    antiSwearRulesCreated: List['AntiSwearRule'] = Relationship(back_populates='creator', sa_relationship_kwargs={'lazy': 'noload'})
    antiSwearWhitelistsCreated: List['AntiSwearWhitelist'] = Relationship(back_populates='creator', sa_relationship_kwargs={'lazy': 'noload'})
    rulesAcceptances: List['HubRulesAcceptance'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload'})
    accounts: List['Account'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    sessions: List['Session'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    hubReportsSubmitted: List['HubReport'] = Relationship(back_populates='reporter', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[HubReport.reporterId]'})
    hubReportsReceived: List['HubReport'] = Relationship(back_populates='reportedUser', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[HubReport.reportedUserId]'})
    hubReportsHandled: List['HubReport'] = Relationship(back_populates='handler', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[HubReport.handledBy]'})
    globalReportsSubmitted: List['GlobalReport'] = Relationship(back_populates='reporter', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[GlobalReport.reporterId]'})
    globalReportsReceived: List['GlobalReport'] = Relationship(back_populates='reportedUser', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[GlobalReport.reportedUserId]'})
    globalReportsHandled: List['GlobalReport'] = Relationship(back_populates='handler', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[GlobalReport.handledBy]'})
    achievements: List['UserAchievement'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    achievementProgress: List['UserAchievementProgress'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})
    blacklists: List['Blacklist'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Blacklist.userId]'})
    issuedBlacklists: List['Blacklist'] = Relationship(back_populates='moderator', sa_relationship_kwargs={'lazy': 'noload', 'foreign_keys': '[Blacklist.moderatorId]'})
    issuedServerBlacklists: List['ServerBlacklist'] = Relationship(back_populates='moderator', sa_relationship_kwargs={'lazy': 'noload'})
    leaderboardEntries: List['LeaderboardEntry'] = Relationship(back_populates='user', sa_relationship_kwargs={'lazy': 'noload', 'cascade': 'all, delete-orphan'})

    lastMessageAt: Optional[datetime] = Field(default=None, sa_column=Column(DateTime, server_default=func.now()))
    inboxLastReadDate: Optional[datetime] = Field(default=None, sa_column=Column(DateTime, server_default=func.now()))
    createdAt: Optional[datetime] = Field(default=None, sa_column=Column(DateTime, server_default=func.now()))
    updatedAt: Optional[datetime] = Field(default=None, sa_column=Column(DateTime, server_default=func.now(), onupdate=func.now()))
    activityLevel: Optional[HubActivityLevel] = Field(default=None, sa_column=Column(HubActivityLevelEnum))
    lastHubJoinAt: Optional[datetime] = None
    email: Optional[str] = Field(default=None, unique=True)
    emailVerified: Optional[bool] = None

    showBadges: bool = Field(default=True, sa_column=Column(Boolean, server_default=text('true'), nullable=False))
    mentionOnReply: bool = Field(default=True, sa_column=Column(Boolean, server_default=text('true'), nullable=False))
    showNsfwHubs: bool = Field(default=False, sa_column=Column(Boolean, server_default=text('false'), nullable=False))

    voteCount: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    reputation: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    messageCount: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    hubJoinCount: int = Field(default=0, sa_column=Column(Integer, server_default=text('0'), nullable=False))
    hubEngagementScore: float = Field(default=0.0, sa_column=Column(Float, server_default=text('0.0'), nullable=False))

    locale: Optional[str] = Field(default='en')
    lastVoted: Optional[datetime] = None

    badges: List[Badges] = Field(default_factory=list, sa_column=Column(ARRAY(BadgesEnum), server_default=text('\'{}\'::"Badges"[]'),    nullable=False))
    preferredLanguages: List[str] = Field(default_factory=list, sa_column=Column(ARRAY(Text), server_default=text("'{}'::text[]"), nullable=False))

    __table_args__ = (
        Index('User_reputation_idx', 'reputation'),
        Index('User_locale_idx', 'locale'),
        Index('User_email_idx', 'email'),
        Index('User_voteCount_idx', 'voteCount'),
        Index('User_lastVoted_idx', 'lastVoted'),
        Index('User_createdAt_idx', 'createdAt'),
    )


class Session(SQLModel, table=True):
    __tablename__: ClassVar[Any] = "Session"
    __table_args__ = (UniqueConstraint("token"),)

    id: str = Field(primary_key=True, nullable=False)
    expiresAt: datetime
    token: str = Field(nullable=False)
    createdAt: datetime
    updatedAt: datetime
    ipAddress: Optional[str] = None
    userAgent: Optional[str] = None
    userId: str = Field(foreign_key="User.id")

    user: "User" = Relationship(back_populates="sessions", sa_relationship_kwargs={'lazy': 'noload'})


class Account(SQLModel, table=True):
    __tablename__: ClassVar[Any] = "Account"

    id: str = Field(primary_key=True, nullable=False)
    accountId: str
    providerId: str
    userId: str = Field(foreign_key="User.id")

    accessToken: Optional[str] = None
    refreshToken: Optional[str] = None
    idToken: Optional[str] = None
    accessTokenExpiresAt: Optional[datetime] = None
    refreshTokenExpiresAt: Optional[datetime] = None
    scope: Optional[str] = None
    password: Optional[str] = None
    createdAt: datetime
    updatedAt: datetime

    user: "User" = Relationship(back_populates="accounts")


class Verification(SQLModel, table=True):
    __tablename__: ClassVar[Any] = "Verification"

    id: str = Field(primary_key=True, nullable=False)
    identifier: str
    value: str
    expiresAt: datetime
    createdAt: Optional[datetime] = None
    updatedAt: Optional[datetime] = None
