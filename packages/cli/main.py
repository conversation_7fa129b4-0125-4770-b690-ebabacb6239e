#!/usr/bin/env python

import subprocess
import shutil
from pathlib import Path
from typing_extensions import Annotated

import typer
from rich.console import Console


# --- Basic Setup ---
def check_uv(uv_path: str = typer.Option(shutil.which('uv'), hidden=True)):
    """A dependency callback that checks if 'uv' is available."""
    if not uv_path:
        console.print("[bold red]Error: 'uv' command not found.[/bold red]")
        console.print('Please install it from Astral: [cyan]https://github.com/astral-sh/uv[/cyan]')
        raise typer.Exit(code=1)


app = typer.Typer(
    name='interchat',
    help='InterChat Workspace Management CLI',
    add_completion=True,
    no_args_is_help=True,
    callback=check_uv,
)
console = Console()

# Resolve paths relative to this script's location
SCRIPT_DIR = Path(__file__).parent.resolve()
PACKAGES_DIR = SCRIPT_DIR.parent


# --- Helper Functions ---
def _run_command(command: list[str], cwd: Path | None = None):
    """Helper to run a command and stream its output."""
    try:
        subprocess.run(command, check=True, cwd=cwd)
    except subprocess.CalledProcessError:
        console.print(f'[bold red]Error running command: {" ".join(command)}[/bold red]')
        raise typer.Exit(code=1)
    except FileNotFoundError as e:
        print(e)
        console.print(
            f"[bold red]Error: Command '{command[0]}' not found. Is it installed and in your PATH?[/bold red]"
        )
        raise typer.Exit(code=1)


# --- CLI Commands ---


@app.command()
def setup():
    """Initial workspace setup (syncs all packages)."""
    console.print('Setting up InterChat workspace...')
    _run_command(['uv', 'sync', '--all-packages'])
    console.print('✅ [bold green]Workspace setup complete![/bold green]')


@app.command()
def sync():
    """Syncs all packages in the workspace."""
    console.print('🔄 Syncing all packages...')
    _run_command(['uv', 'sync', '--all-packages'])
    console.print('✅ [bold green]All packages synced![/bold green]')


@app.command(name='sync-package')
def sync_package(
    package: Annotated[str, typer.Argument(help="The name of the package to sync (e.g., 'bot').")],
):
    """Syncs dependencies for a specific package."""
    console.print(f'🔄 Syncing package: [bold magenta]{package}[/bold magenta]')
    _run_command(['uv', 'sync', '-p', package])
    console.print(f'✅ [bold green]Package {package} synced![/bold green]')


@app.command(name='run-bot')
def run_bot():
    """Runs the Discord bot."""
    bot_dir = PACKAGES_DIR / 'bot'
    _run_command(['uv', 'run', '-m', 'main'], cwd=bot_dir)


@app.command(name='run-api')
def run_api():
    console.print('🚀 Starting API server...')
    api_dir = PACKAGES_DIR / 'api'
    _run_command(['uv', 'run', 'uvicorn', 'src.main:app', '--reload'], cwd=api_dir)


@app.command()
def lint():
    """Runs linting on all packages."""
    console.print('🔍 Linting all packages...')
    packages_to_lint = ['bot', 'api', 'shared/db']  # Add packages here
    for pkg_name in packages_to_lint:
        pkg_dir = PACKAGES_DIR / pkg_name
        if pkg_dir.exists():
            console.print(f'Linting [bold magenta]{pkg_name}[/bold magenta] package...')
            _run_command(['uv', 'run', '--group', 'dev', 'ruff', 'check', '.'], cwd=pkg_dir)


@app.command()
def format():
    """Formats code in all packages using ruff."""
    console.print('💅 Formatting all packages...')
    packages_to_format = ['bot', 'api', 'shared/db']  # Add packages here
    for pkg_name in packages_to_format:
        pkg_dir = PACKAGES_DIR / pkg_name
        if pkg_dir.exists():
            console.print(f'Formatting [bold magenta]{pkg_name}[/bold magenta] package...')
            _run_command(['uv', 'run', '--group', 'dev', 'ruff', 'format', '.'], cwd=pkg_dir)
            console.print(f'✅ [bold green]Package {pkg_name} formatted![/bold green]')


@app.command()
def alembic(desc: Annotated[str, typer.Option('-m', help='Description of the migration.')]):
    """Generates a new database migration."""
    console.print('🔍 Generating database migration...')
    db_dir = PACKAGES_DIR / 'shared' / 'db'
    _run_command(['uv', 'run', 'alembic', 'revision', '--autogenerate', '-m', desc], cwd=db_dir)


@app.command()
def migrate():
    """Runs database migrations."""
    console.print('🗄️ Running database migrations...')
    db_dir = PACKAGES_DIR / 'shared' / 'db'
    alembic_ini = db_dir / 'alembic.ini'
    alembic_example = db_dir / 'alembic.example.ini'

    if not alembic_ini.exists() and alembic_example.exists():
        shutil.copy(alembic_example, alembic_ini)
        console.print(
            '⚠️ Created [yellow]alembic.ini[/yellow] from example. Please configure your database URL.'
        )

    _run_command(['uv', 'run', 'alembic', 'upgrade', 'head'], cwd=db_dir)
    console.print('✅ [bold green]Database migrations complete![/bold green]')


@app.command(name='add-dep')
def add_dependency(
    package: Annotated[str, typer.Argument(help='Package to add the dependency to.')],
    dependency: Annotated[
        str, typer.Argument(help="The dependency to add (e.g., 'uvicorn[standard]').")
    ],
):
    """Adds a dependency to a specific package."""
    console.print(
        f'📦 Adding [bold cyan]{dependency}[/bold cyan] to package [bold magenta]{package}[/bold magenta]...'
    )
    _run_command(['uv', 'add', '--package', package, dependency])
    console.print('✅ [bold green]Dependency added![/bold green]')


@app.command()
def check():
    """Checks the workspace status and dependency tree."""
    console.print('🔍 Checking workspace status...')
    _run_command(['uv', 'tree'])


@app.command()
def typecheck():
    """Runs type checking on all packages."""
    console.print('🔍 Type checking all packages...')
    packages_to_check = ['bot', 'api', 'shared/db']  # Add packages here
    for pkg_name in packages_to_check:
        pkg_dir = PACKAGES_DIR / pkg_name
        if pkg_dir.exists():
            console.print(f'Type checking [bold magenta]{pkg_name}[/bold magenta] package...')
            _run_command(['uv', 'run', '--group', 'dev', 'pyright', '.'], cwd=pkg_dir)
            console.print(f'✅ [bold green]Package {pkg_name} type checked![/bold green]')


if __name__ == '__main__':
    app()
