from contextlib import asynccontextmanager
from typing import As<PERSON><PERSON>enerator

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from .core.config import settings
from .core.database import initialize_database
from .routers import (
    auth_router,
    hub_router,
    user_router,
    announcement_router,
    appeal_router,
    connection_router,
    discover_router,
    moderation_router,
    server_router,
    tags_router,
)


class HealthResponse(BaseModel):
    status: str
    version: str


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    # Startup
    print(f'🚀 Starting InterChat API server v{app.version}')
    # Initialize database connection
    db = initialize_database()
    yield
    # Shutdown
    if db:
        await db.dispose()
    print('👋 InterChat API server shutdown complete')


# Create FastAPI app
app = FastAPI(
    title='InterChat API',
    description='REST API for InterChat Discord bot - migrated from tRPC',
    version='0.1.0',
    lifespan=lifespan,
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Global exception handler for consistent error responses."""
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={'error': exc.detail, 'code': exc.status_code},
        )

    # Log unexpected errors
    print(f'Unexpected error: {exc}')
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={'error': 'Internal server error', 'code': 500},
    )


@app.get('/health', response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Health check endpoint."""
    return HealthResponse(status='healthy', version=app.version)


# Include routers
app.include_router(auth_router)
app.include_router(hub_router)
app.include_router(user_router)
app.include_router(connection_router)
app.include_router(server_router)
app.include_router(moderation_router)
app.include_router(announcement_router)
app.include_router(discover_router)
app.include_router(tags_router)
app.include_router(appeal_router)


if __name__ == '__main__':
    import uvicorn

    uvicorn.run(
        'main:app',
        host='0.0.0.0',
        port=8000,
        reload=True,
        log_level='info',
    )
