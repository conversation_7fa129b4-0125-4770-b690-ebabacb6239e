from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # App metadata
    APP_NAME: str = 'InterChat API'
    APP_VERSION: str = '0.1.0'
    HOST: str = '0.0.0.0'
    PORT: int = 8000

    # Database
    DATABASE_URL: str = ''

    # Authentication
    SECRET_KEY: str = 'your-secret-key-here'
    ALGORITHM: str = 'HS256'
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Discord API
    DISCORD_BOT_TOKEN: str = ''
    DISCORD_CLIENT_ID: str = ''
    DISCORD_CLIENT_SECRET: str = ''

    # CORS
    ALLOWED_ORIGINS: List[str] = [
        'http://localhost:3000',
        'http://localhost:3001',
        'https://interchat.tech',
        'https://www.interchat.tech',
    ]

    # Admin users (comma-separated Discord user IDs)
    ADMIN_USER_IDS: str = '701727675311587358,934537337113804891'

    # Environment
    ENVIRONMENT: str = 'development'
    DEBUG: bool = True

    class Config:
        env_file = '.env'
        case_sensitive = True


# Global settings instance
settings = Settings()
