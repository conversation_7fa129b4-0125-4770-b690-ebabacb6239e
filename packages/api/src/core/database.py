from typing import AsyncGenerator
from db.database import get_db, init_database
from sqlmodel.ext.asyncio.session import AsyncSession

from .config import settings


def initialize_database():
    """Initialize the database connection"""
    return init_database(settings.DATABASE_URL)


async def get_database() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session"""
    database = get_db()
    async with database.get_session() as session:
        yield session
