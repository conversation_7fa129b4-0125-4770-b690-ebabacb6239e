from enum import IntEnum
from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import Hu<PERSON>, HubModerator, HubModeratorRole


class PermissionLevel(IntEnum):
    """Permission levels for hub access"""

    NONE = 0
    MODERATOR = 1
    MANAGER = 2
    OWNER = 3


async def get_user_hub_permission(user_id: str, hub_id: str, db: AsyncSession) -> PermissionLevel:
    """Get user's permission level for a specific hub."""

    if not user_id or not hub_id:
        return PermissionLevel.NONE

    # Get the hub with moderator info
    hub_query = select(Hub).where(Hub.id == hub_id)
    result = await db.exec(hub_query)
    hub = result.first()

    if not hub:
        return PermissionLevel.NONE

    # Check if user is hub owner
    if hub.ownerId == user_id:
        return PermissionLevel.OWNER

    # Check if user is a moderator
    moderator_query = select(HubModerator).where(
        HubModerator.hubId == hub_id, HubModerator.userId == user_id
    )
    result = await db.exec(moderator_query)
    moderator = result.first()

    if moderator:
        if moderator.role == HubModeratorRole.MANAGER:
            return PermissionLevel.MANAGER
        elif moderator.role == HubModeratorRole.MODERATOR:
            return PermissionLevel.MODERATOR

    return PermissionLevel.NONE


async def get_user_accessible_hubs(user_id: str, db: AsyncSession) -> list[dict]:
    """Get all hubs a user has access to with their permission level."""
    if not user_id:
        return []

    accessible_hubs = []

    # Get owned hubs
    owned_hubs_query = select(Hub).where(Hub.ownerId == user_id)
    result = await db.exec(owned_hubs_query)
    owned_hubs = result.all()

    for hub in owned_hubs:
        accessible_hubs.append(
            {
                'id': hub.id,
                'name': hub.name,
                'iconUrl': hub.iconUrl,
                'role': 'OWNER',
                'permissionLevel': PermissionLevel.OWNER,
            }
        )

    # Get moderated hubs
    moderated_hubs_query = (
        select(HubModerator, Hub)
        .join(Hub, col(HubModerator.hubId) == Hub.id)
        .where(HubModerator.userId == user_id)
    )
    result = await db.exec(moderated_hubs_query)
    moderated_hubs = result.all()

    for moderator, hub in moderated_hubs:
        permission_level = (
            PermissionLevel.MANAGER
            if moderator.role == HubModeratorRole.MANAGER
            else PermissionLevel.MODERATOR
        )

        accessible_hubs.append(
            {
                'id': hub.id,
                'name': hub.name,
                'iconUrl': hub.iconUrl,
                'role': moderator.role.value,
                'permissionLevel': permission_level,
            }
        )

    return accessible_hubs


async def check_hub_permission(
    user_id: str, hub_id: str, required_level: PermissionLevel, db: AsyncSession
) -> bool:
    """Check if user has the required permission level for a hub."""
    actual_level = await get_user_hub_permission(user_id, hub_id, db)
    return actual_level >= required_level
