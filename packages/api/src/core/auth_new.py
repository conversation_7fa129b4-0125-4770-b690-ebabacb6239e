"""
Authentication module for InterChat API - migrated from better-auth Discord OAuth.

This module provides authentication functionality using Discord OAuth2
and JWT tokens, maintaining compatibility with the existing system.
"""

from datetime import datetime, timedelta, timezone
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import User
from .config import settings
from .database import get_database

# Security setup
security = HTTPBearer(auto_error=False)

# JWT settings
ALGORITHM = 'HS256'
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7 days


class TokenData(BaseModel):
    user_id: Optional[str] = None


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token - stub implementation"""
    # TODO: Implement proper JWT token creation
    # For now, return a simple token based on user ID
    user_id = data.get('sub')
    return f'token_{user_id}_{int(datetime.now().timestamp())}'


def verify_access_token(token: str) -> TokenData:
    """Verify JWT access token and return token data - stub implementation"""
    # TODO: Implement proper JWT token verification
    # For now, extract user ID from simple token format
    try:
        parts = token.split('_')
        if len(parts) >= 2 and parts[0] == 'token':
            user_id = parts[1]
            return TokenData(user_id=user_id)
    except Exception:
        pass

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail='Could not validate credentials',
        headers={'WWW-Authenticate': 'Bearer'},
    )


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_database),
) -> User:
    """Dependency to get current authenticated user"""

    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='Authorization required',
            headers={'WWW-Authenticate': 'Bearer'},
        )

    # Verify the token
    token_data = verify_access_token(credentials.credentials)

    # Get user from database
    user = await db.get(User, token_data.user_id)
    if user is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail='User not found')

    return user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_database),
) -> Optional[User]:
    """Dependency to get current user if authenticated, None otherwise"""

    if not credentials:
        return None

    try:
        token_data = verify_access_token(credentials.credentials)
        user = await db.get(User, token_data.user_id)
        return user
    except HTTPException:
        return None
