"""
Server router - migrated from tRPC server router.

Handles Discord server integration including roles, channels, and server management.
Requires Discord bot token for API interactions.
"""

import os
import asyncio
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
import aiohttp
from sqlmodel import select, and_, col
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import Hub, Connection, ServerData, HubModerator, User
from ..core.database import get_database
from ..core.auth import get_current_user
from ..core.permissions import get_user_hub_permission, PermissionLevel

router = APIRouter(prefix='/server', tags=['server'])


# Response Models
class RoleInfo(BaseModel):
    id: str
    name: str
    color: int
    position: int
    mentionable: bool


class ServerRolesResponse(BaseModel):
    roles: List[RoleInfo]


class ServerInfo(BaseModel):
    id: str
    name: str
    icon: Optional[str] = None
    botAdded: bool


class ServerResponse(BaseModel):
    server: ServerInfo


class ChannelInfo(BaseModel):
    id: str
    name: str
    type: int
    parentId: Optional[str] = None
    parentName: Optional[str] = None
    isThread: bool
    isPrivateThread: bool
    position: int


class ServerChannelsResponse(BaseModel):
    channels: List[ChannelInfo]


class ConnectionInfo(BaseModel):
    id: str
    serverId: str
    hubId: str
    channelId: str
    webhookURL: str
    connected: bool
    lastActive: str


class ConnectServerResponse(BaseModel):
    connection: ConnectionInfo


class DisconnectServerResponse(BaseModel):
    connection: ConnectionInfo


# Helper function to get Discord bot token
def get_bot_token() -> str:
    bot_token = os.getenv('DISCORD_BOT_TOKEN')
    if not bot_token:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail='Bot token not configured'
        )
    return bot_token


# Helper function to make Discord API requests
async def discord_api_request(endpoint: str, method: str = 'GET') -> Dict[str, Any]:
    """Make authenticated request to Discord API"""
    bot_token = get_bot_token()

    headers = {'Authorization': f'Bot {bot_token}', 'Content-Type': 'application/json'}

    url = f'https://discord.com/api/v10{endpoint}'

    async with aiohttp.ClientSession() as session:
        try:
            async with session.request(method, url, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f'Discord API error: {response.status} - {error_text}',
                    )
        except aiohttp.ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f'Failed to connect to Discord API: {str(e)}',
            )


@router.get('/{server_id}/roles', response_model=ServerRolesResponse)
async def get_server_roles(
    server_id: str,
    current_user: User = Depends(get_current_user),
):
    """Get Discord server roles"""

    try:
        # Fetch roles from Discord API
        discord_roles = await discord_api_request(f'/guilds/{server_id}/roles')

        # Process and filter roles
        processed_roles = []
        for role in discord_roles:
            # Filter out the @everyone role
            if role.get('name') != '@everyone':
                processed_roles.append(
                    RoleInfo(
                        id=role['id'],
                        name=role['name'],
                        color=role.get('color', 0),
                        position=role.get('position', 0),
                        mentionable=role.get('mentionable', False),
                    )
                )

        # Sort by position (higher position roles first)
        processed_roles.sort(key=lambda x: x.position, reverse=True)

        return ServerRolesResponse(roles=processed_roles)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to fetch Discord roles: {str(e)}',
        )


@router.get('/{server_id}', response_model=ServerResponse)
async def get_server(
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Get server information"""

    try:
        # Fetch server from Discord API
        discord_server = await discord_api_request(f'/guilds/{server_id}')

        # Check if server exists in database
        db_server = await db.get(ServerData, server_id)

        server_info = ServerInfo(
            id=discord_server['id'],
            name=discord_server['name'],
            icon=discord_server.get('icon'),
            botAdded=db_server is not None,
        )

        return ServerResponse(server=server_info)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to fetch server: {str(e)}',
        )


@router.get('/{server_id}/channels', response_model=ServerChannelsResponse)
async def get_server_channels(
    server_id: str,
    hubId: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Get server channels available for connection"""

    try:
        # Fetch channels from Discord API
        discord_channels = await discord_api_request(f'/guilds/{server_id}/channels')

        # Get all existing connections to filter out already connected channels
        existing_connections_query = select(Connection.channelId).where(
            col(Connection.connected) == True
        )  # noqa: E712
        existing_result = await db.exec(existing_connections_query)
        connected_channel_ids = list(existing_result.all())

        # Check if hub+server combination already has a connection
        existing_hub_server_connection = None
        if hubId:
            hub_server_query = select(Connection).where(
                and_(
                    col(Connection.serverId) == server_id,
                    col(Connection.hubId) == hubId,
                    col(Connection.connected) == True,  # noqa: E712
                )
            )
            hub_server_result = await db.exec(hub_server_query)
            existing_hub_server_connection = hub_server_result.first()

        # Process and filter channels
        processed_channels = []

        # Create a lookup for parent channels
        channel_lookup = {ch['id']: ch for ch in discord_channels}

        for channel in discord_channels:
            channel_type = channel.get('type', 0)

            # Check if it's a text channel or thread
            is_text_channel = channel_type == 0  # GUILD_TEXT
            is_thread = channel_type in [
                10,
                11,
                12,
            ]  # PUBLIC_THREAD, PRIVATE_THREAD, ANNOUNCEMENT_THREAD

            is_eligible = is_text_channel or is_thread
            is_not_connected = channel['id'] not in connected_channel_ids
            hub_server_already_connected = bool(hubId and existing_hub_server_connection)

            if is_eligible and is_not_connected and not hub_server_already_connected:
                # Find parent channel for threads
                parent_name = None
                parent_id = channel.get('parent_id')
                if is_thread and parent_id and parent_id in channel_lookup:
                    parent_channel = channel_lookup[parent_id]
                    parent_name = parent_channel.get('name')

                processed_channels.append(
                    ChannelInfo(
                        id=channel['id'],
                        name=channel.get('name', 'unknown-channel'),
                        type=channel_type,
                        parentId=parent_id,
                        parentName=parent_name,
                        isThread=is_thread,
                        isPrivateThread=channel_type == 12,  # PRIVATE_THREAD
                        position=channel.get('position', 0),
                    )
                )

        # Sort channels (threads after their parent channels)
        def sort_key(ch):
            if ch.isThread:
                return (1, ch.parentId or '', ch.name)
            else:
                return (0, ch.position, ch.name)

        processed_channels.sort(key=sort_key)

        return ServerChannelsResponse(channels=processed_channels)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to fetch channels: {str(e)}',
        )


# Request Models
class ConnectServerRequest(BaseModel):
    serverId: str
    hubId: str
    channelId: str


class DisconnectServerRequest(BaseModel):
    connectionId: str


@router.post('/connect', response_model=ConnectServerResponse)
async def connect_server_to_hub(
    connect_data: ConnectServerRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Connect server to hub"""

    # Check if the hub exists and user has permission
    hub = await db.get(Hub, connect_data.hubId)
    if not hub:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Hub not found')

    # Check permissions
    permission_level = await get_user_hub_permission(current_user.id, connect_data.hubId, db)
    if permission_level < PermissionLevel.MODERATOR:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail='You do not have permission to connect servers to this hub',
        )

    # Check if the server is already connected to a hub
    existing_connection_query = select(Connection).where(
        and_(
            col(Connection.serverId) == connect_data.serverId,
            col(Connection.connected) == True,  # noqa: E712
        )
    )
    existing_result = await db.exec(existing_connection_query)
    existing_connection = existing_result.first()

    if existing_connection:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail='Server is already connected to a hub'
        )

    # Create the connection
    from datetime import datetime

    new_connection = Connection(
        serverId=connect_data.serverId,
        hubId=connect_data.hubId,
        channelId=connect_data.channelId,
        webhookURL='',  # Will be set by the bot
        connected=True,
        lastActive=datetime.utcnow(),
    )

    db.add(new_connection)
    await db.commit()
    await db.refresh(new_connection)

    connection_info = ConnectionInfo(
        id=new_connection.id,
        serverId=new_connection.serverId,
        hubId=new_connection.hubId,
        channelId=new_connection.channelId,
        webhookURL=new_connection.webhookURL,
        connected=new_connection.connected,
        lastActive=new_connection.lastActive.isoformat(),
    )

    return ConnectServerResponse(connection=connection_info)


@router.post('/disconnect', response_model=DisconnectServerResponse)
async def disconnect_server_from_hub(
    disconnect_data: DisconnectServerRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Disconnect server from hub"""

    # Get the connection
    connection = await db.get(Connection, disconnect_data.connectionId)
    if not connection:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Connection not found')

    # Check permissions
    permission_level = await get_user_hub_permission(current_user.id, connection.hubId, db)
    if permission_level < PermissionLevel.MODERATOR:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail='You do not have permission to disconnect servers from this hub',
        )

    # Update the connection
    connection.connected = False
    db.add(connection)
    await db.commit()
    await db.refresh(connection)

    connection_info = ConnectionInfo(
        id=connection.id,
        serverId=connection.serverId,
        hubId=connection.hubId,
        channelId=connection.channelId,
        webhookURL=connection.webhookURL,
        connected=connection.connected,
        lastActive=connection.lastActive.isoformat(),
    )

    return DisconnectServerResponse(connection=connection_info)
