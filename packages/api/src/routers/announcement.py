from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlmodel import select, col
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import DevAlerts, User
from ..core.database import get_database
from ..core.auth import get_current_user
from ..core.config import settings

router = APIRouter(prefix='/announcements', tags=['announcements'])


def is_admin(user_id: str) -> bool:
    """Check if user is an admin based on ADMIN_USER_IDS config"""
    admin_user_ids = settings.ADMIN_USER_IDS.split(',') if settings.ADMIN_USER_IDS else []
    return user_id in admin_user_ids


class AnnouncementCreate(BaseModel):
    title: str
    content: str
    thumbnailUrl: Optional[str] = None


class AnnouncementResponse(BaseModel):
    id: str
    title: str
    content: str
    imageUrl: Optional[str] = None
    thumbnailUrl: Optional[str] = None
    createdAt: str
    isUnread: Optional[bool] = None


class AnnouncementsListResponse(BaseModel):
    announcements: List[AnnouncementResponse]
    unreadCount: int


@router.get('/', response_model=AnnouncementsListResponse)
async def get_announcements(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Get all announcements with user-specific read status"""

    # Get all announcements
    announcements_query = select(DevAlerts).order_by(col(DevAlerts.createdAt).desc())
    result = await db.exec(announcements_query)
    announcements = result.all()

    # Get user's last read date
    last_read_date = current_user.inboxLastReadDate or datetime.min

    # Mark which announcements are unread
    announcement_responses = []
    unread_count = 0

    for announcement in announcements:
        is_unread = announcement.createdAt > last_read_date
        if is_unread:
            unread_count += 1

        announcement_responses.append(
            AnnouncementResponse(
                id=announcement.id,
                title=announcement.title,
                content=announcement.content,
                imageUrl=announcement.imageUrl,
                thumbnailUrl=announcement.thumbnailUrl,
                createdAt=announcement.createdAt.isoformat(),
                isUnread=is_unread,
            )
        )

    return AnnouncementsListResponse(
        announcements=announcement_responses,
        unreadCount=unread_count,
    )


@router.post('/mark-all-read')
async def mark_all_announcements_read(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Mark all announcements as read for the current user"""

    # Update user's last read date to now
    current_user.inboxLastReadDate = datetime.now()
    db.add(current_user)
    await db.commit()

    return {'success': True}


@router.get('/admin', response_model=List[AnnouncementResponse])
async def get_all_announcements_for_admin(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Get all announcements for admin (without user-specific read status)"""

    if not is_admin(current_user.id):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail='Admin access required')

    # Get all announcements
    announcements_query = select(DevAlerts).order_by(col(DevAlerts.createdAt).desc())
    result = await db.exec(announcements_query)
    announcements = result.all()

    return [
        AnnouncementResponse(
            id=announcement.id,
            title=announcement.title,
            content=announcement.content,
            imageUrl=announcement.imageUrl,
            thumbnailUrl=announcement.thumbnailUrl,
            createdAt=announcement.createdAt.isoformat(),
        )
        for announcement in announcements
    ]


@router.post('/', response_model=AnnouncementResponse)
async def create_announcement(
    announcement_data: AnnouncementCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Create a new announcement (admin only)"""

    if not is_admin(current_user.id):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail='Admin access required')

    # Create new announcement
    new_announcement = DevAlerts(
        title=announcement_data.title,
        content=announcement_data.content,
        thumbnailUrl=announcement_data.thumbnailUrl,
    )

    db.add(new_announcement)
    await db.commit()
    await db.refresh(new_announcement)

    return AnnouncementResponse(
        id=new_announcement.id,
        title=new_announcement.title,
        content=new_announcement.content,
        imageUrl=new_announcement.imageUrl,
        thumbnailUrl=new_announcement.thumbnailUrl,
        createdAt=new_announcement.createdAt.isoformat(),
    )


@router.delete('/{announcement_id}')
async def delete_announcement(
    announcement_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Delete an announcement (admin only)"""

    if not is_admin(current_user.id):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail='Admin access required')

    # Find and delete the announcement
    announcement = await db.get(DevAlerts, announcement_id)
    if not announcement:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Announcement not found')

    await db.delete(announcement)
    await db.commit()

    return {'success': True}
