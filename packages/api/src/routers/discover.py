"""
Discover router - migrated from tRPC discover router.

Handles hub discovery with filtering by tags, features, language, region,
activity levels, and various sorting options.
"""

from typing import List, Optional
from enum import Enum
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel
from sqlmodel import select, func, and_, or_, col, desc
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import Hub, Tag, HubUpvote, Connection, HubActivityLevel
from ..core.database import get_database
from ..core.auth import get_current_user_optional
from db.models import User

router = APIRouter(prefix='/discover', tags=['discover'])


class DiscoverSort(str, Enum):
    TRENDING = 'trending'
    ACTIVE = 'active'
    NEW = 'new'
    UPVOTED = 'upvoted'


class ActivityLevel(str, Enum):
    LOW = 'LOW'
    MEDIUM = 'MEDIUM'
    HIGH = 'HIGH'


class FeaturesFilter(BaseModel):
    verified: Optional[bool] = None
    partnered: Optional[bool] = None
    nsfw: Optional[bool] = None


class TagInfo(BaseModel):
    name: str
    color: Optional[str] = None


class ActivityMetrics(BaseModel):
    messagesLast24h: int
    activeUsersLast24h: int


class HubCounts(BaseModel):
    upvotes: int
    connections: int
    messages: int


class HubCardResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    iconUrl: Optional[str] = None
    bannerUrl: Optional[str] = None
    weeklyMessageCount: int
    activityLevel: str
    language: Optional[str] = None
    region: Optional[str] = None
    verified: bool
    partnered: bool
    nsfw: bool
    tags: List[TagInfo] = []
    activityMetrics: Optional[ActivityMetrics] = None
    _count: HubCounts
    averageRating: Optional[float] = None
    isUpvoted: bool = False


class DiscoverResponse(BaseModel):
    items: List[HubCardResponse]
    page: int
    pageSize: int
    total: int
    nextPage: Optional[int] = None


@router.get('/', response_model=DiscoverResponse)
async def discover_hubs(
    q: Optional[str] = Query(None, description='Search query'),
    tags: Optional[List[str]] = Query(None, description='Filter by tags'),
    features_verified: Optional[bool] = Query(None, alias='features.verified'),
    features_partnered: Optional[bool] = Query(None, alias='features.partnered'),
    features_nsfw: Optional[bool] = Query(None, alias='features.nsfw'),
    language: Optional[str] = Query(None, description='Filter by language'),
    region: Optional[str] = Query(None, description='Filter by region'),
    activity: Optional[List[ActivityLevel]] = Query(None, description='Filter by activity levels'),
    sort: DiscoverSort = Query(DiscoverSort.TRENDING, description='Sort order'),
    page: int = Query(1, ge=1, description='Page number'),
    pageSize: int = Query(24, ge=1, le=60, description='Items per page'),
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_database),
):
    """Discover hubs with filtering and sorting"""

    # Build base query for public hubs only
    conditions = [col(Hub.private) == False]  # noqa: E712

    # Search query
    if q:
        search_condition = or_(col(Hub.name).ilike(f'%{q}%'), col(Hub.description).ilike(f'%{q}%'))
        conditions.append(search_condition)

    # Language and region filters
    if language:
        conditions.append(col(Hub.language) == language)
    if region:
        conditions.append(col(Hub.region) == region)

    # Activity level filter
    if activity:
        activity_values = [HubActivityLevel(level.value) for level in activity]
        conditions.append(col(Hub.activityLevel).in_(activity_values))

    # Features filters
    if features_verified:
        conditions.append(col(Hub.verified) == True)  # noqa: E712
    if features_partnered:
        conditions.append(col(Hub.partnered) == True)  # noqa: E712

    # NSFW filter - only include if explicitly requested
    if not features_nsfw:
        conditions.append(col(Hub.nsfw) == False)  # noqa: E712

    # Tags filter - hub must have ALL selected tags (AND logic)
    if tags:
        for tag in tags:
            # This is a simplified approach - in a real implementation you'd need proper joins
            tag_subquery = (
                select(Hub.id).join(Tag).where(and_(Tag.name == tag, Tag.hubId == Hub.id))
            )
            conditions.append(col(Hub.id).in_(tag_subquery))

    # Build the main query
    base_query = select(Hub).where(and_(*conditions))

    # Apply sorting
    if sort == DiscoverSort.ACTIVE:
        base_query = base_query.order_by(desc(Hub.weeklyMessageCount), desc(Hub.id))
    elif sort == DiscoverSort.NEW:
        base_query = base_query.order_by(desc(Hub.createdAt), desc(Hub.id))
    elif sort == DiscoverSort.UPVOTED:
        # This would need a proper join with upvotes count
        base_query = base_query.order_by(desc(Hub.createdAt), desc(Hub.id))  # Simplified
    else:  # TRENDING (default)
        # Trending uses a complex algorithm, simplified here
        base_query = base_query.order_by(
            desc(Hub.weeklyMessageCount), desc(Hub.createdAt), desc(Hub.id)
        )

    # Get total count
    count_query = select(func.count(col(Hub.id))).where(and_(*conditions))
    total_result = await db.exec(count_query)
    total = total_result.first() or 0

    # Apply pagination
    skip = (page - 1) * pageSize
    paginated_query = base_query.offset(skip).limit(pageSize)

    # Execute query
    result = await db.exec(paginated_query)
    hubs = result.all()

    # Format response
    hub_responses = []
    for hub in hubs:
        # Get upvote status for current user
        is_upvoted = False
        if current_user:
            upvote_query = select(HubUpvote).where(
                and_(HubUpvote.hubId == hub.id, HubUpvote.userId == current_user.id)
            )
            upvote_result = await db.exec(upvote_query)
            is_upvoted = upvote_result.first() is not None

        # Get connection count
        connection_count_query = select(func.count(col(Connection.id))).where(
            and_(Connection.hubId == hub.id, Connection.connected == True)  # noqa: E712
        )
        connection_count_result = await db.exec(connection_count_query)
        connection_count = connection_count_result.first() or 0

        # Get upvote count
        upvote_count_query = select(func.count(col(HubUpvote.id))).where(HubUpvote.hubId == hub.id)
        upvote_count_result = await db.exec(upvote_count_query)
        upvote_count = upvote_count_result.first() or 0

        # Get tags (simplified - would need proper relationship loading)
        tags_info = []  # Would need to implement tag loading

        hub_responses.append(
            HubCardResponse(
                id=hub.id,
                name=hub.name,
                description=hub.description,
                iconUrl=hub.iconUrl,
                bannerUrl=hub.bannerUrl,
                weeklyMessageCount=hub.weeklyMessageCount or 0,
                activityLevel=hub.activityLevel.value if hub.activityLevel else 'LOW',
                language=hub.language,
                region=hub.region,
                verified=hub.verified,
                partnered=hub.partnered,
                nsfw=hub.nsfw,
                tags=tags_info,
                activityMetrics=None,  # Would need to implement activity metrics
                _count=HubCounts(
                    upvotes=upvote_count,
                    connections=connection_count,
                    messages=hub.weeklyMessageCount or 0,
                ),
                averageRating=None,  # Would need to implement rating calculation
                isUpvoted=is_upvoted,
            )
        )

    # Calculate next page
    has_more = page * pageSize < total
    next_page = page + 1 if has_more else None

    return DiscoverResponse(
        items=hub_responses, page=page, pageSize=pageSize, total=total, nextPage=next_page
    )
