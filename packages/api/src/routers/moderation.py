from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime, timedelta, timezone
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from pydantic import BaseModel, Field, model_validator
from sqlmodel import select, func, and_, col, desc
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import (
    Infraction,
    InfractionType,
    InfractionStatus,
    ReportStatus,
    User,
    Hub,
    HubModerator,
)
from ..core.database import get_database
from ..core.auth import get_current_user
from ..core.permissions import get_user_hub_permission, PermissionLevel

router = APIRouter(prefix='/moderation', tags=['moderation'])


class TargetType(str, Enum):
    USER = 'user'
    SERVER = 'server'


# Request Models
class CreateInfractionRequest(BaseModel):
    hubId: str
    type: InfractionType
    reason: str = Field(min_length=3, max_length=500)
    expiresAt: Optional[str] = None
    userId: Optional[str] = None
    serverId: Optional[str] = None
    serverName: Optional[str] = None

    @model_validator(mode='after')
    def check_user_or_server(self):
        if not self.userId and not (self.serverId and self.serverName):
            raise ValueError('Either userId or serverId with serverName must be provided')
        return self


class AddToBlacklistRequest(BaseModel):
    type: TargetType
    id: str
    reason: str = Field(min_length=1, max_length=1000)
    hubId: str
    duration: Optional[int] = None  # Duration in seconds


class UpdateReportStatusRequest(BaseModel):
    reportId: str
    status: ReportStatus
    resolution: Optional[str] = None


class CreateInfractionFromReportRequest(BaseModel):
    reportId: str
    type: InfractionType
    reason: str = Field(min_length=1, max_length=1000)
    duration: Optional[int] = None  # Duration in seconds
    targetType: TargetType


class UpdateInfractionRequest(BaseModel):
    status: Optional[InfractionStatus] = None
    reason: Optional[str] = Field(None, min_length=3, max_length=500)
    expiresAt: Optional[str] = None  # ISO string or null


# Response Models
class UserInfo(BaseModel):
    id: str
    name: str
    image: Optional[str] = None


class HubInfo(BaseModel):
    id: str
    name: str
    iconUrl: Optional[str] = None


class InfractionResponse(BaseModel):
    id: str
    hubId: str
    type: str
    reason: str
    status: str
    createdAt: str
    updatedAt: str
    expiresAt: Optional[str] = None
    userId: Optional[str] = None
    serverId: Optional[str] = None
    serverName: Optional[str] = None
    moderatorId: str
    hub: HubInfo
    moderator: UserInfo
    user: Optional[UserInfo] = None


class InfractionListResponse(BaseModel):
    infractions: List[InfractionResponse]
    total: int
    page: int
    limit: int
    totalPages: int


class MessageData(BaseModel):
    id: str
    content: Optional[str] = None
    imageUrl: Optional[str] = None
    channelId: str
    guildId: str
    authorId: str
    createdAt: str
    reactions: Optional[Dict[str, Any]] = None
    referredMessageId: Optional[str] = None


class ServerData(BaseModel):
    id: str
    name: str
    iconUrl: Optional[str] = None
    inviteCode: Optional[str] = None


class ReportResponse(BaseModel):
    id: str
    hubId: str
    reason: str
    status: str
    createdAt: str
    updatedAt: str
    reporterId: str
    reportedUserId: Optional[str] = None
    reportedServerId: Optional[str] = None
    messageId: Optional[str] = None
    handlerId: Optional[str] = None
    handledAt: Optional[str] = None
    resolution: Optional[str] = None
    hub: HubInfo
    reporter: UserInfo
    reportedUser: Optional[UserInfo] = None
    handler: Optional[UserInfo] = None
    messageData: Optional[MessageData] = None
    serverData: Optional[ServerData] = None


class ReportListResponse(BaseModel):
    reports: List[ReportResponse]
    total: int
    page: int
    limit: int
    totalPages: int


class BlacklistResponse(BaseModel):
    blacklist: List[InfractionResponse]
    total: int
    page: int
    limit: int
    totalPages: int


# Endpoints
@router.get('/infractions', response_model=InfractionListResponse)
async def get_infractions(
    hubId: Optional[str] = Query(None),
    type: Optional[InfractionType] = Query(None),
    status: Optional[InfractionStatus] = Query(None),
    targetType: Optional[TargetType] = Query(None),
    userId: Optional[str] = Query(None),
    serverId: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Get infractions with pagination and filtering"""

    skip = (page - 1) * limit

    # Determine accessible hubs if hubId not provided
    effective_hub_ids = []
    if not hubId:
        # Get hubs where user is owner or moderator
        owner_hubs_query = select(Hub.id).where(col(Hub.ownerId) == current_user.id)
        owner_result = await db.exec(owner_hubs_query)
        owner_hub_ids = list(owner_result.all())

        moderator_hubs_query = select(HubModerator.hubId).where(
            col(HubModerator.userId) == current_user.id
        )
        moderator_result = await db.exec(moderator_hubs_query)
        moderator_hub_ids = list(moderator_result.all())

        effective_hub_ids = list(set(owner_hub_ids + moderator_hub_ids))

        if not effective_hub_ids:
            return InfractionListResponse(
                infractions=[], total=0, page=page, limit=limit, totalPages=0
            )
    else:
        # Ensure user has permissions on the specific hub
        permission_level = await get_user_hub_permission(current_user.id, hubId, db)
        if permission_level < PermissionLevel.MODERATOR:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
            )
        effective_hub_ids = [hubId]

    # Build where conditions
    conditions = [col(Infraction.hubId).in_(effective_hub_ids)]

    if type:
        conditions.append(col(Infraction.type).is_(type))
    if status:
        conditions.append(col(Infraction.status).is_(status))
    if targetType == TargetType.USER:
        conditions.append(col(Infraction.userId).is_not(None))
    if targetType == TargetType.SERVER:
        conditions.append(col(Infraction.serverId).is_not(None))
    if userId:
        conditions.append(col(Infraction.userId).is_(userId))
    if serverId:
        conditions.append(col(Infraction.serverId).is_(serverId))

    # Get total count
    count_query = select(func.count(col(Infraction.id))).where(and_(*conditions))
    total_result = await db.exec(count_query)
    total = total_result.first() or 0

    # Get infractions
    query = (
        select(Infraction)
        .where(and_(*conditions))
        .order_by(desc(Infraction.createdAt))
        .offset(skip)
        .limit(limit)
    )
    result = await db.exec(query)
    infractions = result.all()

    # Format response (simplified - would need proper relationship loading)
    infraction_responses = []
    for infraction in infractions:
        # Get related data
        hub = await db.get(Hub, infraction.hubId)
        moderator = await db.get(User, infraction.moderatorId)
        user = await db.get(User, infraction.userId) if infraction.userId else None

        infraction_responses.append(
            InfractionResponse(
                id=infraction.id,
                hubId=infraction.hubId,
                type=infraction.type.value,
                reason=infraction.reason,
                status=infraction.status.value,
                createdAt=infraction.createdAt.isoformat(),
                updatedAt=infraction.updatedAt.isoformat(),
                expiresAt=infraction.expiresAt.isoformat() if infraction.expiresAt else None,
                userId=infraction.userId,
                serverId=infraction.serverId,
                serverName=infraction.serverName,
                moderatorId=infraction.moderatorId,
                hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
                if hub
                else HubInfo(id=infraction.hubId, name='Unknown'),
                moderator=UserInfo(id=moderator.id, name=moderator.name, image=moderator.image)
                if moderator
                else UserInfo(id=infraction.moderatorId, name='Unknown'),
                user=UserInfo(id=user.id, name=user.name, image=user.image) if user else None,
            )
        )

    return InfractionListResponse(
        infractions=infraction_responses,
        total=total,
        page=page,
        limit=limit,
        totalPages=(total + limit - 1) // limit,
    )


@router.post('/infractions', response_model=Dict[str, InfractionResponse])
async def create_infraction(
    infraction_data: CreateInfractionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Create an infraction"""

    # Validate that either userId or serverId+serverName is provided
    if not infraction_data.userId and not (infraction_data.serverId and infraction_data.serverName):
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail='Either userId or serverId with serverName must be provided',
        )

    # Check permissions
    permission_level = await get_user_hub_permission(current_user.id, infraction_data.hubId, db)
    if permission_level < PermissionLevel.MODERATOR:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    # Parse expiration date if provided
    expires_at = None
    if infraction_data.expiresAt:
        try:
            expires_at = datetime.fromisoformat(infraction_data.expiresAt.replace('Z', '+00:00'))
        except ValueError:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail='Invalid expiresAt format. Use ISO format.',
            )

    # Create the infraction
    new_infraction = Infraction(
        hubId=infraction_data.hubId,
        type=infraction_data.type,
        reason=infraction_data.reason,
        moderatorId=current_user.id,
        expiresAt=expires_at,
        userId=infraction_data.userId,
        serverId=infraction_data.serverId,
        serverName=infraction_data.serverName,
        status=InfractionStatus.ACTIVE,
    )

    db.add(new_infraction)
    await db.commit()
    await db.refresh(new_infraction)

    # Get related data for response
    hub = await db.get(Hub, new_infraction.hubId)
    moderator = await db.get(User, new_infraction.moderatorId)
    user = await db.get(User, new_infraction.userId) if new_infraction.userId else None

    infraction_response = InfractionResponse(
        id=new_infraction.id,
        hubId=new_infraction.hubId,
        type=new_infraction.type.value,
        reason=new_infraction.reason,
        status=new_infraction.status.value,
        createdAt=new_infraction.createdAt.isoformat(),
        updatedAt=new_infraction.updatedAt.isoformat(),
        expiresAt=new_infraction.expiresAt.isoformat() if new_infraction.expiresAt else None,
        userId=new_infraction.userId,
        serverId=new_infraction.serverId,
        serverName=new_infraction.serverName,
        moderatorId=new_infraction.moderatorId,
        hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
        if hub
        else HubInfo(id=new_infraction.hubId, name='Unknown'),
        moderator=UserInfo(id=moderator.id, name=moderator.name, image=moderator.image)
        if moderator
        else UserInfo(id=new_infraction.moderatorId, name='Unknown'),
        user=UserInfo(id=user.id, name=user.name, image=user.image) if user else None,
    )

    return {'infraction': infraction_response}


@router.post('/blacklist', response_model=Dict[str, InfractionResponse])
async def add_to_blacklist(
    blacklist_data: AddToBlacklistRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Add user or server to blacklist"""

    # Validate Discord ID format
    import re

    if not re.match(r'^\d+$', blacklist_data.id):
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f'Invalid Discord {blacklist_data.type.value} ID format',
        )

    # Check permissions
    permission_level = await get_user_hub_permission(current_user.id, blacklist_data.hubId, db)
    if permission_level < PermissionLevel.MODERATOR:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    # Check if already blacklisted
    existing_query = select(Infraction).where(
        and_(
            col(Infraction.hubId) == blacklist_data.hubId,
            col(Infraction.type) == InfractionType.BLACKLIST,
            col(Infraction.status) == InfractionStatus.ACTIVE,
            col(Infraction.userId) == blacklist_data.id
            if blacklist_data.type == TargetType.USER
            else col(Infraction.serverId) == blacklist_data.id,
        )
    )
    existing_result = await db.exec(existing_query)
    existing_infraction = existing_result.first()

    if existing_infraction:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f'This {blacklist_data.type.value} is already blacklisted',
        )

    # Calculate expiration
    expires_at = None
    if blacklist_data.duration:
        expires_at = datetime.now(timezone.utc) + timedelta(seconds=blacklist_data.duration)

    # Create blacklist infraction
    new_infraction = Infraction(
        hubId=blacklist_data.hubId,
        moderatorId=current_user.id,
        type=InfractionType.BLACKLIST,
        status=InfractionStatus.ACTIVE,
        reason=blacklist_data.reason,
        expiresAt=expires_at,
        userId=blacklist_data.id if blacklist_data.type == TargetType.USER else None,
        serverId=blacklist_data.id if blacklist_data.type == TargetType.SERVER else None,
        serverName=blacklist_data.id if blacklist_data.type == TargetType.SERVER else None,
    )

    db.add(new_infraction)
    await db.commit()
    await db.refresh(new_infraction)

    # Get related data for response
    hub = await db.get(Hub, new_infraction.hubId)
    moderator = await db.get(User, new_infraction.moderatorId)
    user = await db.get(User, new_infraction.userId) if new_infraction.userId else None

    infraction_response = InfractionResponse(
        id=new_infraction.id,
        hubId=new_infraction.hubId,
        type=new_infraction.type.value,
        reason=new_infraction.reason,
        status=new_infraction.status.value,
        createdAt=new_infraction.createdAt.isoformat(),
        updatedAt=new_infraction.updatedAt.isoformat(),
        expiresAt=new_infraction.expiresAt.isoformat() if new_infraction.expiresAt else None,
        userId=new_infraction.userId,
        serverId=new_infraction.serverId,
        serverName=new_infraction.serverName,
        moderatorId=new_infraction.moderatorId,
        hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
        if hub
        else HubInfo(id=new_infraction.hubId, name='Unknown'),
        moderator=UserInfo(id=moderator.id, name=moderator.name, image=moderator.image)
        if moderator
        else UserInfo(id=new_infraction.moderatorId, name='Unknown'),
        user=UserInfo(id=user.id, name=user.name, image=user.image) if user else None,
    )

    return {'infraction': infraction_response}


@router.delete('/blacklist/{infraction_id}', response_model=Dict[str, Any])
async def remove_from_blacklist(
    infraction_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Remove from blacklist (revoke infraction)"""

    # Get the infraction
    infraction = await db.get(Infraction, infraction_id)
    if not infraction:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND, detail='Infraction not found'
        )

    # Check if it's a blacklist infraction
    if infraction.type != InfractionType.BLACKLIST:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail='This is not a blacklist infraction',
        )

    # Check permissions
    permission_level = await get_user_hub_permission(current_user.id, infraction.hubId, db)
    if permission_level < PermissionLevel.MODERATOR:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    # Update the infraction status to revoked
    infraction.status = InfractionStatus.REVOKED
    db.add(infraction)
    await db.commit()
    await db.refresh(infraction)

    return {'success': True, 'infraction': infraction}


@router.get('/blacklist', response_model=BlacklistResponse)
async def get_blacklist(
    hubId: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Get blacklist entries (infractions of type BLACKLIST)"""

    skip = (page - 1) * limit

    # Build where conditions
    conditions = [
        col(Infraction.type) == InfractionType.BLACKLIST,
        col(Infraction.status) == InfractionStatus.ACTIVE,
    ]

    if hubId:
        # Check permissions for specific hub
        permission_level = await get_user_hub_permission(current_user.id, hubId, db)
        if permission_level < PermissionLevel.MODERATOR:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
            )
        conditions.append(col(Infraction.hubId) == hubId)
    else:
        # Get accessible hubs
        owner_hubs_query = select(Hub.id).where(col(Hub.ownerId) == current_user.id)
        owner_result = await db.exec(owner_hubs_query)
        owner_hub_ids = list(owner_result.all())

        moderator_hubs_query = select(HubModerator.hubId).where(
            col(HubModerator.userId) == current_user.id
        )
        moderator_result = await db.exec(moderator_hubs_query)
        moderator_hub_ids = list(moderator_result.all())

        accessible_hub_ids = list(set(owner_hub_ids + moderator_hub_ids))
        if not accessible_hub_ids:
            return BlacklistResponse(blacklist=[], total=0, page=page, limit=limit, totalPages=0)
        conditions.append(col(Infraction.hubId).in_(accessible_hub_ids))

    # Get total count
    count_query = select(func.count(col(Infraction.id))).where(and_(*conditions))
    total_result = await db.exec(count_query)
    total = total_result.first() or 0

    # Get blacklist entries
    query = (
        select(Infraction)
        .where(and_(*conditions))
        .order_by(desc(Infraction.createdAt))
        .offset(skip)
        .limit(limit)
    )
    result = await db.exec(query)
    infractions = result.all()

    # Format response
    blacklist_responses = []
    for infraction in infractions:
        # Get related data
        hub = await db.get(Hub, infraction.hubId)
        moderator = await db.get(User, infraction.moderatorId)
        user = await db.get(User, infraction.userId) if infraction.userId else None

        blacklist_responses.append(
            InfractionResponse(
                id=infraction.id,
                hubId=infraction.hubId,
                type=infraction.type.value,
                reason=infraction.reason,
                status=infraction.status.value,
                createdAt=infraction.createdAt.isoformat(),
                updatedAt=infraction.updatedAt.isoformat(),
                expiresAt=infraction.expiresAt.isoformat() if infraction.expiresAt else None,
                userId=infraction.userId,
                serverId=infraction.serverId,
                serverName=infraction.serverName,
                moderatorId=infraction.moderatorId,
                hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
                if hub
                else HubInfo(id=infraction.hubId, name='Unknown'),
                moderator=UserInfo(id=moderator.id, name=moderator.name, image=moderator.image)
                if moderator
                else UserInfo(id=infraction.moderatorId, name='Unknown'),
                user=UserInfo(id=user.id, name=user.name, image=user.image) if user else None,
            )
        )

    return BlacklistResponse(
        blacklist=blacklist_responses,
        total=total,
        page=page,
        limit=limit,
        totalPages=(total + limit - 1) // limit,
    )
