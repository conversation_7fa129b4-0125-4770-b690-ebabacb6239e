from typing import Dict
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import User
from ..core.database import get_database
from ..core.auth import create_access_token, get_current_user

router = APIRouter(prefix='/auth', tags=['authentication'])


class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user: Dict


class DiscordOAuthRequest(BaseModel):
    code: str


@router.post('/discord/callback', response_model=LoginResponse)
async def discord_oauth_callback(
    oauth_data: DiscordOAuthRequest, db: AsyncSession = Depends(get_database)
):
    """Handle Discord OAuth callback"""

    # TODO: Implement proper Discord OAuth flow
    # For now, this is a stub implementation

    # In a real implementation, you would:
    # 1. Exchange the code for an access token
    # 2. Fetch user info from Discord
    # 3. Create or update user in database
    # 4. Return JWT token

    # Stub implementation - create a test user
    fake_user_id = f'discord_{oauth_data.code}'

    # Check if user exists, create if not
    user = await db.get(User, fake_user_id)
    if not user:
        user = User(id=fake_user_id, name=f'TestUser_{oauth_data.code[:8]}', image=None)
        db.add(user)
        await db.commit()
        await db.refresh(user)

    # Create access token
    access_token = create_access_token(data={'sub': user.id})

    return LoginResponse(
        access_token=access_token,
        token_type='bearer',
        user={'id': user.id, 'name': user.name, 'image': user.image},
    )


@router.get('/me')
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return {
        'id': current_user.id,
        'name': current_user.name,
        'image': current_user.image,
        'email': current_user.email,
    }


@router.post('/logout')
async def logout():
    """Logout user (client-side token invalidation)"""
    return {'message': 'Logged out successfully'}
