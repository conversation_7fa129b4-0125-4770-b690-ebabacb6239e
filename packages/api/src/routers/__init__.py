from .hubs import router as hub_router
from .user import router as user_router
from .connection import router as connection_router
from .auth import router as auth_router

# For now, we'll implement stub routers for the remaining endpoints
# These will be expanded based on the tRPC implementation
from fastapi import APIRouter

# Stub routers - to be implemented
announcement_router = APIRouter(prefix='/announcement', tags=['announcements'])
appeal_router = APIRouter(prefix='/appeal', tags=['appeals'])
discover_router = APIRouter(prefix='/discover', tags=['discover'])
moderation_router = APIRouter(prefix='/moderation', tags=['moderation'])
server_router = APIRouter(prefix='/server', tags=['servers'])
tags_router = APIRouter(prefix='/tags', tags=['tags'])

# Export all routers
__all__ = [
    'auth_router',
    'hub_router',
    'user_router',
    'connection_router',
    'announcement_router',
    'appeal_router',
    'discover_router',
    'moderation_router',
    'server_router',
    'tags_router',
]
