from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status as http_status, Query
from pydantic import BaseModel
from sqlmodel import select, func, and_, col
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import Appeal, Infraction, User, Hub, HubModerator, AppealStatus, InfractionStatus
from ..core.database import get_database
from ..core.auth import get_current_user
from ..core.permissions import get_user_hub_permission, PermissionLevel

router = APIRouter(prefix='/appeals', tags=['appeals'])


class AppealCreate(BaseModel):
    infractionId: str
    reason: str


class AppealStatusUpdate(BaseModel):
    status: AppealStatus


class UserInfo(BaseModel):
    id: str
    name: Optional[str] = None
    image: Optional[str] = None


class HubInfo(BaseModel):
    id: str
    name: str
    iconUrl: str


class InfractionInfo(BaseModel):
    id: str
    hubId: str
    type: str
    reason: str
    createdAt: str
    hub: HubInfo
    user: Optional[UserInfo] = None
    moderator: UserInfo


class AppealResponse(BaseModel):
    id: str
    infractionId: str
    userId: str
    reason: str
    status: str
    createdAt: str
    updatedAt: str
    user: UserInfo
    infraction: InfractionInfo


class AppealListResponse(BaseModel):
    appeals: List[AppealResponse]
    total: int
    page: int
    limit: int
    totalPages: int


@router.get('/', response_model=AppealListResponse)
async def list_appeals(
    status: Optional[str] = Query(None, description='Filter by appeal status'),
    user_id: Optional[str] = Query(None, alias='userId', description='Filter by user ID'),
    infraction_id: Optional[str] = Query(
        None, alias='infractionId', description='Filter by infraction ID'
    ),
    hub_id: Optional[str] = Query(None, alias='hubId', description='Filter by hub ID'),
    my_appeals: bool = Query(
        False, alias='myAppeals', description="Show only current user's appeals"
    ),
    page: int = Query(1, ge=1, description='Page number'),
    limit: int = Query(10, ge=1, le=100, description='Items per page'),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """List appeals with filtering and pagination"""

    skip = (page - 1) * limit

    # Build base query
    query = select(Appeal)
    count_query = select(func.count(col(Appeal.id)))

    # Apply filters
    conditions = []

    if status:
        try:
            appeal_status = AppealStatus(status)
            conditions.append(Appeal.status == appeal_status)
        except ValueError:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST, detail=f'Invalid status: {status}'
            )

    if infraction_id:
        conditions.append(Appeal.infractionId == infraction_id)

    if my_appeals:
        # Only current user's appeals
        conditions.append(Appeal.userId == current_user.id)
    else:
        # Moderation view: restrict to hubs the user can access
        # Get hubs where user is owner
        owned_hubs_query = select(Hub.id).where(Hub.ownerId == current_user.id)
        owned_hubs_result = await db.exec(owned_hubs_query)
        owned_hub_ids = list(owned_hubs_result.all())

        # Get hubs where user is a moderator/manager
        moderated_hubs_query = select(HubModerator.hubId).where(
            HubModerator.userId == current_user.id
        )
        moderated_hubs_result = await db.exec(moderated_hubs_query)
        moderated_hub_ids = list(moderated_hubs_result.all())

        accessible_hub_ids = list(set(owned_hub_ids + moderated_hub_ids))

        if not accessible_hub_ids:
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN, detail='No accessible hubs found'
            )

        # For moderation view, we need to filter by accessible hubs
        # We'll do this by getting appeals for infractions in accessible hubs
        accessible_infraction_query = select(Infraction.id).where(
            col(Infraction.hubId).in_(accessible_hub_ids)
        )
        accessible_infraction_result = await db.exec(accessible_infraction_query)
        accessible_infraction_ids = list(accessible_infraction_result.all())

        if not accessible_infraction_ids:
            # No accessible infractions, return empty result
            return AppealListResponse(
                appeals=[],
                total=0,
                page=page,
                limit=limit,
                totalPages=0,
            )

        conditions.append(col(Appeal.infractionId).in_(accessible_infraction_ids))

        if user_id:
            conditions.append(Appeal.userId == user_id)

        if hub_id:
            if hub_id not in accessible_hub_ids:
                raise HTTPException(
                    status_code=http_status.HTTP_403_FORBIDDEN, detail='Access denied to this hub'
                )
            # Filter infractions by hub
            hub_infraction_query = select(Infraction.id).where(
                and_(
                    col(Infraction.hubId) == hub_id,
                    col(Infraction.id).in_(accessible_infraction_ids),
                )
            )
            hub_infraction_result = await db.exec(hub_infraction_query)
            hub_infraction_ids = list(hub_infraction_result.all())
            conditions.append(col(Appeal.infractionId).in_(hub_infraction_ids))

    # Apply all conditions
    if conditions:
        query = query.where(and_(*conditions))
        count_query = count_query.where(and_(*conditions))

    # Get total count
    total_result = await db.exec(count_query)
    total = total_result.first() or 0

    # Apply pagination and ordering
    query = query.order_by(col(Appeal.createdAt).desc()).offset(skip).limit(limit)

    # Execute query
    result = await db.exec(query)
    appeals = result.all()

    # Format response (simplified - would need proper joins for full data)
    appeal_responses = []
    for appeal in appeals:
        # Get related data
        infraction = await db.get(Infraction, appeal.infractionId)
        user = await db.get(User, appeal.userId)

        if infraction:
            hub = await db.get(Hub, infraction.hubId)
            moderator = await db.get(User, infraction.moderatorId)
            infraction_user = await db.get(User, infraction.userId) if infraction.userId else None

            appeal_responses.append(
                AppealResponse(
                    id=appeal.id,
                    infractionId=appeal.infractionId,
                    userId=appeal.userId,
                    reason=appeal.reason,
                    status=appeal.status.value,
                    createdAt=appeal.createdAt.isoformat(),
                    updatedAt=appeal.updatedAt.isoformat(),
                    user=UserInfo(id=user.id, name=user.name, image=user.image)
                    if user
                    else UserInfo(id=appeal.userId),
                    infraction=InfractionInfo(
                        id=infraction.id,
                        hubId=infraction.hubId,
                        type=infraction.type.value,
                        reason=infraction.reason,
                        createdAt=infraction.createdAt.isoformat(),
                        hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
                        if hub
                        else HubInfo(id=infraction.hubId, name='Unknown', iconUrl=''),
                        user=UserInfo(
                            id=infraction_user.id,
                            name=infraction_user.name,
                            image=infraction_user.image,
                        )
                        if infraction_user
                        else None,
                        moderator=UserInfo(
                            id=moderator.id, name=moderator.name, image=moderator.image
                        )
                        if moderator
                        else UserInfo(id=infraction.moderatorId),
                    ),
                )
            )

    return AppealListResponse(
        appeals=appeal_responses,
        total=total,
        page=page,
        limit=limit,
        totalPages=(total + limit - 1) // limit,
    )


@router.get('/{appeal_id}', response_model=AppealResponse)
async def get_appeal_by_id(
    appeal_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Get a specific appeal with permission check"""

    # Get the appeal with related data
    appeal = await db.get(Appeal, appeal_id)
    if not appeal:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail='Appeal not found')

    # Get infraction to check permissions
    infraction = await db.get(Infraction, appeal.infractionId)
    if not infraction:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND, detail='Associated infraction not found'
        )

    # Permission check: user owns the appeal OR can moderate the hub
    is_owner = appeal.userId == current_user.id
    permission_level = await get_user_hub_permission(current_user.id, infraction.hubId, db)
    can_moderate = permission_level >= PermissionLevel.MODERATOR

    if not is_owner and not can_moderate:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail='Access denied')

    # Get related data for response
    user = await db.get(User, appeal.userId)
    hub = await db.get(Hub, infraction.hubId)
    moderator = await db.get(User, infraction.moderatorId)
    infraction_user = await db.get(User, infraction.userId) if infraction.userId else None

    return AppealResponse(
        id=appeal.id,
        infractionId=appeal.infractionId,
        userId=appeal.userId,
        reason=appeal.reason,
        status=appeal.status.value,
        createdAt=appeal.createdAt.isoformat(),
        updatedAt=appeal.updatedAt.isoformat(),
        user=UserInfo(id=user.id, name=user.name, image=user.image)
        if user
        else UserInfo(id=appeal.userId),
        infraction=InfractionInfo(
            id=infraction.id,
            hubId=infraction.hubId,
            type=infraction.type.value,
            reason=infraction.reason,
            createdAt=infraction.createdAt.isoformat(),
            hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
            if hub
            else HubInfo(id=infraction.hubId, name='Unknown', iconUrl=''),
            user=UserInfo(
                id=infraction_user.id, name=infraction_user.name, image=infraction_user.image
            )
            if infraction_user
            else None,
            moderator=UserInfo(id=moderator.id, name=moderator.name, image=moderator.image)
            if moderator
            else UserInfo(id=infraction.moderatorId),
        ),
    )


@router.post('/', response_model=AppealResponse)
async def create_appeal(
    appeal_data: AppealCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Create a new appeal"""

    # Get the infraction
    infraction = await db.get(Infraction, appeal_data.infractionId)
    if not infraction:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND, detail='Infraction not found'
        )

    # Only the user that received the infraction may appeal
    if infraction.userId and infraction.userId != current_user.id:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN,
            detail='You can only appeal your own infractions',
        )

    # Check if infraction is already appealed
    if infraction.status == InfractionStatus.APPEALED:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail='This infraction has already been appealed',
        )

    # Check for existing pending appeal
    existing_appeal_query = select(Appeal).where(
        and_(Appeal.infractionId == appeal_data.infractionId, Appeal.status == AppealStatus.PENDING)
    )
    result = await db.exec(existing_appeal_query)
    existing_appeal = result.first()

    if existing_appeal:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail='There is already a pending appeal for this infraction',
        )

    # Create the appeal
    new_appeal = Appeal(
        infractionId=appeal_data.infractionId,
        userId=current_user.id,
        reason=appeal_data.reason,
        status=AppealStatus.PENDING,
    )

    db.add(new_appeal)
    await db.commit()
    await db.refresh(new_appeal)

    # Get related data for response
    hub = await db.get(Hub, infraction.hubId)
    moderator = await db.get(User, infraction.moderatorId)
    infraction_user = await db.get(User, infraction.userId) if infraction.userId else None

    return AppealResponse(
        id=new_appeal.id,
        infractionId=new_appeal.infractionId,
        userId=new_appeal.userId,
        reason=new_appeal.reason,
        status=new_appeal.status.value,
        createdAt=new_appeal.createdAt.isoformat(),
        updatedAt=new_appeal.updatedAt.isoformat(),
        user=UserInfo(id=current_user.id, name=current_user.name, image=current_user.image),
        infraction=InfractionInfo(
            id=infraction.id,
            hubId=infraction.hubId,
            type=infraction.type.value,
            reason=infraction.reason,
            createdAt=infraction.createdAt.isoformat(),
            hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
            if hub
            else HubInfo(id=infraction.hubId, name='Unknown', iconUrl=''),
            user=UserInfo(
                id=infraction_user.id, name=infraction_user.name, image=infraction_user.image
            )
            if infraction_user
            else None,
            moderator=UserInfo(id=moderator.id, name=moderator.name, image=moderator.image)
            if moderator
            else UserInfo(id=infraction.moderatorId),
        ),
    )


@router.put('/{appeal_id}/status', response_model=AppealResponse)
async def update_appeal_status(
    appeal_id: str,
    status_update: AppealStatusUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Update an appeal status (moderator+)"""

    # Validate status
    if status_update.status not in ['ACCEPTED', 'REJECTED']:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail="Status must be 'ACCEPTED' or 'REJECTED'",
        )

    # Get the appeal
    appeal = await db.get(Appeal, appeal_id)
    if not appeal:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail='Appeal not found')

    # Get infraction for permission check
    infraction = await db.get(Infraction, appeal.infractionId)
    if not infraction:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND, detail='Associated infraction not found'
        )

    # Permission check - must be moderator or higher
    permission_level = await get_user_hub_permission(current_user.id, infraction.hubId, db)
    if permission_level < PermissionLevel.MODERATOR:
        raise HTTPException(
            status_code=http_status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    # Check if status is already the same
    new_status = AppealStatus(status_update.status)
    if appeal.status == new_status:
        # Return current appeal without changes
        pass
    else:
        # Update appeal status
        appeal.status = new_status
        db.add(appeal)

        # If accepted, update infraction status
        if new_status == AppealStatus.ACCEPTED:
            infraction.status = InfractionStatus.APPEALED
            db.add(infraction)

        await db.commit()
        await db.refresh(appeal)

    # Get related data for response
    user = await db.get(User, appeal.userId)
    hub = await db.get(Hub, infraction.hubId)
    moderator = await db.get(User, infraction.moderatorId)
    infraction_user = await db.get(User, infraction.userId) if infraction.userId else None

    return AppealResponse(
        id=appeal.id,
        infractionId=appeal.infractionId,
        userId=appeal.userId,
        reason=appeal.reason,
        status=appeal.status.value,
        createdAt=appeal.createdAt.isoformat(),
        updatedAt=appeal.updatedAt.isoformat(),
        user=UserInfo(id=user.id, name=user.name, image=user.image)
        if user
        else UserInfo(id=appeal.userId),
        infraction=InfractionInfo(
            id=infraction.id,
            hubId=infraction.hubId,
            type=infraction.type.value,
            reason=infraction.reason,
            createdAt=infraction.createdAt.isoformat(),
            hub=HubInfo(id=hub.id, name=hub.name, iconUrl=hub.iconUrl)
            if hub
            else HubInfo(id=infraction.hubId, name='Unknown', iconUrl=''),
            user=UserInfo(
                id=infraction_user.id, name=infraction_user.name, image=infraction_user.image
            )
            if infraction_user
            else None,
            moderator=UserInfo(id=moderator.id, name=moderator.name, image=moderator.image)
            if moderator
            else UserInfo(id=infraction.moderatorId),
        ),
    )
