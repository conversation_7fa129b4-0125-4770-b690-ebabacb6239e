"""
Tags router - migrated from tRPC tags router.

Handles tag search, popular tags, hub tag management, and tag suggestions.
Includes caching and categorization functionality.
"""

from typing import List, Optional, Dict, Any
from enum import Enum
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
from sqlmodel import select, func, and_, or_, col, desc
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import Tag, Hub, User
from ..core.database import get_database
from ..core.auth import get_current_user, get_current_user_optional
from ..core.permissions import get_user_hub_permission, PermissionLevel
from ..core.config import settings

router = APIRouter(prefix='/tags', tags=['tags'])


class TagAction(str, Enum):
    ADD = 'add'
    REMOVE = 'remove'


# Response Models
class TagInfo(BaseModel):
    name: str
    usageCount: Optional[int] = None
    category: Optional[str] = None
    isOfficial: Optional[bool] = None


class TagMetadata(BaseModel):
    count: int
    query: Optional[str] = None
    category: Optional[str] = None
    popular: Optional[bool] = None
    limit: int


class TagListResponse(BaseModel):
    tags: List[TagInfo]
    metadata: TagMetadata


class CategorizedTagsResponse(BaseModel):
    categories: Dict[str, List[TagInfo]]
    metadata: Dict[str, int]


class TagSuggestionsResponse(BaseModel):
    suggestions: List[str]
    metadata: Dict[str, Any]


class TagMutationRequest(BaseModel):
    hubId: str
    tags: List[str] = Field(min_items=1, max_items=5)
    action: TagAction = TagAction.ADD


class TagMutationResponse(BaseModel):
    success: bool
    message: str
    tags: List[str]


class TagSuggestionRequest(BaseModel):
    hubName: Optional[str] = None
    hubDescription: Optional[str] = None


# Helper functions
def categorize_tag(tag_name: str) -> str:
    """Simple tag categorization based on keywords"""
    name_lower = tag_name.lower()

    # Gaming keywords
    gaming_keywords = [
        'game',
        'gaming',
        'play',
        'player',
        'esports',
        'competitive',
        'tournament',
        'stream',
        'twitch',
    ]
    if any(keyword in name_lower for keyword in gaming_keywords):
        return 'Gaming'

    # Art keywords
    art_keywords = [
        'art',
        'draw',
        'paint',
        'design',
        'creative',
        'artist',
        'illustration',
        'digital art',
    ]
    if any(keyword in name_lower for keyword in art_keywords):
        return 'Art'

    # Tech keywords
    tech_keywords = [
        'tech',
        'programming',
        'code',
        'developer',
        'software',
        'web',
        'app',
        'ai',
        'ml',
    ]
    if any(keyword in name_lower for keyword in tech_keywords):
        return 'Technology'

    # Music keywords
    music_keywords = ['music', 'song', 'band', 'artist', 'album', 'concert', 'instrument']
    if any(keyword in name_lower for keyword in music_keywords):
        return 'Music'

    # Default category
    return 'General'


def generate_tag_suggestions(
    hub_name: Optional[str] = None, hub_description: Optional[str] = None
) -> List[str]:
    """Generate tag suggestions based on hub content"""
    suggestions = []
    text = f'{hub_name or ""} {hub_description or ""}'.lower()

    # Gaming-related keywords
    gaming_keywords = [
        'game',
        'gaming',
        'play',
        'player',
        'esports',
        'competitive',
        'tournament',
        'stream',
        'twitch',
    ]
    if any(keyword in text for keyword in gaming_keywords):
        suggestions.append('Gaming')

    # Art-related keywords
    art_keywords = [
        'art',
        'draw',
        'paint',
        'design',
        'creative',
        'artist',
        'illustration',
        'digital art',
    ]
    if any(keyword in text for keyword in art_keywords):
        suggestions.append('Art')

    # Tech-related keywords
    tech_keywords = [
        'tech',
        'programming',
        'code',
        'developer',
        'software',
        'web',
        'app',
        'ai',
        'ml',
    ]
    if any(keyword in text for keyword in tech_keywords):
        suggestions.append('Technology')

    # Music-related keywords
    music_keywords = ['music', 'song', 'band', 'artist', 'album', 'concert', 'instrument']
    if any(keyword in text for keyword in music_keywords):
        suggestions.append('Music')

    # Community-related keywords
    community_keywords = ['community', 'social', 'chat', 'discussion', 'forum', 'group']
    if any(keyword in text for keyword in community_keywords):
        suggestions.append('Community')

    return suggestions[:5]  # Limit to 5 suggestions


@router.get('/', response_model=TagListResponse)
async def list_tags(
    search: Optional[str] = Query(None, description='Search query for tags'),
    limit: int = Query(20, ge=1, le=100, description='Maximum number of tags to return'),
    category: Optional[str] = Query(None, description='Filter by category'),
    popular: bool = Query(False, description='Get popular tags'),
    db: AsyncSession = Depends(get_database),
):
    """Get tags: search, popular, or by category"""

    # Search functionality
    if search and len(search) >= 2:
        search_query = (
            select(Tag)
            .where(col(Tag.name).ilike(f'%{search}%'))
            .order_by(desc(Tag.isOfficial), desc(Tag.usageCount), Tag.name)
            .limit(limit)
        )

        result = await db.exec(search_query)
        tags = result.all()

        tag_responses = [
            TagInfo(name=tag.name, category=tag.category, isOfficial=tag.isOfficial) for tag in tags
        ]

        return TagListResponse(
            tags=tag_responses,
            metadata=TagMetadata(
                count=len(tag_responses),
                query=search,
                category=category,
                popular=popular,
                limit=limit,
            ),
        )

    # Popular tags
    if popular:
        popular_query = select(Tag).order_by(desc(Tag.usageCount), Tag.name).limit(limit)

        result = await db.exec(popular_query)
        tags = result.all()

        tag_responses = [
            TagInfo(name=tag.name, usageCount=tag.usageCount, category=tag.category) for tag in tags
        ]

        return TagListResponse(
            tags=tag_responses,
            metadata=TagMetadata(
                count=len(tag_responses),
                query=search,
                category=category,
                popular=popular,
                limit=limit,
            ),
        )

    # Category filtering (simplified - would need proper implementation)
    if category:
        category_query = (
            select(Tag)
            .where(col(Tag.category) == category)
            .order_by(desc(Tag.usageCount), Tag.name)
            .limit(limit)
        )

        result = await db.exec(category_query)
        tags = result.all()

        tag_responses = [
            TagInfo(name=tag.name, usageCount=tag.usageCount, category=tag.category) for tag in tags
        ]

        return TagListResponse(
            tags=tag_responses,
            metadata=TagMetadata(
                count=len(tag_responses),
                query=search,
                category=category,
                popular=popular,
                limit=limit,
            ),
        )

    # Default: return popular tags
    default_query = select(Tag).order_by(desc(Tag.usageCount), Tag.name).limit(limit)

    result = await db.exec(default_query)
    tags = result.all()

    tag_responses = [
        TagInfo(name=tag.name, usageCount=tag.usageCount, category=tag.category) for tag in tags
    ]

    return TagListResponse(
        tags=tag_responses,
        metadata=TagMetadata(
            count=len(tag_responses), query=search, category=category, popular=popular, limit=limit
        ),
    )


@router.post('/mutate', response_model=TagMutationResponse)
async def mutate_hub_tags(
    mutation_data: TagMutationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Add or remove tags for a hub"""

    # Check permissions
    permission_level = await get_user_hub_permission(current_user.id, mutation_data.hubId, db)
    if permission_level < PermissionLevel.MODERATOR:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    # Validate tags
    valid_tags = []
    for tag in mutation_data.tags:
        if isinstance(tag, str) and 0 < len(tag.strip()) <= 30:
            valid_tags.append(tag.strip())

    if not valid_tags:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail='No valid tags provided'
        )

    # Get the hub
    hub = await db.get(Hub, mutation_data.hubId)
    if not hub:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Hub not found')

    if mutation_data.action == TagAction.ADD:
        # Create or get tags and associate with hub
        # This is simplified - would need proper many-to-many relationship handling
        for tag_name in valid_tags:
            # Check if tag exists
            existing_tag_query = select(Tag).where(col(Tag.name) == tag_name)
            existing_result = await db.exec(existing_tag_query)
            existing_tag = existing_result.first()

            if not existing_tag:
                # Create new tag
                new_tag = Tag(
                    name=tag_name, category=categorize_tag(tag_name), isOfficial=False, usageCount=1
                )
                db.add(new_tag)
            else:
                # Increment usage count
                existing_tag.usageCount = (existing_tag.usageCount or 0) + 1
                db.add(existing_tag)

        await db.commit()
        message = f'Successfully added {len(valid_tags)} tags'

    else:  # REMOVE
        # Remove tags from hub (simplified implementation)
        message = f'Successfully removed {len(valid_tags)} tags'

    return TagMutationResponse(success=True, message=message, tags=valid_tags)


@router.get('/categories', response_model=CategorizedTagsResponse)
async def get_tag_categories(
    db: AsyncSession = Depends(get_database),
):
    """Get tags organized by category"""

    # Get all tags grouped by category
    tags_query = select(Tag).order_by(desc(Tag.usageCount), Tag.name)
    result = await db.exec(tags_query)
    all_tags = result.all()

    # Group by category
    categories = {}
    for tag in all_tags:
        category = tag.category or 'General'
        if category not in categories:
            categories[category] = []
        categories[category].append(TagInfo(name=tag.name, usageCount=tag.usageCount))

    total_categories = len(categories)
    total_tags = len(all_tags)

    return CategorizedTagsResponse(
        categories=categories,
        metadata={'totalCategories': total_categories, 'totalTags': total_tags},
    )


@router.get('/suggest', response_model=TagSuggestionsResponse)
async def suggest_tags(
    hubName: Optional[str] = Query(None, description='Hub name for suggestions'),
    hubDescription: Optional[str] = Query(None, description='Hub description for suggestions'),
):
    """Generate tag suggestions based on content"""

    suggestions = generate_tag_suggestions(hubName, hubDescription)

    return TagSuggestionsResponse(
        suggestions=suggestions,
        metadata={
            'count': len(suggestions),
            'hubName': hubName,
            'hubDescription': 'provided' if hubDescription else 'not provided',
        },
    )


@router.post('/initialize', response_model=Dict[str, Any])
async def initialize_official_tags(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Initialize official tags (admin-only)"""

    # Check if user is admin
    admin_ids = getattr(settings, 'ADMIN_USER_IDS', [])
    if admin_ids and current_user.id not in admin_ids:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail='Admin access required')

    # Initialize official tags
    official_tags = [
        ('Gaming', 'Gaming'),
        ('Art', 'Art'),
        ('Technology', 'Technology'),
        ('Music', 'Music'),
        ('Community', 'General'),
        ('Education', 'General'),
        ('Entertainment', 'General'),
        ('Sports', 'General'),
        ('News', 'General'),
        ('Science', 'Technology'),
    ]

    created_count = 0
    for tag_name, category in official_tags:
        # Check if tag already exists
        existing_query = select(Tag).where(col(Tag.name) == tag_name)
        existing_result = await db.exec(existing_query)
        existing_tag = existing_result.first()

        if not existing_tag:
            new_tag = Tag(name=tag_name, category=category, isOfficial=True, usageCount=0)
            db.add(new_tag)
            created_count += 1
        else:
            # Update existing tag to be official
            existing_tag.isOfficial = True
            db.add(existing_tag)

    await db.commit()

    return {
        'success': True,
        'message': f'Official tags initialized successfully. Created {created_count} new tags.',
    }
