"""
User management router - migrated from tRPC user router.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import User
from ..core.database import get_database
from ..core.auth import get_current_user

router = APIRouter(prefix='/user', tags=['users'])


class UserProfileResponse(BaseModel):
    id: str
    name: Optional[str] = None
    image: Optional[str] = None
    badges: List[str] = []
    bio: Optional[str] = None
    createdAt: str


class UserProfileUpdate(BaseModel):
    bio: Optional[str] = None


@router.get('/me', response_model=UserProfileResponse)
async def get_current_user_profile(current_user: User = Depends(get_current_user)):
    """Get current user's profile"""
    return UserProfileResponse(
        id=current_user.id,
        name=current_user.name,
        image=current_user.image,
        badges=[badge.value for badge in current_user.badges] if current_user.badges else [],
        bio=None,  # No separate profile table
        createdAt=current_user.createdAt.isoformat() if current_user.createdAt else '',
    )


@router.get('/{user_id}', response_model=UserProfileResponse)
async def get_user_profile(user_id: str, db: AsyncSession = Depends(get_database)):
    """Get user profile by ID"""
    user = await db.get(User, user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='User not found')

    return UserProfileResponse(
        id=user.id,
        name=user.name,
        image=user.image,
        badges=[badge.value for badge in user.badges] if user.badges else [],
        bio=None,  # No separate profile table
        createdAt=user.createdAt.isoformat() if user.createdAt else '',
    )


@router.put('/profile', response_model=UserProfileResponse)
async def update_user_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_database),
):
    """Update current user's profile - simplified since no separate profile table"""

    # For now, just return the user info since there's no separate profile table
    # In a full implementation, you might want to add bio field to User model

    return UserProfileResponse(
        id=current_user.id,
        name=current_user.name,
        image=current_user.image,
        badges=[badge.value for badge in current_user.badges] if current_user.badges else [],
        bio=profile_data.bio,  # Return the bio from the request, but don't save it
        createdAt=current_user.createdAt.isoformat() if current_user.createdAt else '',
    )


@router.get('/search/{query}')
async def search_users(query: str, limit: int = 10, db: AsyncSession = Depends(get_database)):
    """Search users by username or global name"""
    if len(query) < 2:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Search query must be at least 2 characters',
        )

    # For now, just return a limited set of users
    # TODO: Implement proper search when we understand the SQLModel column syntax better
    search_query = select(User).limit(limit)

    result = await db.exec(search_query)
    users = result.all()

    return [{'id': user.id, 'name': user.name, 'image': user.image} for user in users]
