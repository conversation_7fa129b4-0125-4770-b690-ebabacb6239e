from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import Connection, Hub, User
from ..core.database import get_database
from ..core.auth import get_current_user

router = APIRouter(prefix='/connection', tags=['connections'])


class ConnectionResponse(BaseModel):
    id: str
    serverId: str
    channelId: str
    hubId: str
    connected: bool
    invite: Optional[str] = None
    createdAt: str
    lastActive: str


class ConnectionCreate(BaseModel):
    serverId: str
    channelId: str
    hubId: str
    invite: Optional[str] = None


class ConnectionUpdate(BaseModel):
    connected: Optional[bool] = None
    invite: Optional[str] = None
    channelId: Optional[str] = None


@router.get('/', response_model=List[ConnectionResponse])
async def list_connections(
    db: AsyncSession = Depends(get_database), current_user: User = Depends(get_current_user)
):
    """List user's connections"""
    # Get connections for hubs where user is owner or moderator
    # This is a simplified implementation - in production you'd need proper permission checking

    query = select(Connection).limit(20)  # Simple query for now
    result = await db.exec(query)
    connections = result.all()

    return [
        ConnectionResponse(
            id=conn.id,
            serverId=conn.serverId,
            channelId=conn.channelId,
            hubId=conn.hubId,
            connected=conn.connected,
            invite=conn.invite,
            createdAt=conn.createdAt.isoformat(),
            lastActive=conn.lastActive.isoformat()
            if conn.lastActive
            else conn.createdAt.isoformat(),
        )
        for conn in connections
    ]


@router.post('/', response_model=ConnectionResponse)
async def create_connection(
    connection_data: ConnectionCreate,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    """Create a new connection"""

    # Check if hub exists and user has permission
    hub = await db.get(Hub, connection_data.hubId)
    if not hub:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Hub not found')

    # Simple permission check - user must own the hub
    if hub.ownerId != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    # Check if connection already exists
    existing = await db.exec(
        select(Connection).where(
            Connection.serverId == connection_data.serverId,
            Connection.channelId == connection_data.channelId,
        )
    )
    if existing.first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail='Connection already exists'
        )

    # Create connection
    new_connection = Connection(
        serverId=connection_data.serverId,
        channelId=connection_data.channelId,
        hubId=connection_data.hubId,
        connected=True,
        invite=connection_data.invite,
    )

    db.add(new_connection)
    await db.commit()
    await db.refresh(new_connection)

    return ConnectionResponse(
        id=new_connection.id,
        serverId=new_connection.serverId,
        channelId=new_connection.channelId,
        hubId=new_connection.hubId,
        connected=new_connection.connected,
        invite=new_connection.invite,
        createdAt=new_connection.createdAt.isoformat(),
        lastActive=new_connection.lastActive.isoformat()
        if new_connection.lastActive
        else new_connection.createdAt.isoformat(),
    )


@router.put('/{connection_id}', response_model=ConnectionResponse)
async def update_connection(
    connection_id: str,
    connection_data: ConnectionUpdate,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    """Update connection"""

    connection = await db.get(Connection, connection_id)
    if not connection:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Connection not found')

    # Check permissions - simplified check
    hub = await db.get(Hub, connection.hubId)
    if not hub or hub.ownerId != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    # Update fields
    update_data = connection_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(connection, field, value)

    await db.commit()
    await db.refresh(connection)

    return ConnectionResponse(
        id=connection.id,
        serverId=connection.serverId,
        channelId=connection.channelId,
        hubId=connection.hubId,
        connected=connection.connected,
        invite=connection.invite,
        createdAt=connection.createdAt.isoformat(),
        lastActive=connection.lastActive.isoformat()
        if connection.lastActive
        else connection.createdAt.isoformat(),
    )


@router.delete('/{connection_id}')
async def delete_connection(
    connection_id: str,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    """Delete connection"""

    connection = await db.get(Connection, connection_id)
    if not connection:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Connection not found')

    # Check permissions
    hub = await db.get(Hub, connection.hubId)
    if not hub or hub.ownerId != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    await db.delete(connection)
    await db.commit()

    return {'message': 'Connection deleted successfully'}
