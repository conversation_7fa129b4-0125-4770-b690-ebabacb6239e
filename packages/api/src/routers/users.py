from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession
from datetime import datetime

from db.models import User
from ..core.database import get_database
from ..core.auth import get_current_user

router = APIRouter(prefix='/users', tags=['users'])

# User creation not allowed via API

class CreateUser(BaseModel):
    id: str
    name: str
    showBadges: bool
    image: Optional[str] = None
    locale: Optional[str] = None
    voteCount: int
    reputation: int
    lastVoted: datetime
    mentionOnReply: bool
    messageCount: int
    lastMessageAt: Optional[datetime] = None
    inboxLastReadDate: Optional[datetime] = None
    email: Optional[str] = None
    hubEngagementScore: int
    hubJoinCount: int
    lastHubJoinAt: Optional[datetime] = None
    preferredLanguages: Optional[List[str]] = None
    showNsfwHubs: bool
    badges: Dict[str, Any]
    emailVerified: Optional[bool] = None

class UserUpdate(BaseModel):
    name: Optional[str] = None
    showBadges: Optional[bool] = None
    image: Optional[str] = None
    locale: Optional[str] = None
    voteCount: Optional[int] = None
    reputation: Optional[int] = None
    lastVoted: Optional[datetime] = None
    mentionOnReply: Optional[bool] = None
    messageCount: Optional[int] = None
    lastMessageAt: Optional[datetime] = None
    inboxLastReadDate: Optional[datetime] = None
    email: Optional[str] = None
    hubEngagementScore: Optional[int] = None
    hubJoinCount: Optional[int] = None
    lastHubJoinAt: Optional[datetime] = None
    preferredLanguages: Optional[List[str]] = None
    showNsfwHubs: Optional[bool] = None
    badges: Optional[Dict[str, Any]] = None
    emailVerified: Optional[bool] = None

class UserResponse(BaseModel):
    id: str
    name: str
    showBadges: bool
    image: Optional[str] = None
    locale: Optional[str] = None
    voteCount: int
    reputation: int
    lastVoted: datetime
    mentionOnReply: bool
    messageCount: int
    lastMessageAt: Optional[datetime] = None
    inboxLastReadDate: Optional[datetime] = None
    createdAt: datetime
    updatedAt: datetime
    email: Optional[str] = None
    hubEngagementScore: int
    hubJoinCount: int
    lastHubJoinAt: Optional[datetime] = None
    preferredLanguages: Optional[List[str]] = None
    showNsfwHubs: bool
    badges: Dict[str, Any]
    emailVerified: Optional[bool] = None

# stops my poor filanges from typing this out every time
def _user_to_response(user: User) -> UserResponse:
    return UserResponse(
        id=user.id,
        name=user.name,
        showBadges=user.showBadges,
        image=user.image,
        locale=user.locale,
        voteCount=user.voteCount,
        reputation=user.reputation,
        lastVoted=user.lastVoted,
        mentionOnReply=user.mentionOnReply,
        messageCount=user.messageCount,
        lastMessageAt=user.lastMessageAt,
        inboxLastReadDate=user.inboxLastReadDate,
        createdAt=user.createdAt,
        updatedAt=user.updatedAt,
        email=user.email,
        hubEngagementScore=user.hubEngagementScore,
        hubJoinCount=user.hubJoinCount,
        lastHubJoinAt=user.lastHubJoinAt,
        preferredLanguages=user.preferredLanguages,
        showNsfwHubs=user.showNsfwHubs,
        badges=user.badges,
        emailVerified=user.emailVerified,
    )

@router.get('/me', response_model=UserResponse)
async def get_self(
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    result = await db.get(User, current_user.id)
    if not result:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='User not found')

    return _user_to_response(result)

@router.get('/{user_id}', response_model=UserResponse)
async def get_user(
    user_id: str,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    # TODO: Allow fetching other users if they have adequate permissions
    if current_user.id != user_id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail='Access denied')

    result = await db.get(User, user_id)
    if not result:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='User not found')

    return _user_to_response(result)

@router.put('/{user_id}', response_model=UserResponse)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    user = await db.get(User, user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='User not found')

    if user.id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
        )

    update_data = user_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)

    db.add(user)
    await db.commit()
    await db.refresh(user)

    return _user_to_response(user)

