"""
Hub management router - migrated from tRPC hub router.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from db.models import <PERSON>b, HubModeratorRole, User, HubModerator
from ..core.database import get_database
from ..core.auth import get_current_user

router = APIRouter(prefix='/hubs', tags=['hubs'])


class HubCreate(BaseModel):
    name: str
    description: str
    iconUrl: str
    shortDescription: Optional[str] = None
    private: bool = True
    nsfw: bool = False
    rules: List[str] = []


class HubUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    iconUrl: Optional[str] = None
    shortDescription: Optional[str] = None
    private: Optional[bool] = None
    nsfw: Optional[bool] = None
    rules: Optional[List[str]] = None
    bannerUrl: Optional[str] = None
    welcomeMessage: Optional[str] = None


class HubResponse(BaseModel):
    id: str
    name: str
    description: str
    ownerId: str
    iconUrl: str
    shortDescription: Optional[str] = None
    private: bool
    nsfw: bool
    verified: bool
    partnered: bool
    featured: bool
    rules: List[str]
    createdAt: str
    updatedAt: str


@router.post('/create', response_model=HubResponse)
async def create_hub(
    hub_data: HubCreate,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    """Create a new hub"""
    # Check if user already owns a hub with this name
    existing_hub = await db.exec(select(Hub).where(Hub.name == hub_data.name))
    if existing_hub.first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail='A hub with this name already exists'
        )

    # Create new hub
    new_hub = Hub(
        name=hub_data.name,
        description=hub_data.description,
        ownerId=current_user.id,
        iconUrl=hub_data.iconUrl,
        shortDescription=hub_data.shortDescription,
        private=hub_data.private,
        nsfw=hub_data.nsfw,
        rules=hub_data.rules,
    )

    db.add(new_hub)
    await db.commit()
    await db.refresh(new_hub)

    return HubResponse(
        id=new_hub.id,
        name=new_hub.name,
        description=new_hub.description,
        ownerId=new_hub.ownerId,
        iconUrl=new_hub.iconUrl,
        shortDescription=new_hub.shortDescription,
        private=new_hub.private,
        nsfw=new_hub.nsfw,
        verified=new_hub.verified,
        partnered=new_hub.partnered,
        featured=new_hub.featured,
        rules=new_hub.rules,
        createdAt=new_hub.createdAt.isoformat(),
        updatedAt=new_hub.updatedAt.isoformat(),
    )


@router.get('/{hub_id}', response_model=HubResponse)
async def get_hub(
    hub_id: str,
    db: AsyncSession = Depends(get_database),
    current_user: Optional[User] = Depends(get_current_user),
):
    """Get hub by ID"""
    hub = await db.get(Hub, hub_id)
    if not hub:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Hub not found')

    # Check if user can access private hub
    if hub.private and current_user and hub.ownerId != current_user.id:
        # Check if user is a moderator
        moderator = await db.exec(
            select(HubModerator).where(
                HubModerator.hubId == hub_id, HubModerator.userId == current_user.id
            )
        )
        if not moderator.first():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail='Access denied to private hub'
            )

    return HubResponse(
        id=hub.id,
        name=hub.name,
        description=hub.description,
        ownerId=hub.ownerId,
        iconUrl=hub.iconUrl,
        shortDescription=hub.shortDescription,
        private=hub.private,
        nsfw=hub.nsfw,
        verified=hub.verified,
        partnered=hub.partnered,
        featured=hub.featured,
        rules=hub.rules,
        createdAt=hub.createdAt.isoformat(),
        updatedAt=hub.updatedAt.isoformat(),
    )


@router.put('/{hub_id}', response_model=HubResponse)
async def update_hub(
    hub_id: str,
    hub_data: HubUpdate,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    """Update hub"""
    hub = await db.get(Hub, hub_id)
    if not hub:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Hub not found')

    # Check if user owns the hub or is a manager
    if hub.ownerId != current_user.id:
        moderator = await db.exec(
            select(HubModerator).where(
                HubModerator.hubId == hub_id,
                HubModerator.userId == current_user.id,
                HubModerator.role == HubModeratorRole.MANAGER,
            )
        )
        if not moderator.first():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail='Insufficient permissions'
            )

    # Update fields
    update_data = hub_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(hub, field, value)

    await db.commit()
    await db.refresh(hub)

    return HubResponse(
        id=hub.id,
        name=hub.name,
        description=hub.description,
        ownerId=hub.ownerId,
        iconUrl=hub.iconUrl,
        shortDescription=hub.shortDescription,
        private=hub.private,
        nsfw=hub.nsfw,
        verified=hub.verified,
        partnered=hub.partnered,
        featured=hub.featured,
        rules=hub.rules,
        createdAt=hub.createdAt.isoformat(),
        updatedAt=hub.updatedAt.isoformat(),
    )


@router.delete('/{hub_id}')
async def delete_hub(
    hub_id: str,
    db: AsyncSession = Depends(get_database),
    current_user: User = Depends(get_current_user),
):
    """Delete hub"""
    hub = await db.get(Hub, hub_id)
    if not hub:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Hub not found')

    # Only owner can delete hub
    if hub.ownerId != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail='Only hub owner can delete the hub'
        )

    await db.delete(hub)
    await db.commit()

    return {'message': 'Hub deleted successfully'}


@router.get('/', response_model=List[HubResponse])
async def list_hubs(
    skip: int = 0,
    limit: int = 20,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_database),
):
    """List public hubs"""
    query = select(Hub).where(not Hub.private)

    if search:
        query = query.where(col(Hub.name).ilike(f'%{search}%'))

    query = query.offset(skip).limit(limit)
    result = await db.exec(query)
    hubs = result.all()

    return [
        HubResponse(
            id=hub.id,
            name=hub.name,
            description=hub.description,
            ownerId=hub.ownerId,
            iconUrl=hub.iconUrl,
            shortDescription=hub.shortDescription,
            private=hub.private,
            nsfw=hub.nsfw,
            verified=hub.verified,
            partnered=hub.partnered,
            featured=hub.featured,
            rules=hub.rules,
            createdAt=hub.createdAt.isoformat(),
            updatedAt=hub.updatedAt.isoformat(),
        )
        for hub in hubs
    ]
