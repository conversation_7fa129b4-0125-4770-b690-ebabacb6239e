#!/usr/bin/env python3
"""
Test script for the InterChat FastAPI server.

This script tests basic functionality of the migrated API.
"""

import asyncio
import os
import sys

sys.path.append('/home/<USER>/Desktop/InterChat.py/packages/api/src')

from db.database import init_database
from core.config import Settings


async def test_setup():
    """Test the database setup and basic functionality."""

    # Initialize settings (for testing, we'll use placeholder values)
    os.environ.setdefault('DATABASE_URL', 'postgresql+asyncpg://user:pass@localhost/interchat_test')
    os.environ.setdefault('SECRET_KEY', 'test-secret-key-for-development')
    os.environ.setdefault('DISCORD_CLIENT_ID', 'test-client-id')
    os.environ.setdefault('DISCORD_CLIENT_SECRET', 'test-client-secret')

    settings = Settings()
    print(f'✅ Settings loaded')
    print(f'   DATABASE_URL: {settings.DATABASE_URL[:50]}...')
    print(f'   DEBUG: {settings.DEBUG}')

    try:
        # Try to initialize database
        db = init_database(settings.DATABASE_URL)
        print('✅ Database initialization completed')

        # Test database connection
        async with db.get_session() as session:
            print('✅ Database connection test successful')

    except Exception as e:
        print(f'❌ Database setup failed: {e}')
        return False

    print('✅ All basic tests passed!')
    return True


if __name__ == '__main__':
    print('🧪 Testing InterChat FastAPI setup...')
    success = asyncio.run(test_setup())
    sys.exit(0 if success else 1)
