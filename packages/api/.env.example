# FastAPI Hub Management System Environment Configuration

# Application Settings
APP_NAME="Hub Management API"
APP_VERSION="1.0.0"
DEBUG=true

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Database Configuration (PostgreSQL example)
DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/hub_db"

# Alternative: SQLite for development
# DATABASE_URL="sqlite+aiosqlite:///./hub_db.sqlite"

# JWT Settings
SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# Redis Configuration (for caching)
REDIS_URL="redis://localhost:6379"

# External Services
DICEBEAR_API_BASE="https://api.dicebear.com/7.x/shapes/svg"

# File Upload Settings
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_DIR="./uploads"

# Logging
LOG_LEVEL="INFO"
