[project]
name = "api"
version = "0.1.0"
description = "InterChat API server"
requires-python = ">=3.12"
dependencies = [
    "fastapi[standard]>=0.116.1",
    "pydantic>=2.11.7",
    "uvicorn[standard]>=0.35.0",
    "sqlalchemy>=2.0.0",
    "asyncpg>=0.29.0",
    "python-multipart>=0.0.9",
    "pydantic-settings>=2.1.0",
    "httpx>=0.27.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "db",
    "requests>=2.32.5",
]

[tool.uv.sources]
db = { workspace = true }

[tool.uv]
package = false
