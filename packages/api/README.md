# InterChat API

### 1. Install Dependencies

```bash
uv sync
```

### 2. Run the Development Server

```bash
uvicorn main_fastapi:app --reload --host 0.0.0.0 --port 8000
```

### 4. Access the API

-   **API Documentation**: http://localhost:8000/docs
-   **ReDoc**: http://localhost:8000/redoc
-   **OpenAPI JSON**: http://localhost:8000/openapi.json

## Permission Levels

-   **USER (0)**: Basic user
-   **MODERATOR (1)**: Can moderate content
-   **MANAGER (2)**: Can manage hub settings and members
-   **OWNER (3)**: Full control over hub
