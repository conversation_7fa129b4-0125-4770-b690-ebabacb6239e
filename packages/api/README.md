# InterChat FastAPI Migration

This package contains the FastAPI implementation of the InterChat API, migrated from the tRPC-based implementation in the website.

## Overview

The InterChat FastAPI server provides REST API endpoints for:

- **Hub Management** (`/api/hub/*`) - Create, update, delete, and list hubs
- **User Management** (`/api/user/*`) - User profiles, preferences, and search  
- **Connection Management** (`/api/connection/*`) - Discord server connections to hubs
- **Authentication** (`/api/auth/*`) - Discord OAuth authentication
- **Server Management** (`/api/server/*`) - Discord server operations (stub)
- **Moderation** (`/api/moderation/*`) - Hub moderation features (stub)
- **Appeals** (`/api/appeal/*`) - Infraction appeals system (stub)
- **Discovery** (`/api/discover/*`) - Hub discovery features (stub)
- **Tags** (`/api/tags/*`) - Hub tagging system (stub)

## Quick Start

### 1. Install Dependencies

```bash
uv sync
```

### 2. Configure Environment

Create `.env` file with:
```env
DATABASE_URL=postgresql+asyncpg://user:pass@localhost/interchat_db
SECRET_KEY=your-secret-key-here
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret
```

### 3. Run the Development Server

```bash
uvicorn main_fastapi:app --reload --host 0.0.0.0 --port 8000
```

### 4. Access the API

-   **API Documentation**: http://localhost:8000/docs
-   **ReDoc**: http://localhost:8000/redoc
-   **OpenAPI JSON**: http://localhost:8000/openapi.json

## Permission Levels

-   **USER (0)**: Basic user
-   **MODERATOR (1)**: Can moderate content
-   **MANAGER (2)**: Can manage hub settings and members
-   **OWNER (3)**: Full control over hub
