{
  "files.associations": {
    "*.py": "python"
  },
  "python.analysis.typeCheckingMode": "standard",
  "python.analysis.autoIndent": true,
  "python.analysis.autoImportCompletions": true,
  "python.analysis.diagnosticMode": "openFilesOnly",
  "python.analysis.useLibraryCodeForTypes": true,
  "python.analysis.logLevel": "Error",
  "python.terminal.useEnvFile": true,
  "python.analysis.diagnosticSeverityOverrides": {
    "reportMissingImports": "error",
    "reportMissingModuleSource": "error",
    // "reportImportCycles": "error",
    "reportCallIssue": "error",
    "reportOptionalMemberAccess": "warning",
    "reportOptionalSubscript": "warning",
    "reportOptionalIterable": "warning",
    "reportOptionalOperand": "warning",
    "reportOptionalContextManager": "warning",
    "reportUnboundVariable": "warning",
    "reportUnnecessaryComparison": "error",
    "reportUnnecessaryIsInstance": "warning",
    "reportUnnecessaryTypeIgnoreComment": "warning",
    "reportUnnecessaryTypeAlias": "warning",
    "reportUnnecessaryCast": "warning",
    "reportUnnecessaryLambda": "warning",
    "reportUnnecessaryAwait": "warning",
    "reportUnnecessaryReturn": "warning",
    "reportUnnecessaryRaise": "warning",
    "reportUnnecessaryPass": "warning",
    "reportUnnecessaryElse": "warning",
    "reportUnnecessaryFinally": "warning",
    "reportUnnecessaryContinue": "warning",
    "reportUnnecessaryBreak": "warning",
    "reportUnnecessaryImport": "error",
    "reportUnnecessaryFunction": "warning",
    "reportUnnecessaryClass": "warning",
    "reportUnnecessaryDecorator": "warning",
    "reportUnnecessaryModule": "warning",
    "reportUnnecessaryVariable": "warning",
    "reportUnnecessaryAttribute": "warning",
    "reportUnnecessarySubscript": "warning",
    "reportUnnecessaryIterable": "warning",
    "reportUnnecessaryOperand": "warning",
    "reportUnnecessaryContextManager": "warning",
    "reportUnnecessaryType": "warning",
    "reportUnnecessaryTypeParameter": "warning",
    "reportUnnecessaryTypeAliasDefinition": "warning",
    "reportUnnecessaryTypeDefinition": "warning",
    "reportUnnecessaryTypeCheck": "warning",
    "reportUnnecessaryTypeGuard": "warning",
    "reportUnnecessaryTypeVar": "warning",
    "reportUnnecessaryTypeVarTuple": "warning",
    "reportUnnecessaryTypeAliasImport": "warning",
    "reportUnnecessaryTypeAliasUsage": "warning",
    "reportUnnecessaryTypeAliasDefinitionImport": "warning",
    "reportUnnecessaryTypeAliasDefinitionUsage": "warning"
  },
  "editor.detectIndentation": true,
  "editor.formatOnSave": true,
  "editor.codeLens": true,
  "editor.suggestSelection": "first",
  "editor.fontLigatures": true,
  "python-envs.defaultEnvManager": "ms-python.python:venv",
  "python-envs.pythonProjects": [],
  "conventionalCommits.scopes": [
    "report"
  ]
}
